openapi: '3.0.2'
info:
    version: 1.0.0
    title: SGi Web Connect App
servers:
    - url: http://cwa-api-alb-576463729.ap-south-1.elb.amazonaws.com/api
    - url: https://api.sgilighting.com/api
    - url: http://************:3007/api
    - url: http://localhost:3007/api
tags:
    - name: Admin
      description: Operations related to user admin
    - name: Authentication
      description: Operations related to user authentication
    - name: Attributes
      description: Operations related to user attributes  
    - name: Brands
      description: Operations related to brands  
    - name: Categories
      description: Operations related to user authentication
    - name: Careers
      description: Operations related to Careers
    - name: Filters
      description: Operations related to Filters  
    - name: Home
      description: Home page related operations
    - name: Inspiration
      description: Home page related operations  
    - name: Page
      description: Static pages related operations
    - name: Products
      description: Products related operations  
    - name: Projects
      description: Projects related operations  
    - name: User
      description: User related operations
    - name: Utility
      description: Miscellaneous utility functions
    - name: Whats-New
      description: Miscellaneous Whats-New functions
paths:
    /admin/request-otp:
        post:
            summary: Request OTP
            operationId: requestOtp
            tags:
                - Admin
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - type
                                - email
                            properties:
                                type:
                                    type: string
                                    description: OTP type (e.g., SIGN_UP, FORGOT_PASSWORD, CHANGE_PHONE)
                                email:
                                    type: string
                                    description: Email of user
                                
            responses:
                200:
                    description: OTP sent successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /admin/verify-otp:
        post:
            summary: Verify OTP
            operationId: verifyOtp
            tags:
                - Admin
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - type
                                - email
                                - token
                            properties:
                                type:
                                    type: string
                                    description: OTP type (e.g., SIGN_UP, FORGOT_PASSWORD, CHANGE_PHONE)
                                email:
                                    type: string
                                    description: Email of user
                                token:
                                    type: string
                                    deszcription: OTP received on mobile
            responses:
                200:
                    description: OTP verified successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: object
                400:
                    $ref: '#/components/responses/BadRequestError'

    /admin/change-password:
        post:
            summary: reset user password
            operationId: resetPassword
            tags:
                - Admin
            security:
                - ApiKeyAuth: []   
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            currentPassword: 'Qwerty12#'
                            newPassword: 'Qwerty123$'
                            confirmPassword: 'Qwerty123$'
                        schema:
                            type: object
                            required:
                                - currentPassword
                                - newPassword
                                - confirmPassword
                            properties:
                                currentPassword:
                                    type: string
                                    format: password
                                    description: Old Password of user
                                newPassword:
                                    type: string
                                    format: password
                                    description: New Password of user
                                confirmPassword:
                                    type: string
                                    format: password
                                    description: New Password of user
            responses:
                200:
                    description: Password changed successfully message
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                400:
                    $ref: '#/components/responses/BadRequestError'
    /admin/change-subadmin-password:
        put:
            summary: reset user password
            operationId: resetPasswordchange
            tags:
                - Admin
            security:
                - ApiKeyAuth: []   
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            subadminId: '66b2fe867d46c5ae33ca6389'
                            newPassword: 'Qwerty123$'
                            confirmPassword: 'Qwerty123$'
                        schema:
                            type: object
                            required:
                                - subadminId
                                - newPassword
                                - confirmPassword
                            properties:
                                subadminId:
                                    type: string
                                    description: Id of sub admin
                                newPassword:
                                    type: string
                                    format: password
                                    description: New Password of user
                                confirmPassword:
                                    type: string
                                    format: password
                                    description: New Password of user
            responses:
                200:
                    description: Password changed successfully message
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                400:
                    $ref: '#/components/responses/BadRequestError'
    /admin/test:
        get:
            summary: Logout user
            operationId: logOutUser
            tags:
                - Authentication
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: hostname
                  in: query
                  schema:
                      type: string
            responses:
                200:
                    description: Success message indicating a user is logged out successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /admin/log-in:
        post:
            summary: log in into admin app
            operationId: adminLogIn
            tags:
                - Admin
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            email: '<EMAIL>'
                            password: 'Qwerty12#'
                        schema:
                            type: object
                            required:
                                - email
                                - password
                            properties:
                                email:
                                    type: string
                                    description: email of register admin
                                password:
                                    type: string
                                    format: password
                                    description: Password of registered user
            responses:
                200:
                    description: User profile and auth token
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                properties:
                                    data:
                                        type: object
                                        properties:
                                            token:
                                                type: string
                                            user:
                                                $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /admin/delete-row:
        delete:
            summary: Delete any row from any model
            operationId: deleteRow
            tags:
                - Admin
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                200:
                    description: Message indicating row deleted successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                400:
                    $ref: '#/components/responses/BadRequestError'
    
    /admin/toggle-active-inactive:
        put:
          summary: Toggle active/inactive status for any model
          operationId: toggleActiveInactive
          tags:
            - Admin
          security:
            - ApiKeyAuth: []
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
          requestBody:
            description: Request body to toggle the active/inactive status of a model.
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    modelName:
                      type: string
                      description: Name of the model to update.
                      example: Admin,AdminSettings, User, Category,BrandPopUpSGi, BrandPopUpGENIE, BrandPopUpEfficacy,Home, PopupMenu, HomeIntroducing, HomeInspiration, HomeProjects, Footer, Career, Page, Resource, Legal, Contact, Company, WhatsNew,Product
                    modelId:
                      type: string
                      description: ID of the model instance to update.
                      example: 60d9c6b8f1e7b2c8a23d2e4b
                    status:
                      type: boolean
                      description: The status to set for the `isActive` field.
                      example: true
          responses:
            200:
              description: Status toggled successfully
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            400:
              $ref: '#/components/responses/BadRequestError'
            404:
              description: Model or record not found
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            500:
              description: Internal server error
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
                
    /admin/profile:
        get:
            summary: Get Admin Profile
            operationId: getAdminProfile
            tags:
                - Admin
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                200:
                    description: Success response with the admin profile
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    firstName:
                                        type: string
                                        description: First name of the admin.
                                    lastName:
                                        type: string
                                        description: Last name of the admin.
                                    email:
                                        type: string
                                        description: Email of the admin.
                                    countryCode:
                                        type: string
                                        description: Country code for the contact number.
                                    contactNumber:
                                        type: string
                                        description: Contact number of the admin.
                '400':
                    $ref: '#/components/responses/BadRequestError'
        
        put:
          summary: Update Admin Profile
          operationId: updateAdminProfile
          tags:
              - Admin
          security:
              - ApiKeyAuth: []
          parameters:
              - $ref: '#/components/parameters/headerLanguage'
              - $ref: '#/components/parameters/headerPlatform'
              - $ref: '#/components/parameters/headerVersion'
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          type: object
                          properties:
                              firstName:
                                  type: string
                                  description: Admin's first name
                                  example: John
                              lastName:
                                  type: string
                                  description: Admin's last name
                                  example: Doe
                              email:
                                  type: string
                                  description: Admin's email address
                                  example: <EMAIL>
                              countryCode:
                                  type: string
                                  description: Country code for the contact number
                                  example: '+1'
                              contactNumber:
                                  type: string
                                  description: Admin's contact number
                                  example: '1234567890'
                          required:
                              - firstName
                              - lastName
                              - email
          responses:
              '200':
                  description: Admin profile updated successfully
                  content:
                      application/json:
                          schema:
                              allOf:
                                  - $ref: '#/components/schemas/ApiResponse'
                                  - type: object
                                    properties:
                                        data:
                                            $ref: '#/components/schemas/Admin'
              '400':
                  $ref: '#/components/responses/BadRequestError'
              '404':
                  $ref: '#/components/responses/NotFoundError'
              '500':
                  $ref: '#/components/responses/ServerError'            
      
    /admin/sub-admins:
      get:
          summary: Get list of sub-admins
          operationId: getSubAdminList
          tags:
              - Admin
          security:
              - ApiKeyAuth: []   
          parameters:
              - $ref: '#/components/parameters/headerLanguage'
              - $ref: '#/components/parameters/headerPlatform'
              - $ref: '#/components/parameters/headerVersion'
          responses:
              200:
                  description: List of sub-admins retrieved successfully
                  content:
                      application/json:
                          schema:
                              allOf:
                                  - $ref: '#/components/schemas/ApiResponse'
              400:
                  $ref: '#/components/responses/BadRequestError'
  
      post:
          summary: Create a new sub-admin
          operationId: createSubAdmin
          tags:
              - Admin
          security:
              - ApiKeyAuth: []   
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'    
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          type: object
                          required:
                              - firstName
                              - lastName
                              - email
                              - password
                              - countryCode
                              - contactNumber
                          properties:
                              firstName:
                                  type: string
                                  description: First name of the sub-admin
                              lastName:
                                  type: string
                                  description: Last name of the sub-admin
                              email:
                                  type: string
                                  format: email
                                  description: Email of the sub-admin
                              password:
                                  type: string
                                  format: password
                                  description: Password for the sub-admin account
                              countryCode:
                                  type: string
                                  description: Country code of the sub-admin
                              contactNumber:
                                  type: string
                                  description: Contact number of the sub-admin
          responses:
              201:
                  description: Sub-admin created successfully
                  content:
                      application/json:
                          schema:
                              allOf:
                                  - $ref: '#/components/schemas/ApiResponse'
              400:
                  $ref: '#/components/responses/BadRequestError' 
                  
    /admin/sub-admins/{id}:
        put:
            summary: Update sub-admin information
            operationId: updateSubAdmin
            tags:
                - Admin
            security:
                - ApiKeyAuth: []   
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the sub-admin to update
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                firstName:
                                    type: string
                                    description: First name of the sub-admin
                                lastName:
                                    type: string
                                    description: Last name of the sub-admin
                                email:
                                    type: string
                                    format: email
                                    description: Email of the sub-admin
                                password:
                                    type: string
                                    format: password
                                    description: Password for the sub-admin account
                                countryCode:
                                    type: string
                                    description: Country code of the sub-admin
                                contactNumber:
                                    type: string
                                    description: Contact number of the sub-admin
            responses:
                200:
                    description: Sub-admin updated successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                400:
                    $ref: '#/components/responses/BadRequestError' 
                    
    /auth/request-otp:
        post:
            summary: Request OTP
            operationId: requestOtp
            tags:
                - Authentication
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - type
                                - countryCode
                                - phone
                            properties:
                                type:
                                    type: string
                                    description: OTP type (e.g., SIGN_UP, FORGOT_PASSWORD, CHANGE_PHONE)
                                countryCode:
                                    type: string
                                    description: Country code of user
                                phone:
                                    type: string
                                    description: Phone number of user
            responses:
                200:
                    description: OTP sent successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /auth/verify-otp:
        post:
            summary: Verify OTP
            operationId: verifyOtp
            tags:
                - Authentication
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - type
                                - countryCode
                                - phone
                                - token
                            properties:
                                type:
                                    type: string
                                    description: OTP type (e.g., SIGN_UP, FORGOT_PASSWORD, CHANGE_PHONE)
                                countryCode:
                                    type: string
                                    description: Country code of user
                                phone:
                                    type: string
                                    description: Phone number of user
                                token:
                                    type: string
                                    description: OTP received on mobile
            responses:
                200:
                    description: OTP verified successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: object
                400:
                    $ref: '#/components/responses/BadRequestError'

    /auth/sign-up:
        post:
            summary: Sign up into app
            operationId: signUp
            tags:
                - Authentication
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - fullName
                                - age
                                - countryCode
                                - phone
                                - password
                            properties:
                                fullName:
                                    type: string
                                    description: Full name of user
                                age:
                                    type: string
                                    description: Age of user
                                email:
                                    type: string
                                    format: email
                                    description: Email address of user
                                countryCode:
                                    type: string
                                    description: Country code of user
                                phone:
                                    type: string
                                    description: Phone number of user
                                password:
                                    type: string
                                    format: password
                                    description: Password of user
                                deviceToken:
                                    type: string
                                    description: Firebase device token
            responses:
                200:
                    description: User profile and auth token
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: object
                                              properties:
                                                  token:
                                                      type: string
                                                  user:
                                                      $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /auth/log-in:
        post:
            summary: Log in into app
            operationId: logIn
            tags:
                - Authentication
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - countryCode
                                - phone
                                - password
                            properties:
                                countryCode:
                                    type: string
                                    description: Country code of registered user
                                phone:
                                    type: string
                                    description: Phone number of registered user
                                password:
                                    type: string
                                    format: password
                                    description: Password of registered user
                                deviceToken:
                                    type: string
                                    description: Firebase device token
            responses:
                200:
                    description: User profile and auth token
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: object
                                              properties:
                                                  token:
                                                      type: string
                                                  user:
                                                      $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /auth/log-out:
        get:
            summary: logout user
            operationId: Auth
            tags:
                - Authentication
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                200:
                    description: Success message indicating a user is logged out successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /auth/reset-password:
        post:
            summary: reset user password
            operationId: resetPasswordchnage
            tags:
                - Authentication
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            countryCode: '+1'
                            phone: '0001112223'
                            password: 'asdf@1234'
                        schema:
                            type: object
                            required:
                                - countryCode
                                - phone
                                - password
                            properties:
                                countryCode:
                                    type: string
                                    description: Country code of user
                                phone:
                                    type: string
                                    description: Phone of user
                                password:
                                    type: string
                                    format: password
                                    description: Password of user
            responses:
                200:
                    description: Password changed successfully message
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                400:
                    $ref: '#/components/responses/BadRequestError'
                    
    /attributes/antiglare:
      get:
        summary: Get all anti-glare filters or a specific anti-glare filter by ID
        operationId: listAntiGlareFilters
        tags:
          - Attributes
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the anti-glare filter to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the anti-glare filters
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No anti-glare filters found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      post:
        summary: Add a new anti-glare filter
        operationId: addAntiGlareFilter
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the anti-glare filter to be added
                  modelSlug:
                    type: string
                    description: The model slug for the anti-glare filter
                required:
                  - name
        responses:
          '200':
            description: Anti-glare filter added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Filter name is required or filter already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'                

    /attributes/antiglare/{id}:
      put:
        summary: Edit an existing anti-glare filter
        operationId: editAntiGlareFilter
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the anti-glare filter to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The new name of the anti-glare filter
                  modelSlug:
                    type: string
                    description: The new model slug for the anti-glare filter
                  isActive:
                    type: boolean
                    description: Whether the anti-glare filter is active
                required:
                  - name
        responses:
          '200':
            description: Anti-glare filter updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Anti-glare filter not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      delete:
        summary: Delete an existing anti-glare filter (soft delete)
        operationId: deleteAntiGlareFilter
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the anti-glare filter to delete
            schema:
              type: string
        responses:
          '200':
            description: Anti-glare filter deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Anti-glare filter not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /attributes/beam-angle:
      get:
        summary: Get all beam angle attributes or a specific beam angle attribute by ID
        operationId: listBeamAngleAttribute
        tags:
          - Attributes
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the beam angle attribute to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the beam angle attributes
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No beam angle attributes found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      post:
        summary: Add a new beam angle attribute
        operationId: addBeamAngleAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the beam angle attribute to be added
                  modelSlug:
                    type: string
                    description: Model slug for the beam angle attribute (default ba)
                required:
                  - name
        responses:
          '200':
            description: Beam angle attribute added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Beam angle name is required or attribute already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /attributes/beam-angle/{id}:
      put:
        summary: Edit an existing beam angle attribute
        operationId: editBeamAngleAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the beam angle attribute to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The new name of the beam angle attribute
                  modelSlug:
                    type: string
                    description: The model slug for the beam angle attribute
                  isActive:
                    type: boolean
                    description: Active status of the beam angle attribute
                required:
                  - name
        responses:
          '200':
            description: Beam angle attribute updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Beam angle attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      delete:
        summary: Delete an existing beam angle attribute (soft delete)
        operationId: deleteBeamAngleAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the beam angle attribute to delete
            schema:
              type: string
        responses:
          '200':
            description: Beam angle attribute deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Beam angle attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /attributes/bend:
      get:
        summary: Get all bend attributes or a specific bend attribute by ID
        operationId: listBendAttribute
        tags:
          - Attributes
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the bend attribute to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the bend attributes
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No bend attributes found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
  
      post:
        summary: Add a new bend attribute
        operationId: addBendAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the bend attribute to be added
                  modelSlug:
                    type: string
                    description: The model slug of the bend attribute
                required:
                  - name
        responses:
          '200':
            description: Bend attribute added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Attribute name is required or the attribute already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /attributes/bend/{id}:
      put:
        summary: Edit an existing bend attribute
        operationId: editBendAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the bend attribute to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The new name of the bend attribute
                  modelSlug:
                    type: string
                    description: The new model slug of the bend attribute
                  isActive:
                    type: boolean
                    description: The active status of the bend attribute
                required:
                  - name
        responses:
          '200':
            description: Bend attribute updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Bend attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
  
      delete:
        summary: Delete an existing bend attribute (soft delete)
        operationId: deleteBendAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the bend attribute to delete
            schema:
              type: string
        responses:
          '200':
            description: Bend attribute deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Bend attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /attributes/connection-cable:
      get:
        summary: Get all connection cable attributes or a specific attribute by ID
        operationId: listConnectionCableAttribute
        tags:
          - Attributes
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the connection cable attribute to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the connection cable attributes
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No attributes found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
        
      post:
        summary: Add a new connection cable attribute
        operationId: addConnectionCableAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the connection cable attribute to be added
                  modelSlug:
                    type: string
                    description: The model slug for the attribute (default 'concab')
                required:
                  - name
        responses:
          '200':
            description: Connection cable attribute added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Attribute name is required or attribute already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
            
    /attributes/connection-cable/{id}:
      put:
        summary: Edit an existing connection cable attribute
        operationId: editConnectionCableAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the connection cable attribute to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The new name of the connection cable attribute
                  modelSlug:
                    type: string
                    description: The new model slug (default 'drv')
                  isActive:
                    type: boolean
                    description: Active status of the attribute
                required:
                  - name
        responses:
          '200':
            description: Connection cable attribute updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
      
      delete:
        summary: Delete an existing connection cable attribute (soft delete)
        operationId: deleteConnectionCableAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the connection cable attribute to delete
            schema:
              type: string
        responses:
          '200':
            description: Connection cable attribute deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'        
    
    /attributes/driver:
      get:
        summary: Get all driver attributes or a specific driver attribute by ID
        operationId: listDriverAttribute
        tags:
          - Attributes
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the driver attribute to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the driver attributes
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No driver attributes found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      post:
        summary: Add a new driver attribute
        operationId: addDriverAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the driver attribute to be added
                  modelSlug:
                    type: string
                    description: The model slug of the driver attribute
                required:
                  - name
        responses:
          '200':
            description: Driver attribute added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Attribute name is required or the attribute already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
            
    /attributes/driver/{id}:
      put:
        summary: Edit an existing driver attribute
        operationId: editDriverAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the driver attribute to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The new name of the driver attribute
                  modelSlug:
                    type: string
                    description: The new model slug of the driver attribute
                  isActive:
                    type: boolean
                    description: The active status of the driver attribute
                required:
                  - name
        responses:
          '200':
            description: Driver attribute updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Driver attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      delete:
        summary: Delete an existing driver attribute (soft delete)
        operationId: deleteDriverAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the driver attribute to delete
            schema:
              type: string
        responses:
          '200':
            description: Driver attribute deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Driver attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'        
    
    /attributes/finish:
      get:
        summary: Get all finish attributes or a specific finish attribute by ID
        operationId: listFinishAttribute
        tags:
          - Attributes
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the finish attribute to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the finish attributes
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No finish attributes found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      post:
        summary: Add a new finish attribute
        operationId: addFinishAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the finish attribute to be added
                  modelSlug:
                    type: string
                    description: The model slug of the finish attribute
                required:
                  - name
        responses:
          '200':
            description: Finish attribute added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Attribute name is required or the attribute already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
            
    /attributes/finish/{id}:
      put:
        summary: Edit an existing finish attribute
        operationId: editFinishAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the finish attribute to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The new name of the finish attribute
                  modelSlug:
                    type: string
                    description: The new model slug of the finish attribute
                  isActive:
                    type: boolean
                    description: The active status of the finish attribute
                required:
                  - name
        responses:
          '200':
            description: Finish attribute updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Finish attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      delete:
        summary: Delete an existing finish attribute (soft delete)
        operationId: deleteFinishAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the finish attribute to delete
            schema:
              type: string
        responses:
          '200':
            description: Finish attribute deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Finish attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /attributes/lens:
      get:
        summary: Get all lens attributes or a specific lens attribute by ID
        operationId: listLensAttribute
        tags:
          - Attributes
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the lens attribute to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the lens attributes
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No lens attributes found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      post:
        summary: Add a new lens attribute
        operationId: addLensAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the lens attribute to be added
                  modelSlug:
                    type: string
                    description: The model slug of the lens attribute
                required:
                  - name
        responses:
          '200':
            description: Lens attribute added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Attribute name is required or the attribute already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /attributes/lens/{id}:
      put:
        summary: Edit an existing lens attribute
        operationId: editLensAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the lens attribute to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The new name of the lens attribute
                  modelSlug:
                    type: string
                    description: The new model slug of the lens attribute
                  isActive:
                    type: boolean
                    description: The active status of the lens attribute
                required:
                  - name
        responses:
          '200':
            description: Lens attribute updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Lens attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      delete:
        summary: Delete an existing lens attribute (soft delete)
        operationId: deleteLensAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the lens attribute to delete
            schema:
              type: string
        responses:
          '200':
            description: Lens attribute deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Lens attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /attributes/light-colour:
      get:
        summary: Get all light colour attributes or a specific light colour attribute by ID
        operationId: listLightColourAttribute
        tags:
          - Attributes
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the light colour attribute to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the light colour attributes
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No light colour attributes found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      post:
        summary: Add a new light colour attribute
        operationId: addLightColourAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the light colour attribute to be added
                  modelSlug:
                    type: string
                    description: The model slug of the light colour attribute
                required:
                  - name
        responses:
          '200':
            description: Light colour attribute added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Attribute name is required or the attribute already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'

    /attributes/light-colour/{id}:
      put:
        summary: Edit an existing light colour attribute
        operationId: editLightColourAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the light colour attribute to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The new name of the light colour attribute
                  modelSlug:
                    type: string
                    description: The new model slug of the light colour attribute
                  isActive:
                    type: boolean
                    description: The active status of the light colour attribute
                required:
                  - name
        responses:
          '200':
            description: Light colour attribute updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Light colour attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      delete:
        summary: Delete an existing light colour attribute (soft delete)
        operationId: deleteLightColourAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the light colour attribute to delete
            schema:
              type: string
        responses:
          '200':
            description: Light colour attribute deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Light colour attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /attributes/suspension:
      get:
        summary: Get all suspension attributes or a specific suspension attribute by ID
        operationId: listSuspensionAttribute
        tags:
          - Attributes
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the suspension attribute to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the suspension attributes
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No suspension attributes found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      post:
        summary: Add a new suspension attribute
        operationId: addSuspensionAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the suspension attribute to be added
                  modelSlug:
                    type: string
                    description: The model slug of the suspension attribute
                required:
                  - name
        responses:
          '200':
            description: Suspension attribute added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Attribute name is required or the attribute already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /attributes/suspension/{id}:
      put:
        summary: Edit an existing suspension attribute
        operationId: editSuspensionAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the suspension attribute to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The new name of the suspension attribute
                  modelSlug:
                    type: string
                    description: The new model slug of the suspension attribute
                  isActive:
                    type: boolean
                    description: The active status of the suspension attribute
                required:
                  - name
        responses:
          '200':
            description: Suspension attribute updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Suspension attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      delete:
        summary: Delete an existing suspension attribute (soft delete)
        operationId: deleteSuspensionAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the suspension attribute to delete
            schema:
              type: string
        responses:
          '200':
            description: Suspension attribute deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Suspension attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /attributes/trim:
      get:
        summary: Get all trim attributes or a specific trim attribute by ID
        operationId: listTrimAttribute
        tags:
          - Attributes
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the trim attribute to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the trim attributes
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No trim attributes found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      post:
        summary: Add a new trim attribute
        operationId: addTrimAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the trim attribute to be added
                  modelSlug:
                    type: string
                    description: The model slug of the trim attribute
                required:
                  - name
        responses:
          '200':
            description: Trim attribute added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Attribute name is required or the attribute already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /attributes/trim/{id}:
      put:
        summary: Edit an existing trim attribute
        operationId: editTrimAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the trim attribute to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The new name of the trim attribute
                  modelSlug:
                    type: string
                    description: The new model slug of the trim attribute
                  isActive:
                    type: boolean
                    description: The active status of the trim attribute
                required:
                  - name
        responses:
          '200':
            description: Trim attribute updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Trim attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      delete:
        summary: Delete an existing trim attribute (soft delete)
        operationId: deleteTrimAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the trim attribute to delete
            schema:
              type: string
        responses:
          '200':
            description: Trim attribute deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Trim attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /attributes/watts:
      get:
        summary: Get all watts attributes or a specific watts attribute by ID
        operationId: listWattsAttribute
        tags:
          - Attributes
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the watts attribute to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the watts attributes
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No watts attributes found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      post:
        summary: Add a new watts attribute
        operationId: addWattsAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the watts attribute to be added
                  modelSlug:
                    type: string
                    description: The model slug of the watts attribute
                required:
                  - name
        responses:
          '200':
            description: Watts attribute added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Attribute name is required or the attribute already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /attributes/watts/{id}:
      put:
        summary: Edit an existing watts attribute
        operationId: editWattsAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the watts attribute to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The new name of the watts attribute
                  modelSlug:
                    type: string
                    description: The new model slug of the watts attribute
                  isActive:
                    type: boolean
                    description: The active status of the watts attribute
                required:
                  - name
        responses:
          '200':
            description: Watts attribute updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Watts attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      delete:
        summary: Delete an existing watts attribute (soft delete)
        operationId: deleteWattsAttribute
        tags:
          - Attributes
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the watts attribute to delete
            schema:
              type: string
        responses:
          '200':
            description: Watts attribute deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Watts attribute not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /brands/sgi-banner:
        post:
            summary: Add or Update SGi Brand Banners
            operationId: sgiBrandAddHeroSection
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                banners:
                                    type: array
                                    description: Array of banners to add or update
                                    items:
                                        type: object
                                        properties:
                                            banner:
                                                type: string
                                                description: URL of the banner image or video
                                            mediaType:
                                                type: string
                                                enum: ['image', 'video']
                                                description: Type of media (image or video)
                                            bannerMobi:
                                                type: string
                                                description: URL of the banner image or video
                                            mediaTypeMobi:
                                                type: string
                                                enum: ['image', 'video']
                                                description: Type of media (image or video)    
                                            textOne:
                                                type: string
                                                description: First text overlay on the banner
                                            textTwo:
                                                type: string
                                                description: Second text overlay on the banner
                                            link:
                                                type: string
                                                description: URL link associated with the banner
                                            isTextVisible:
                                                type: boolean
                                                description: Whether the text is visible on the banner
                                            isActive:
                                                type: boolean
                                                description: Whether the banner is active
                                            sortOrder:
                                                type: number
                                                description: Sort order for the banner
                            required:
                                - banners
            responses:
                '200':
                    description: Banners added/updated successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
    
        get:
            summary: Get SGi Brand Banners
            operationId: sgiBrandViewBannerSection
            tags:
                - Brands
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: query
                  name: isActive
                  schema:
                    type: string
                    enum:
                        - 'true'
                        - 'false'
                  required: false
            responses:
                '200':
                    description: Success response with the SGi brand banners data
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Banners not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
                    
    /brands/sgi-banner/{id}:
        delete:
            summary: Delete an SGi Brand Banner
            operationId: sgiBrandDeleteBannerSection
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: id
                  in: path
                  required: true
                  schema:
                      type: string
                  description: ID of the banner to delete
            responses:
                '200':
                    description: Banner deleted successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Banner not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'                
         
    /brands/sgi-value-image:
        get:
            summary: Get SGi Value Image
            operationId: sgiBrandViewValueImage
            tags:
                - Brands
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Success response with the SGi brand value image data
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Banners not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
        put:
            summary: Add or Update SGi Brand Value Image
            operationId: sgiBrandAddOrUpdateValueImage
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                BrandValueSGi:
                                    type: string
                                    description: Image for brand value section
                                BrandValueSGiMobi:
                                    type: string
                                    description: Mobile Image for brand value section    
                            required:
                                - BrandValueSGi
            responses:
                '200':
                    description: Banners added/updated successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
    
            
                    
    /brands/sgi-product-tile:
        get:
            summary: View all SGi Brand Product Tiles
            operationId: sgiViewProductTiles
            tags:
                - Brands
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'    
                - in: query
                  name: isActive
                  schema:
                    type: string
                    enum:
                        - 'all'
                        - 'false'
                        - 'false'
                  required: false
            responses:
                '200':
                    description: Success response with the SGi brand product tiles data
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Tiles not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'   
                    
        post:
            summary: Add a SGi Brand Product Tile
            operationId: sgiAddProductTile
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'    
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                TileImage:
                                    type: string
                                    description: URL of the tile image
                                TileHoverImage:
                                    type: string
                                    description: URL of the tile hover image
                                isActive:
                                    type: boolean
                                    description: Whether the tile is active
                                sortOrder:
                                    type: number
                                    description: Sort order of the tile
                            required:
                                    - TileImage
                                    - TileHoverImage
            responses:
                '200':
                    description: Tile added successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'            
                    
    /brands/sgi-product-tile/{id}:
        put:
          summary: Edit a SGi Brand Product Tile
          operationId: sgiEditProductTile
          tags:
              - Brands
          security:
              - ApiKeyAuth: []
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the product tile to delete    
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          type: object
                          properties:
                              TileImage:
                                  type: string
                                  description: URL of the tile image
                              TileHoverImage:
                                  type: string
                                  description: URL of the tile hover image
                              isActive:
                                  type: boolean
                                  description: Whether the tile is active
                              sortOrder:
                                  type: number
                                  description: Sort order of the tile
                          required:
                              - TileHoverImage
                              - TileImage
          responses:
              '200':
                  description: Tile updated successfully
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '404':
                  description: Tile not found
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '500':
                  $ref: '#/components/responses/ServerError'  
    
        delete:
            summary: Delete a SGi Brand Product Tile
            operationId: sgiDeleteProductTile
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the product tile to delete
            responses:
                '200':
                    description: Tile deleted successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Tile not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'                
                    
    /brands/genie-banner:
        post:
            summary: Add or Update GENIE Brand Banners
            operationId: genieBrandAddHeroSection
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                banners:
                                    type: array
                                    description: Array of banners to add or update
                                    items:
                                        type: object
                                        properties:
                                            banner:
                                                type: string
                                                description: URL of the banner image or video
                                            mediaType:
                                                type: string
                                                enum: ['image', 'video']
                                                description: Type of media (image or video)
                                            bannerMobi:
                                                type: string
                                                description: URL of the banner image or video
                                            mediaTypeMobi:
                                                type: string
                                                enum: ['image', 'video']
                                                description: Type of media (image or video)    
                                            textOne:
                                                type: string
                                                description: First text overlay on the banner
                                            textTwo:
                                                type: string
                                                description: Second text overlay on the banner
                                            link:
                                                type: string
                                                description: URL link associated with the banner
                                            isTextVisible:
                                                type: boolean
                                                description: Whether the text is visible on the banner
                                            isActive:
                                                type: boolean
                                                description: Whether the banner is active
                                            sortOrder:
                                                type: number
                                                description: Sort order for the banner
                            required:
                                - banners
            responses:
                '200':
                    description: Banners added/updated successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
    
        get:
            summary: Get GENIE Brand Banners
            operationId: genieBrandViewBannerSection
            tags:
                - Brands
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: query
                  name: isActive
                  schema:
                    type: string
                    enum:
                        - 'true'
                        - 'false'
                  required: false
            responses:
                '200':
                    description: Success response with the GENIE brand banners data
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Banners not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
                    
    /brands/genie-banner/{id}:
        delete:
            summary: Delete an GENIE Brand Banner
            operationId: genieBrandDeleteBannerSection
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: id
                  in: path
                  required: true
                  schema:
                      type: string
                  description: ID of the banner to delete
            responses:
                '200':
                    description: Banner deleted successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Banner not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'                
         
    /brands/genie-value-image:
        get:
            summary: Get GENIE Value Image
            operationId: genieBrandViewValueImage
            tags:
                - Brands
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Success response with the GENIE brand value image data
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Banners not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
        put:
            summary: Add or Update GENIE Brand Value Image
            operationId: genieBrandAddOrUpdateValueImage
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                BrandValueGENIEImage:
                                    type: string
                                    description: Image for brand value section
                                BrandValueGENIEImageMobi:
                                    type: string
                                    description: Mobile Image for brand value section    
                            required:
                                - BrandValueGENIEImage
            responses:
                '200':
                    description: Banners added/updated successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
    
            
                    
    /brands/genie-product-tile:
        get:
            summary: View all GENIE Brand Product Tiles
            operationId: genieViewProductTiles
            tags:
                - Brands
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'    
                - in: query
                  name: isActive
                  schema:
                    type: string
                    enum:
                        - 'all'
                        - 'false'
                        - 'false'
                  required: false
            responses:
                '200':
                    description: Success response with the GENIE brand product tiles data
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Tiles not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'   
                    
        post:
            summary: Add a GENIE Brand Product Tile
            operationId: genieAddProductTile
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'    
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                TileImage:
                                    type: string
                                    description: URL of the tile image
                                TileHoverImage:
                                    type: string
                                    description: URL of the tile hover image
                                isActive:
                                    type: boolean
                                    description: Whether the tile is active
                                sortOrder:
                                    type: number
                                    description: Sort order of the tile
                            required:
                                    - TileImage
                                    - TileHoverImage
            responses:
                '200':
                    description: Tile added successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'            
                    
    /brands/genie-product-tile/{id}:
        put:
          summary: Edit a GENIE Brand Product Tile
          operationId: genieEditProductTile
          tags:
              - Brands
          security:
              - ApiKeyAuth: []
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the product tile to delete    
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          type: object
                          properties:
                              TileImage:
                                  type: string
                                  description: URL of the tile image
                              TileHoverImage:
                                  type: string
                                  description: URL of the tile hover image
                              isActive:
                                  type: boolean
                                  description: Whether the tile is active
                              sortOrder:
                                  type: number
                                  description: Sort order of the tile
                          required:
                              - TileHoverImage
                              - TileImage
          responses:
              '200':
                  description: Tile updated successfully
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '404':
                  description: Tile not found
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '500':
                  $ref: '#/components/responses/ServerError'  
    
        delete:
            summary: Delete a Genie Brand Product Tile
            operationId: genieDeleteProductTile
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the product tile to delete
            responses:
                '200':
                    description: Tile deleted successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Tile not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'                
    
    /brands/efficacy-banner:
        post:
            summary: Add or Update GENIE Brand Banners
            operationId: efficacyBrandAddHeroSection
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                banners:
                                    type: array
                                    description: Array of banners to add or update
                                    items:
                                        type: object
                                        properties:
                                            banner:
                                                type: string
                                                description: URL of the banner image or video
                                            mediaType:
                                                type: string
                                                enum: ['image', 'video']
                                                description: Type of media (image or video)
                                            bannerMobi:
                                                type: string
                                                description: URL of the banner image or video
                                            mediaTypeMobi:
                                                type: string
                                                enum: ['image', 'video']
                                                description: Type of media (image or video)    
                                            textOne:
                                                type: string
                                                description: First text overlay on the banner
                                            textTwo:
                                                type: string
                                                description: Second text overlay on the banner
                                            link:
                                                type: string
                                                description: URL link associated with the banner
                                            isTextVisible:
                                                type: boolean
                                                description: Whether the text is visible on the banner
                                            isActive:
                                                type: boolean
                                                description: Whether the banner is active
                                            sortOrder:
                                                type: number
                                                description: Sort order for the banner
                            required:
                                - banners
            responses:
                '200':
                    description: Banners added/updated successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
    
        get:
            summary: Get GENIE Brand Banners
            operationId: efficacyBrandViewBannerSection
            tags:
                - Brands
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: query
                  name: isActive
                  schema:
                    type: string
                    enum:
                        - 'true'
                        - 'false'
                  required: false
            responses:
                '200':
                    description: Success response with the GENIE brand banners data
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Banners not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
                    
    /brands/efficacy-banner/{id}:
        delete:
            summary: Delete an efficacy Brand Banner
            operationId: efficacyBrandDeleteBannerSection
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: id
                  in: path
                  required: true
                  schema:
                      type: string
                  description: ID of the banner to delete
            responses:
                '200':
                    description: Banner deleted successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Banner not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'                
         
    /brands/efficacy-value-image:
        get:
            summary: Get efficacy Value Image
            operationId: efficacyBrandViewValueImage
            tags:
                - Brands
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Success response with the GENIE brand value image data
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Banners not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
        put:
            summary: Add or Update GENIE Brand Value Image
            operationId: efficacyBrandAddOrUpdateValueImage
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                BrandValueImage:
                                    type: string
                                    description: Image for brand value section
                                BrandValueImageMobi:
                                    type: string
                                    description: Mobile Image for brand value section    
                            required:
                                - BrandValueImage
            responses:
                '200':
                    description: Banners added/updated successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
    
            
                    
    /brands/efficacy-category-tile:
        get:
            summary: View all GENIE Brand Product Tiles
            operationId: efficacyViewProductTiles
            tags:
                - Brands
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'   
                - in: query
                  name: isActive
                  schema:
                    type: string
                    enum:
                        - 'all'
                        - 'false'
                        - 'false'
                  required: false 
            responses:
                '200':
                    description: Success response with the GENIE brand product tiles data
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Tiles not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'   
                    
        post:
            summary: Add a efficacy Brand Product Tile
            operationId: efficacyAddProductTile
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'    
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                TileImage:
                                    type: string
                                    description: URL of the tile image
                                TileHoverImage:
                                    type: string
                                    description: URL of the tile hover image
                                isActive:
                                    type: boolean
                                    description: Whether the tile is active
                                sortOrder:
                                    type: number
                                    description: Sort order of the tile
                            required:
                                    - TileImage
                                    - TileHoverImage
            responses:
                '200':
                    description: Tile added successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'            
                    
    /brands/efficacy-category-tile/{id}:
        put:
          summary: Edit a efficacy Brand Category Tile
          operationId: efficacyEditCategoryTile
          tags:
              - Brands
          security:
              - ApiKeyAuth: []
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the product tile to delete    
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          type: object
                          properties:
                              TileImage:
                                  type: string
                                  description: URL of the tile image
                              TileHoverImage:
                                  type: string
                                  description: URL of the tile hover image
                              isActive:
                                  type: boolean
                                  description: Whether the tile is active
                              sortOrder:
                                  type: number
                                  description: Sort order of the tile
                          required:
                              - TileHoverImage
                              - TileImage
          responses:
              '200':
                  description: Tile updated successfully
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '404':
                  description: Tile not found
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '500':
                  $ref: '#/components/responses/ServerError'  
    
        delete:
            summary: Delete a efficacy Brand Category Tile
            operationId: efficacyDeleteCategoryTile
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the product tile to delete
            responses:
                '200':
                    description: Tile deleted successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Tile not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError' 
                    
    /brands/efficacy-collections-tile:
        get:
            summary: View all Efficacy Brand collections Tiles
            operationId: efficacyViewCollectionTiles
            tags:
                - Brands
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'   
                - in: query
                  name: isActive
                  schema:
                    type: string
                    enum:
                        - 'all'
                        - 'false'
                        - 'false'
                  required: false 
            responses:
                '200':
                    description: Success response with the GENIE brand product tiles data
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Tiles not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'   
                    
        post:
            summary: Add a efficacy Brand Collections Tile
            operationId: efficacyAddCollectionTile
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'    
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                TileImage:
                                    type: string
                                    description: URL of the tile image
                                TileHoverImage:
                                    type: string
                                    description: URL of the tile hover image
                                isActive:
                                    type: boolean
                                    description: Whether the tile is active
                                sortOrder:
                                    type: number
                                    description: Sort order of the tile
                            required:
                                    - TileImage
                                    - TileHoverImage
            responses:
                '200':
                    description: Tile added successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'            
                    
    /brands/efficacy-collections-tile/{id}:
        put:
          summary: Edit a efficacy Brand Collection Tile
          operationId: efficacyEditCollectionTile
          tags:
              - Brands
          security:
              - ApiKeyAuth: []
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the product tile to delete    
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          type: object
                          properties:
                              TileImage:
                                  type: string
                                  description: URL of the tile image
                              TileHoverImage:
                                  type: string
                                  description: URL of the tile hover image
                              isActive:
                                  type: boolean
                                  description: Whether the tile is active
                              sortOrder:
                                  type: number
                                  description: Sort order of the tile
                          required:
                              - TileHoverImage
                              - TileImage
          responses:
              '200':
                  description: Tile updated successfully
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '404':
                  description: Tile not found
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '500':
                  $ref: '#/components/responses/ServerError'  
    
        delete:
            summary: Delete a efficacy Brand Collection Tile
            operationId: efficacyDeleteCollectionTile
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the product tile to delete
            responses:
                '200':
                    description: Tile deleted successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Tile not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
                    
    /brands/efficacy-finishes-tile:
        get:
            summary: View all EFFICACY Brand Finishes Tiles
            operationId: efficacyViewFinishesTiles
            tags:
                - Brands
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion' 
                - in: query
                  name: isActive
                  schema:
                    type: string
                    enum:
                        - 'all'
                        - 'false'
                        - 'false'
                  required: false   
            responses:
                '200':
                    description: Success response with the GENIE brand product tiles data
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Tiles not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'   
                    
        post:
            summary: Add a efficacy Brand Finishes Tile
            operationId: efficacyAddFinishesTile
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'    
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                TileImage:
                                    type: string
                                    description: URL of the tile image
                                TileHoverImage:
                                    type: string
                                    description: URL of the tile hover image
                                isActive:
                                    type: boolean
                                    description: Whether the tile is active
                                sortOrder:
                                    type: number
                                    description: Sort order of the tile
                            required:
                                    - TileImage
                                    - TileHoverImage
            responses:
                '200':
                    description: Tile added successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'            
                    
    /brands/efficacy-finishes-tile/{id}:
        put:
          summary: Edit a efficacy Brand Finishes Tile
          operationId: efficacyEditFinishesTile
          tags:
              - Brands
          security:
              - ApiKeyAuth: []
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the Finishes tile to delete    
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          type: object
                          properties:
                              TileImage:
                                  type: string
                                  description: URL of the tile image
                              TileHoverImage:
                                  type: string
                                  description: URL of the tile hover image
                              isActive:
                                  type: boolean
                                  description: Whether the tile is active
                              sortOrder:
                                  type: number
                                  description: Sort order of the tile
                          required:
                              - TileHoverImage
                              - TileImage
          responses:
              '200':
                  description: Tile updated successfully
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '404':
                  description: Tile not found
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '500':
                  $ref: '#/components/responses/ServerError'  
    
        delete:
            summary: Delete a efficacy Brand Finishes Tile
            operationId: efficacyDeleteFinishesTile
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the product tile to delete
            responses:
                '200':
                    description: Tile deleted successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Tile not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'                    
    
    
    /brands/aasatti-banner:
      post:
          summary: Add or Update AASATTI Brand Banners
          operationId: aasattiBrandAddHeroSection
          tags:
              - Brands
          security:
              - ApiKeyAuth: []
          parameters:
              - $ref: '#/components/parameters/headerLanguage'
              - $ref: '#/components/parameters/headerPlatform'
              - $ref: '#/components/parameters/headerVersion'
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          type: object
                          properties:
                              banners:
                                  type: array
                                  description: Array of banners to add or update
                                  items:
                                      type: object
                                      properties:
                                          banner:
                                              type: string
                                              description: URL of the banner image or video
                                          mediaType:
                                              type: string
                                              enum: ['image', 'video']
                                              description: Type of media (image or video)
                                          bannerMobi:
                                              type: string
                                              description: URL of the banner image or video
                                          mediaTypeMobi:
                                              type: string
                                              enum: ['image', 'video']
                                              description: Type of media (image or video)    
                                          textOne:
                                              type: string
                                              description: First text overlay on the banner
                                          textTwo:
                                              type: string
                                              description: Second text overlay on the banner
                                          link:
                                              type: string
                                              description: URL link associated with the banner
                                          isTextVisible:
                                              type: boolean
                                              description: Whether the text is visible on the banner
                                          isActive:
                                              type: boolean
                                              description: Whether the banner is active
                                          sortOrder:
                                              type: number
                                              description: Sort order for the banner
                          required:
                              - banners
          responses:
              '200':
                  description: Banners added/updated successfully
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '500':
                  $ref: '#/components/responses/ServerError'
  
      get:
          summary: Get AASATTI Brand Banners
          operationId: aasattiBrandViewBannerSection
          tags:
              - Brands
          parameters:
              - $ref: '#/components/parameters/headerLanguage'
              - $ref: '#/components/parameters/headerPlatform'
              - $ref: '#/components/parameters/headerVersion'
              - in: query
                name: isActive
                schema:
                  type: string
                  enum:
                      - 'true'
                      - 'false'
                required: false
          responses:
              '200':
                  description: Success response with the GENIE brand banners data
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '404':
                  description: Banners not found
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '500':
                  $ref: '#/components/responses/ServerError'
                    
    /brands/aasatti-banner/{id}:
        delete:
            summary: Delete an aasatti Brand Banner
            operationId: aasatiBrandDeleteBannerSection
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: id
                  in: path
                  required: true
                  schema:
                      type: string
                  description: ID of the banner to delete
            responses:
                '200':
                    description: Banner deleted successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Banner not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'                
         
    /brands/aasatti-value-image:
        get:
            summary: Get aasatti Value Image
            operationId: aasatiBrandViewValueImage
            tags:
                - Brands
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Success response with the AASATTI brand value image data
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Banners not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
        put:
            summary: Add or Update AASATTI Brand Value Image
            operationId: aasatiBrandAddOrUpdateValueImage
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                BrandValueImage:
                                    type: string
                                    description: Image for brand value section
                                BrandValueImageMobi:
                                    type: string
                                    description: MobileImage for brand value section    
                            required:
                                - BrandValueImage
            responses:
                '200':
                    description: Banners added/updated successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
    
    /brands/aasatti-inspiration:
        get:
            summary: View all AASATTI Brand Product Tiles
            operationId: aasatiBrandViewInspiration
            tags:
                - Brands
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'    
                - in: query
                  name: isActive
                  schema:
                    type: string
                    enum:
                        - 'all'
                        - 'false'
                        - 'false'
                  required: false
            responses:
                '200':
                    description: Success response with the GENIE brand product tiles data
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Tiles not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'   
                    
        post:
            summary: Add a AASATTI Brand Product Tile
            operationId: aasatiBrandAddInspiration
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'    
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                InspirationName:
                                    type: string
                                    description: Name of inspiration
                                InspiredImage:
                                    type: string
                                    description: URL of the inspired image
                                BuiltImage:
                                    type: string
                                    description: URL of the built image
                                isActive:
                                    type: boolean
                                    description: Whether the tile is active
                                sortOrder:
                                    type: number
                                    description: Sort order of the tile
                            required:
                                    - TileImage
                                    - TileHoverImage
            responses:
                '200':
                    description: Inspiration added successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'            
                    
    /brands/aasatti-inspiration/{id}:
        put:
          summary: Edit a AASATTI Brand Product Tile
          operationId: aasatiBrandEditInspiration
          tags:
              - Brands
          security:
              - ApiKeyAuth: []
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the product tile to delete    
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          type: object
                          properties:
                              InspirationName:
                                  type: string
                                  description: Name of inspiration
                              InspiredImage:
                                  type: string
                                  description: URL of the inspired image
                              BuiltImage:
                                  type: string
                                  description: URL of the built image
                              isActive:
                                  type: boolean
                                  description: Whether the tile is active
                              sortOrder:
                                  type: number
                                  description: Sort order of the tile
                          required:
                              - TileHoverImage
                              - TileImage
          responses:
              '200':
                  description: Inspiration updated successfully
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '404':
                  description: Tile not found
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '500':
                  $ref: '#/components/responses/ServerError'  
    
        delete:
            summary: Delete a AASATTI Brand Product Tile
            operationId: aasatiBrandDeleteInspiration
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the inspiration to delete
            responses:
                '200':
                    description: Inspiration deleted successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Inspiration not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
    
    /brands/aasatti-product-tile:
        get:
            summary: View all AASATTI Brand Product Tiles
            operationId: aasatiBrandViewProductTiles
            tags:
                - Brands
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'    
                - in: query
                  name: isActive
                  schema:
                    type: string
                    enum:
                        - 'all'
                        - 'true'
                        - 'false'
                  required: false
            responses:
                '200':
                    description: Success response with the GENIE brand product tiles data
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Tiles not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'   
                    
        post:
            summary: Add a AASATTI Brand Product Tile
            operationId: aasatiBrandAddProductTile
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'    
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                TileImage:
                                    type: string
                                    description: URL of the tile image
                                TileHoverImage:
                                    type: string
                                    description: URL of the tile hover image
                                isActive:
                                    type: boolean
                                    description: Whether the tile is active
                                sortOrder:
                                    type: number
                                    description: Sort order of the tile
                            required:
                                    - TileImage
                                    - TileHoverImage
            responses:
                '200':
                    description: Tile added successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'            
                    
    /brands/aasatti-product-tile/{id}:
        put:
          summary: Edit a AASATTI Brand Product Tile
          operationId: aasatiBrandEditProductTile
          tags:
              - Brands
          security:
              - ApiKeyAuth: []
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the product tile to delete    
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          type: object
                          properties:
                              TileImage:
                                  type: string
                                  description: URL of the tile image
                              TileHoverImage:
                                  type: string
                                  description: URL of the tile hover image
                              isActive:
                                  type: boolean
                                  description: Whether the tile is active
                              sortOrder:
                                  type: number
                                  description: Sort order of the tile
                          required:
                              - TileHoverImage
                              - TileImage
          responses:
              '200':
                  description: Tile updated successfully
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '404':
                  description: Tile not found
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '500':
                  $ref: '#/components/responses/ServerError'  
    
        delete:
            summary: Delete a AASATTI Brand Product Tile
            operationId: aasatiBrandDeleteProductTile
            tags:
                - Brands
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the product tile to delete
            responses:
                '200':
                    description: Tile deleted successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Tile not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
    
                    
    /careers:
        get:
            summary: Get the career entry
            operationId: getCareers
            tags:
                - Careers
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Success response with the career entry
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Career entry not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
                    
        put:
          summary: Add or update the career entry
          operationId: addOrUpdateCareer
          tags:
              - Careers
          security:
              - ApiKeyAuth: []
          parameters:
              - $ref: '#/components/parameters/headerLanguage'
              - $ref: '#/components/parameters/headerPlatform'
              - $ref: '#/components/parameters/headerVersion'
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          type: object
                          properties:
                              CareersBannerType:
                                  type: string
                                  description: Type of the banner
                              CareersBannerTitle:
                                  type: string
                                  description: Title of the careers banner
                              CareersBannerFileName:
                                  type: string
                                  description: Filename of the careers banner image
                              LifeatSGiTitle:
                                  type: string
                                  description: Title for Life at SGi section
                              LifeatSGiSummary:
                                  type: string
                                  description: Summary for Life at SGi section
                              LifeatSGiDetail:
                                  type: string
                                  description: Detailed information for Life at SGi section
                              CareersImage:
                                  type: string
                                  description: Image URL for the careers section
                              WeAreHiringText:
                                  type: string
                                  description: Text for the "We Are Hiring" section
                          required:
                              - CareersBannerTitle
                              - CareersBannerFileName
                              - LifeatSGiTitle
                              - LifeatSGiSummary
                              - CareersImage
          responses:
              '200':
                  description: Career entry was successfully added or updated
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '500':
                  $ref: '#/components/responses/ServerError'            

    /categories/{catId}:
        get:
            summary: Retrieve Category Detail
            operationId: categoriesDetail
            tags:
                - Categories
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: catId
                  in: path
                  required: true
                  schema:
                      type: string
                  description: ID of the category to retrieve
            responses:
                '200':
                    description: Category data
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              $ref: '#/components/schemas/Category'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'

        put:
            summary: Update Category
            operationId: updateCategory
            tags:
                - Categories
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: catId
                  in: path
                  required: true
                  schema:
                      type: string
                  description: ID of the category to update
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                name:
                                    type: string
                                    description: Category name
                                slug:
                                    type: string
                                    description: URL-friendly identifier
                                image:
                                    type: string
                                    description: URL of the category image
                                description:
                                    type: string
                                    description: Detailed description of the category
                                parentId:
                                    type: string
                                    description: ID of the parent category
                                isSuspended:
                                    type: boolean
                                    description: Whether the category is suspended
                                isDeleted:
                                    type: boolean
                                    description: Whether the category is deleted
                            required:
                                - name
                                - slug
            responses:
                '200':
                    description: Successfully updated category
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /categories:
        get:
            summary: List Categories
            operationId: listCategories
            tags:
                - Categories
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: query
                  name: search
                  schema:
                      type: string
                  description: Search keyword (by name, slug, or ID)
                - in: query
                  name: catId
                  schema:
                      type: string
                  description: ID of a specific category to filter
            responses:
                '200':
                    description: List of categories
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: array
                                              items:
                                                  $ref: '#/components/schemas/Category'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'

        post:
            summary: Add a New Category
            operationId: addCategory
            tags:
                - Categories
            security:
                - ApiKeyAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                name:
                                    type: string
                                    description: Category name
                                slug:
                                    type: string
                                    description: URL-friendly identifier
                                image:
                                    type: string
                                    description: URL of the category image
                                description:
                                    type: string
                                    description: Detailed description of the category
                                parentId:
                                    type: string
                                    description: ID of the parent category
                                isSuspended:
                                    type: boolean
                                    description: Whether the category is suspended
                                isDeleted:
                                    type: boolean
                                    description: Whether the category is deleted
                            required:
                                - name
                                - slug
            responses:
                '200':
                    description: Successfully added category
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /filters/brand:
      get:
        summary: Get all brand filters or a specific brand filter by ID
        operationId: listBrandFilters
        tags:
          - Filters
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the brand filter to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the brand filters
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No brand filters found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
  
      post:
        summary: Add a new brand filter
        operationId: addBrandFilter
        tags:
          - Filters
        security:
                - ApiKeyAuth: []  
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'        
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  brandName:
                    type: string
                    description: The name of the brand to be added
                  logo:
                    type: string
                    description: The name of the brand to be added
                required:
                  - brandName
        responses:
          '200':
            description: Brand filter added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Brand name is required or brand already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'

    /filters/brand/{id}:
        put:
          summary: Edit an existing brand filter
          operationId: editBrandFilter
          tags:
            - Filters
          security:
                - ApiKeyAuth: []  
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: id
              in: path
              required: true
              description: The ID of the brand filter to update
              schema:
                type: string
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    brandName:
                      type: string
                      description: The new name of the brand filter
                    logo:
                      type: string
                      description: The new name of the brand filter
                  required:
                    - brandName
          responses:
            '200':
              description: Brand filter updated successfully
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '404':
              description: Brand filter not found
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '500':
              $ref: '#/components/responses/ServerError'
    
        delete:
          summary: Delete an existing brand filter (soft delete)
          operationId: deleteBrandFilter
          tags:
            - Filters
          security:
            - ApiKeyAuth: []  
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: id
              in: path
              required: true
              description: The ID of the brand filter to delete
              schema:
                type: string
          responses:
            '200':
              description: Brand filter deleted successfully
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '404':
              description: Brand filter not found
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '500':
              $ref: '#/components/responses/ServerError'
    
    /filters/type:
      get:
        summary: Get all brand filters or a specific brand filter by ID
        operationId: listTypeFilters
        tags:
          - Filters
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the brand filter to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: sortBy
            in: query
            description: sort by 
            required: false
            schema:
              type: string
              enum: ['createdAt', 'sortOrder']
              default: 'createdAt'
          - name: sortOrder
            in: query
            description: sorting in asc order or desc order
            required: false
            schema:
              type: string
              enum: ['asc', 'desc']
              default: 'desc'    
        responses:
          '200':
            description: Successfully retrieved the brand filters
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No brand filters found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
  
      post:
        summary: Add a new type filter
        operationId: addTypeFilter
        tags:
          - Filters
        security:
                - ApiKeyAuth: []  
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'        
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  brandName:
                    type: string
                    description: The name of the type to be added
                  displayName:
                    type: string
                    description: The display name of the type to be added  
                required:
                  - brandName
        responses:
          '200':
            description: Type filter added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Type name is required or brand already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'

    /filters/type/{id}:
        put:
          summary: Edit an existing type filter
          operationId: editTypeFilter
          tags:
            - Filters
          security:
                - ApiKeyAuth: []  
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: id
              in: path
              required: true
              description: The ID of the type filter to update
              schema:
                type: string
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    brandName:
                      type: string
                      description: The new name of the type filter
                    displayName:
                      type: string
                      description: The display name of the type to be added
                    sortOrder:
                      type: integer
                      description: The sorting order of the type filter  
                  required:
                    - brandName
          responses:
            '200':
              description: Type filter updated successfully
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '404':
              description: Type filter not found
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '500':
              $ref: '#/components/responses/ServerError'
    
        delete:
          summary: Delete an existing type filter (soft delete)
          operationId: deleteTypeFilter
          tags:
            - Filters
          security:
            - ApiKeyAuth: []  
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: id
              in: path
              required: true
              description: The ID of the type filter to delete
              schema:
                type: string
          responses:
            '200':
              description: type filter deleted successfully
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '404':
              description: type filter not found
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '500':
              $ref: '#/components/responses/ServerError'
              
    /filters/series:
        get:
          summary: Get all series filters or a specific series filter by ID
          operationId: listSeriesFilters
          tags:
            - Filters
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: id
              in: query
              required: false
              description: The ID of the series filter to retrieve
              schema:
                type: string
            - name: isDelete
              in: query
              required: false
              description: Filter by deletion status (true, false, or omit for all)
              schema:
                type: string
                enum: ['true', 'false']
            - name: isActive
              in: query
              required: false
              description: Filter by active status (true, false, or omit for all)
              schema:
                type: string
                enum: ['true', 'false']
            - name: brandId
              in: query
              required: false
              description: Filter by project brand IDs (comma-separated)
              schema:
                type: string
            - name: sortBy
              in: query
              description: sort by 
              required: false
              schema:
                type: string
                enum: ['createdAt', 'sortOrder']
                default: 'createdAt'
            - name: sortOrder
              in: query
              description: sorting in asc order or desc order
              required: false
              schema:
                type: string
                enum: ['asc', 'desc']
                default: 'desc'    
          responses:
            '200':
              description: Successfully retrieved the series filters
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '404':
              description: No series filters found
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '500':
              $ref: '#/components/responses/ServerError'
      
        post:
          summary: Add a new series filter
          operationId: addSeriesFilter
          tags:
            - Filters
          security:
                - ApiKeyAuth: []  
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'      
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    seriesName:
                      type: string
                      description: The name of the series to be added
                    displayName:
                      type: string
                      description: The display name of the series to be added
                    brands:
                      type: array
                      items:
                        type: string
                        format: uuid
                      description: List of Brand IDs associated with the filter  
                  required:
                    - seriesName
          responses:
            '200':
              description: Series filter added successfully
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '400':
              description: Series name is required or series already exists
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '500':
              $ref: '#/components/responses/ServerError'    
              
    /filters/series/{id}:
        put:
          summary: Edit an existing series filter
          operationId: editSeriesFilter
          tags:
            - Filters
          security:
            - ApiKeyAuth: []  
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: id
              in: path
              required: true
              description: The ID of the series filter to update
              schema:
                type: string
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    seriesName:
                      type: string
                      description: The new name of the series filter
                    displayName:
                      type: string
                      description: The display name of the series to be added
                    sortOrder:
                      type: integer
                      description: The sorting order of the series filter  
                    brands:
                      type: array
                      items:
                        type: string
                        format: uuid
                      description: List of Brand IDs associated with the filter  
                  required:
                    - seriesName
          responses:
            '200':
              description: Series filter updated successfully
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '404':
              description: Series filter not found
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '500':
              $ref: '#/components/responses/ServerError'
      
        delete:
          summary: Delete an existing series filter (soft delete)
          operationId: deleteSeriesFilter
          tags:
            - Filters
          security:
            - ApiKeyAuth: []  
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: id
              in: path
              required: true
              description: The ID of the series filter to delete
              schema:
                type: string
          responses:
            '200':
              description: Series filter deleted successfully
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '404':
              description: Series filter not found
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '500':
              $ref: '#/components/responses/ServerError'   
              
    /filters/application:
      get:
        summary: Get all application filters or a specific application filter by ID
        operationId: listApplicationFilters
        tags:
          - Filters
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the application filter to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: sortBy
            in: query
            description: sort by 
            required: false
            schema:
              type: string
              enum: ['createdAt', 'sortOrder']
              default: 'createdAt'
          - name: sortOrder
            in: query
            description: sorting in asc order or desc order
            required: false
            schema:
              type: string
              enum: ['asc', 'desc']
              default: 'desc'    
        responses:
          '200':
            description: Successfully retrieved the application filters
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No application filters found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      post:
        summary: Add a new application filter
        operationId: addApplicationFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []  
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'  
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the application to be added
                  displayName:
                      type: string
                      description: The display name of the application to be added 
                required:
                  - name
        responses:
          '200':
            description: Application filter added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Application name is required or application already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError' 
            
    /filters/application/{id}:
      put:
        summary: Edit an existing application filter
        operationId: editApplicationFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []  
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the application filter to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The new name of the application filter
                  displayName:
                      type: string
                      description: The display name of the application to be added
                  sortOrder:
                    type: integer
                    description: The sorting order of the type filter  
                required:
                  - name
        responses:
          '200':
            description: Application filter updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Application filter not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      delete:
        summary: Delete an existing application filter (soft delete)
        operationId: deleteApplicationFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []  
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the application filter to delete
            schema:
              type: string
        responses:
          '200':
            description: Application filter deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Application filter not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'        
    
    /filters/category:
      get:
        summary: Get all category filters or a specific category filter by ID
        operationId: listCategoryFilters
        tags:
          - Filters
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the category filter to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: brandId
            in: query
            required: false
            description: Filter by project brand IDs (comma-separated)
            schema:
              type: string
          - name: sortBy
            in: query
            description: sort by 
            required: false
            schema:
              type: string
              enum: ['createdAt', 'sortOrder']
              default: 'createdAt'
          - name: sortOrder
            in: query
            description: sorting in asc order or desc order
            required: false
            schema:
              type: string
              enum: ['asc', 'desc']
              default: 'desc'    
        responses:
          '200':
            description: Successfully retrieved the application filters
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No application filters found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      post:
        summary: Add a new category filter
        operationId: addCategoryFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []  
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'  
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the category to be added
                  displayName:
                      type: string
                      description: The display name of the category to be added  
                  brands:
                    type: array
                    items:
                      type: string
                      format: uuid
                    description: List of Brand IDs associated with the filter  
                required:
                  - name
        responses:
          '200':
            description: Filter added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Name is required or already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError' 
            
    /filters/category/{id}:
      put:
        summary: Edit an existing category filter
        operationId: editCategoryFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []  
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the category filter to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The new name of the filter
                  displayName:
                      type: string
                      description: The display name of the application to be added
                  sortOrder:
                    type: integer
                    description: The sorting order of the application filter  
                  brands:
                    type: array
                    items:
                      type: string
                      format: uuid
                    description: List of Brand IDs associated with the filter  
                required:
                  - name
        responses:
          '200':
            description:  Filter updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Filter not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      delete:
        summary: Delete an existing filter (soft delete)
        operationId: deleteCategoryFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []  
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the filter to delete
            schema:
              type: string
        responses:
          '200':
            description: Filter deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Application filter not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'        

    /filters/family:
      get:
        summary: Get all family filters or a specific family filter by ID
        operationId: listFamilyFilters
        tags:
          - Filters
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the family filter to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: brandId
            in: query
            required: false
            description: Filter by project brand IDs (comma-separated)
            schema:
              type: string
          - name: sortBy
            in: query
            description: sort by 
            required: false
            schema:
              type: string
              enum: ['createdAt', 'sortOrder']
              default: 'createdAt'
          - name: sortOrder
            in: query
            description: sorting in asc order or desc order
            required: false
            schema:
              type: string
              enum: ['asc', 'desc']
              default: 'desc'    
        responses:
          '200':
            description: Successfully retrieved the family filters
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    filters:
                      type: array
                      items:
                        $ref: '#/components/schemas/FamilyFilter'
          '404':
            description: No family filters found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      post:
        summary: Add a new family filter
        operationId: addFamilyFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FamilyFilter'
        responses:
          '200':
            description: Filter added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Validation error or filter already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'

    /filters/family/{id}:
        put:
          summary: Edit an existing family filter
          operationId: editFamilyFilter
          tags:
            - Filters
          security:
            - ApiKeyAuth: []
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: id
              in: path
              required: true
              description: The ID of the family filter to update
              schema:
                type: string
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/FamilyFilter'
          responses:
            '200':
              description: Filter updated successfully
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '404':
              description: Filter not found
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '500':
              $ref: '#/components/responses/ServerError'
      
        delete:
          summary: Delete an existing family filter (soft delete)
          operationId: deleteFamilyFilter
          tags:
            - Filters
          security:
            - ApiKeyAuth: []
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: id
              in: path
              required: true
              description: The ID of the family filter to delete
              schema:
                type: string
          responses:
            '200':
              description: Filter deleted successfully
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '404':
              description: Family filter not found
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '500':
              $ref: '#/components/responses/ServerError'
  
    /filters/light-colors:
      get:
        summary: Get all light colors filters or a specific light colors filter by ID
        operationId: listLightColorsFilters
        tags:
          - Filters
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the light colors filter to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the light colors filters
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No light colors filters found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
  
      post:
        summary: Add a new light colors filter
        operationId: addLightColorsFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the light colors filter to be added
                required:
                  - name
        responses:
          '200':
            description: Filter added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Name is required or already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /filters/light-colors/{id}:
      put:
        summary: Edit an existing light colors filter
        operationId: editLightColorsFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the light colors filter to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The new name of the light colors filter
                required:
                  - name
        responses:
          '200':
            description: Filter updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Filter not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
  
      delete:
        summary: Delete an existing light colors filter (soft delete)
        operationId: deleteLightColorsFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the light colors filter to delete
            schema:
              type: string
        responses:
          '200':
            description: Filter deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Light colors filter not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /filters/output-lumens-fixture:
      get:
        summary: Get all output lumens fixture filters or a specific output lumens fixture filter by ID
        operationId: listOutputLumensFixtureFilters
        tags:
          - Filters
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the output lumens fixture filter to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the output lumens fixture filters
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No output lumens fixture filters found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
  
      post:
        summary: Add a new output lumens fixture filter
        operationId: addOutputLumensFixtureFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the output lumens fixture filter to be added
                required:
                  - name
        responses:
          '200':
            description: Filter added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Name is required or already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /filters/output-lumens-fixture/{id}:
        put:
          summary: Edit an existing output lumens fixture filter
          operationId: editOutputLumensFixtureFilter
          tags:
            - Filters
          security:
            - ApiKeyAuth: []
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: id
              in: path
              required: true
              description: The ID of the output lumens fixture filter to update
              schema:
                type: string
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    name:
                      type: string
                      description: The new name of the output lumens fixture filter
                  required:
                    - name
          responses:
            '200':
              description: Filter updated successfully
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '404':
              description: Filter not found
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '500':
              $ref: '#/components/responses/ServerError'
    
        delete:
          summary: Delete an existing output lumens fixture filter (soft delete)
          operationId: deleteOutputLumensFixtureFilter
          tags:
            - Filters
          security:
            - ApiKeyAuth: []
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: id
              in: path
              required: true
              description: The ID of the output lumens fixture filter to delete
              schema:
                type: string
          responses:
            '200':
              description: Filter deleted successfully
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '404':
              description: Output lumens fixture filter not found
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '500':
              $ref: '#/components/responses/ServerError'
              
    /filters/output-lumens-foot:
        get:
          summary: Get all output lumens foot filters or a specific output lumens foot filter by ID
          operationId: listOutputLumensFootFilters
          tags:
            - Filters
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: id
              in: query
              required: false
              description: The ID of the output lumens foot filter to retrieve
              schema:
                type: string
            - name: isDelete
              in: query
              required: false
              description: Filter by deletion status (true, false, or omit for all)
              schema:
                type: string
                enum: ['true', 'false']
            - name: isActive
              in: query
              required: false
              description: Filter by active status (true, false, or omit for all)
              schema:
                type: string
                enum: ['true', 'false']
          responses:
            '200':
              description: Successfully retrieved the output lumens foot filters
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '404':
              description: No output lumens foot filters found
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '500':
              $ref: '#/components/responses/ServerError'
    
        post:
          summary: Add a new output lumens foot filter
          operationId: addOutputLumensFootFilter
          tags:
            - Filters
          security:
            - ApiKeyAuth: []
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    name:
                      type: string
                      description: The name of the output lumens foot filter to be added
                  required:
                    - name
          responses:
            '200':
              description: Filter added successfully
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '400':
              description: Name is required or already exists
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '500':
              $ref: '#/components/responses/ServerError'          
    
    /filters/output-lumens-foot/{id}:
        put:
          summary: Edit an existing output lumens foot filter
          operationId: editOutputLumensFootFilter
          tags:
            - Filters
          security:
            - ApiKeyAuth: []
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: id
              in: path
              required: true
              description: The ID of the output lumens foot filter to update
              schema:
                type: string
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    name:
                      type: string
                      description: The new name of the output lumens foot filter
                  required:
                    - name
          responses:
            '200':
              description: Filter updated successfully
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '404':
              description: Filter not found
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '500':
              $ref: '#/components/responses/ServerError'
    
        delete:
          summary: Delete an existing output lumens foot filter (soft delete)
          operationId: deleteOutputLumensFootFilter
          tags:
            - Filters
          security:
            - ApiKeyAuth: []
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: id
              in: path
              required: true
              description: The ID of the output lumens foot filter to delete
              schema:
                type: string
          responses:
            '200':
              description: Filter deleted successfully
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '404':
              description: Output lumens foot filter not found
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '500':
              $ref: '#/components/responses/ServerError'
              
    /filters/output-watts-fixture:
      get:
        summary: Get all output watts fixture filters or a specific filter by ID
        operationId: listOutputWattsFixtureFilters
        tags:
          - Filters
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the output watts fixture filter to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the output watts fixture filters
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No output watts fixture filters found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      post:
        summary: Add a new output watts fixture filter
        operationId: addOutputWattsFixtureFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the filter to be added
                required:
                  - name
        responses:
          '200':
            description: Filter added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Name is required or filter already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
          
    /filters/output-watts-fixture/{id}:
      put:
        summary: Edit an existing output watts fixture filter
        operationId: editOutputWattsFixtureFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the output watts fixture filter to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The new name of the filter
                required:
                  - name
        responses:
          '200':
            description: Filter updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Filter not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      delete:
        summary: Soft delete an existing output watts fixture filter
        operationId: deleteOutputWattsFixtureFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the output watts fixture filter to delete
            schema:
              type: string
        responses:
          '200':
            description: Filter deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Filter not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
            
    /filters/output-watts-foot:
      get:
        summary: Get all Output Watts Foot filters or a specific filter by ID
        operationId: listOutputWattsFootFilters
        tags:
          - Filters
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the Output Watts Foot filter to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the Output Watts Foot filters
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No Output Watts Foot filters found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      post:
        summary: Add a new Output Watts Foot filter
        operationId: addOutputWattsFootFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []  
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the Output Watts Foot filter to be added
                required:
                  - name
        responses:
          '200':
            description: Filter added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Name is required or filter already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'   
            
    /filters/output-watts-foot/{id}:
      put:
        summary: Edit an existing Output Watts Foot filter
        operationId: editOutputWattsFootFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the Output Watts Foot filter to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The new name of the Output Watts Foot filter
                required:
                  - name
        responses:
          '200':
            description: Filter updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Filter not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      delete:
        summary: Delete an existing Output Watts Foot filter (soft delete)
        operationId: deleteOutputWattsFootFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the Output Watts Foot filter to delete
            schema:
              type: string
        responses:
          '200':
            description: Filter deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Filter not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'        
    
    /filters/material:
      get:
        summary: Get all material filters or a specific filter by ID
        operationId: listMaterialFilters
        tags:
          - Filters
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the material filter to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the material filters
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No material filters found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      post:
        summary: Add a new material filter
        operationId: addMaterialFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'  
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the material filter to be added
                  displayName:
                      type: string
                      description: The display name of the material to be added
                  sortOrder:
                    type: integer
                    description: The sorting order of the material filter   
                required:
                  - name
        responses:
          '200':
            description: Filter added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Name is required or filter already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /filters/material/{id}:
      put:
        summary: Edit an existing material filter
        operationId: editMaterialFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the material filter to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The new name of the material filter
                  displayName:
                      type: string
                      description: The display name of the material to be added
                  sortOrder:
                    type: integer
                    description: The sorting order of the material filter  
                required:
                  - name
        responses:
          '200':
            description: Filter updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Filter not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      delete:
        summary: Soft delete an existing material filter
        operationId: deleteMaterialFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the material filter to delete
            schema:
              type: string
        responses:
          '200':
            description: Filter deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Filter not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'

    /filters/collection:
      get:
        summary: Get all Collection filters or a specific filter by ID
        operationId: listCollectionFilters
        tags:
          - Filters
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            required: false
            description: The ID of the Collection filter to retrieve
            schema:
              type: string
          - name: isDelete
            in: query
            required: false
            description: Filter by deletion status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isActive
            in: query
            required: false
            description: Filter by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
        responses:
          '200':
            description: Successfully retrieved the Collection filters
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No Collection filters found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      post:
        summary: Add a new Collection filter
        operationId: addCollectionFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'  
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the Collection filter to be added
                  displayName:
                      type: string
                      description: The display name of the Collection to be added
                  sortOrder:
                    type: integer
                    description: The sorting order of the Collection filter  
                required:
                  - name
        responses:
          '200':
            description: Filter added successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Name is required or filter already exists
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /filters/collection/{id}:
      put:
        summary: Edit an existing Collection filter
        operationId: editCollectionFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the Collection filter to update
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    description: The name of the Collection filter to be added
                  displayName:
                      type: string
                      description: The display name of the Collection to be added
                  sortOrder:
                    type: integer
                    description: The sorting order of the Collection filter  
                required:
                  - name
        responses:
          '200':
            description: Filter updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Filter not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
      delete:
        summary: Soft delete an existing Collection filter
        operationId: deleteCollectionFilter
        tags:
          - Filters
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: The ID of the Collection filter to delete
            schema:
              type: string
        responses:
          '200':
            description: Filter deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Filter not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'

    /home/<USER>
        get:
            summary: View Hero Section
            operationId: viewHeroSection
            tags:
                - Home
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'
        post:
            summary: Add Hero Section
            operationId: addHeroSection
            tags:
                - Home
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                heroSection:
                                    type: object
                                    properties:
                                        carouselImages:
                                            type: array
                                            items:
                                                type: object
                                                properties:
                                                    banner:
                                                        type: string
                                                        description: URL of the banner image or video
                                                    mediaType:
                                                        type: string
                                                        enum: [image, video]
                                                        description: Type of media (image or video)
                                                    bannerMobi:
                                                        type: string
                                                        description: URL of the banner image or video
                                                    mediaTypeMobi:
                                                        type: string
                                                        enum: [image, video]
                                                        description: Type of media (image or video)    
                                                    textOne:
                                                        type: string
                                                    textTwo:
                                                        type: string
                                                    link:
                                                        type: string
                                                    isTextVisible:
                                                        type: boolean
                            required:
                                - heroSection
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'
  
        put:
            summary: Update Hero Section
            operationId: updateHeroSection
            tags:
                - Home
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                heroSection:
                                    type: object
                                    properties:
                                        carouselImages:
                                            type: array
                                            items:
                                                type: object
                                                properties:
                                                    banner:
                                                        type: string
                                                        description: URL of the banner image or video
                                                    mediaType:
                                                        type: string
                                                        enum: [image, video]
                                                        description: Type of media (image or video)
                                                    bannerMobi:
                                                        type: string
                                                        description: URL of the banner image or video
                                                    mediaTypeMobi:
                                                        type: string
                                                        enum: [image, video]
                                                        description: Type of media (image or video)    
                                                    text1:
                                                        type: string
                                                    text2:
                                                        type: string
                                                    link:
                                                        type: string
                            required:
                                - heroSection
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /home/<USER>
        get:
            summary: View Hero Section Popup Menu
            operationId: viewHeroSectionPopupMenu
            tags:
                - Home
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    success:
                                        type: boolean
                                    message:
                                        type: string
                                    data:
                                        type: object
                                        properties:
                                            HeroPopUpMenuProductsImage:
                                                type: string
                                                description: URL of the Products Image
                                            HeroPopUpMenuAasattiImage:
                                                type: string
                                                description: URL of the Aasatti Image
                                            HeroPopUpMenuInspirationImage:
                                                type: string
                                                description: URL of the Inspiration Image
                                            HeroPopUpMenuProjectsImage:
                                                type: string
                                                description: URL of the Projects Image
                                            HeroPopUpMenuResourcesImage:
                                                type: string
                                                description: URL of the Resources Image
                                            HeroPopUpMenuCompanyImage:
                                                type: string
                                                description: URL of the Company Image
                                            HeroPopUpMenuContactImage:
                                                type: string
                                                description: URL of the Contact Image
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'

        put:
            summary: Update Hero Section Popup Menu
            operationId: updateHeroSectionPopupMenu
            tags:
                - Home
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                HeroPopUpMenuProductsImage:
                                    type: string
                                    description: URL of the Products Image
                                HeroPopUpMenuAasattiImage:
                                    type: string
                                    description: URL of the Aasatti Image
                                HeroPopUpMenuInspirationImage:
                                    type: string
                                    description: URL of the Inspiration Image
                                HeroPopUpMenuProjectsImage:
                                    type: string
                                    description: URL of the Projects Image
                                HeroPopUpMenuResourcesImage:
                                    type: string
                                    description: URL of the Resources Image
                                HeroPopUpMenuCompanyImage:
                                    type: string
                                    description: URL of the Company Image
                                HeroPopUpMenuContactImage:
                                    type: string
                                    description: URL of the Contact Image
                            required:
                                - HeroPopUpMenuProductsImage
                                - HeroPopUpMenuAasattiImage
                                - HeroPopUpMenuInspirationImage
                                - HeroPopUpMenuProjectsImage
                                - HeroPopUpMenuResourcesImage
                                - HeroPopUpMenuCompanyImage
                                - HeroPopUpMenuContactImage
            responses:
                '201':
                    description: The Hero Section Popup Menu was successfully updated
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'
    /home/<USER>
        get:
            summary: View Home Introducing Menu
            operationId: viewHomeIntroducingMenu
            tags:
                - Home
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    success:
                                        type: boolean
                                    message:
                                        type: string
                                    data:
                                        type: object
                                        properties:
                                            introducingTitle:
                                                type: string
                                                description: Title of home introducing
                                            introducingCopy:
                                                type: string
                                                description: Caption of home introducing
                                            introducingImage1:
                                                type: string
                                                description: URL of the introducing Image 1
                                            introducingImage2:
                                                type: string
                                                description: URL of the introducing Image 2
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'

        put:
            summary: Update Home Introducing Menu
            operationId: updateHomeIntroducingMenu
            tags:
                - Home
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                introducingTitle:
                                    type: string
                                    description: Title of home introducing
                                introducingCopy:
                                    type: string
                                    description: Caption of home introducing
                                introducingImage1:
                                    type: string
                                    description: URL of the introducing Image 1
                                introducingImage2:
                                    type: string
                                    description: URL of the introducing Image 2
                            required:
                                - introducingImage1
                                - introducingImage2
            responses:
                '201':
                    description: The Home Introducing Menu was successfully updated
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'
    /home/<USER>
        post:
          tags:
            - Home
          summary: Add or update Home Inspiration section
          description: Add or update the main title and copy for the Home Inspiration section.
          operationId: addOrUpdateHomeInspiration
          security:
            - ApiKeyAuth: []
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'  
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    InspirationMainTitle:
                      type: string
                      example: "Our Inspirations"
                    InspriationMainCopy:
                      type: string
                      example: "Discover the inspirations behind our designs."
          responses:
            '200':
              description: Inspiration section updated successfully.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      data:
                        type: object
                        $ref: '#/components/schemas/HomeInspiration'
                      message:
                        type: string
                        example: INSPIRATION_SECTION_UPDATED_SUCCESSFULLY
            '500':
              description: Internal Server Error
              content:
                application/json:
                  schema:
                    $ref: '#/components/responses/ServerError'
    
        get:
          tags:
            - Home
          summary: View Home Inspiration section
          description: Fetch the Home Inspiration section details.
          operationId: viewHomeInspiration
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
          responses:
            '200':
              description: Inspiration section fetched successfully.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      data:
                        type: object
                        $ref: '#/components/schemas/HomeInspiration'
                      message:
                        type: string
                        example: INSPIRATION_SECTION_FETCHED_SUCCESSFULLY
            '404':
              description: Inspiration section not found.
              content:
                application/json:
                  schema:
                    $ref: '#/components/responses/BadRequestError'
            '500':
              description: Internal Server Error
              content:
                application/json:
                  schema:
                    $ref: '#/components/responses/ServerError'
                    
    /home/<USER>
        post:
          tags:
            - Home
          summary: Add a new carousel item to Home Inspiration
          description: Add a new item to the carousel in the Home Inspiration section.
          operationId: addHomeInspirationCarousel
          security:
            - ApiKeyAuth: []
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'  
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    InspirationTitle:
                      type: string
                      example: "Nature's Beauty"
                    Scene1Image:
                      type: string
                      example: "https://example.com/scene1.jpg"
                    Scene2Image:
                      type: string
                      example: "https://example.com/scene2.jpg"
                    Scene3Image:
                      type: string
                      example: "https://example.com/scene3.jpg"
                    Scene4Image:
                      type: string
                      example: "https://example.com/scene4.jpg"
                    Scene5Image:
                      type: string
                      example: "https://example.com/scene5.jpg"
                    isActive:
                      type: boolean
                      example: true
          responses:
            '200':
              description: Carousel item added successfully.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      data:
                        type: object
                        $ref: '#/components/schemas/HomeInspiration'
                      message:
                        type: string
                        example: CAROUSEL_ITEM_ADDED_SUCCESSFULLY
            '500':
              description: Internal Server Error
              content:
                application/json:
                  schema:
                    $ref: '#/components/responses/ServerError'                
               
    /home/<USER>
        put:
          tags:
            - Home
          summary: Update a carousel item in Home Inspiration
          description: Update an existing carousel item in the Home Inspiration section.
          operationId: updateHomeInspirationAllCarousel
          security:
            - ApiKeyAuth: []
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    InspirationTitle:
                      type: string
                      example: "Nature's Wonders"
                    Scene1Image:
                      type: string
                      example: "https://example.com/scene1-updated.jpg"
                    Scene2Image:
                      type: string
                      example: "https://example.com/scene2-updated.jpg"
                    Scene3Image:
                      type: string
                      example: "https://example.com/scene3-updated.jpg"
                    Scene4Image:
                      type: string
                      example: "https://example.com/scene4-updated.jpg"
                    Scene5Image:
                      type: string
                      example: "https://example.com/scene5-updated.jpg"
                    isActive:
                      type: boolean
                      example: true
                    sortOrder:
                      type: integer
                      example: 2
          responses:
            '200':
              description: Carousel item updated successfully.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      data:
                        type: object
                        $ref: '#/components/schemas/HomeInspiration'
                      message:
                        type: string
                        example: CAROUSEL_ITEM_UPDATED_SUCCESSFULLY
            '404':
              description: Carousel item or section not found.
              content:
                application/json:
                  schema:
                    $ref: '#/components/responses/BadRequestError'
            '500':
              description: Internal Server Error
              content:
                application/json:
                  schema:
                    $ref: '#/components/responses/ServerError'
    /home/<USER>/{carouselId}:
        put:
          tags:
            - Home
          summary: Update a carousel item in Home Inspiration
          description: Update an existing carousel item in the Home Inspiration section.
          operationId: updateHomeInspirationCarousel
          security:
            - ApiKeyAuth: []
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: carouselId
              in: path
              required: true
              description: ID of the carousel item to update
              schema:
                type: string
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    InspirationTitle:
                      type: string
                      example: "Nature's Wonders"
                    Scene1Image:
                      type: string
                      example: "https://example.com/scene1-updated.jpg"
                    Scene2Image:
                      type: string
                      example: "https://example.com/scene2-updated.jpg"
                    Scene3Image:
                      type: string
                      example: "https://example.com/scene3-updated.jpg"
                    Scene4Image:
                      type: string
                      example: "https://example.com/scene4-updated.jpg"
                    Scene5Image:
                      type: string
                      example: "https://example.com/scene5-updated.jpg"
                    isActive:
                      type: boolean
                      example: true
                    sortOrder:
                      type: integer
                      example: 2
          responses:
            '200':
              description: Carousel item updated successfully.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      data:
                        type: object
                        $ref: '#/components/schemas/HomeInspiration'
                      message:
                        type: string
                        example: CAROUSEL_ITEM_UPDATED_SUCCESSFULLY
            '404':
              description: Carousel item or section not found.
              content:
                application/json:
                  schema:
                    $ref: '#/components/responses/BadRequestError'
            '500':
              description: Internal Server Error
              content:
                application/json:
                  schema:
                    $ref: '#/components/responses/ServerError'
                    
                    
    /home/<USER>
        post:
          tags:
            - Home
          summary: Add or update Home Inspiration section
          description: Add or update the main title and copy for the Home Inspiration section.
          operationId: addOrUpdateHomeProjects
          security:
            - ApiKeyAuth: []
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'  
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    HomeProjectsMainTitle:
                      type: string
                      example: "Our Projects"
          responses:
            '200':
              description: Projects section updated successfully.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      data:
                        type: object
                        $ref: '#/components/schemas/HomeInspiration'
                      message:
                        type: string
                        example: INSPIRATION_SECTION_UPDATED_SUCCESSFULLY
            '500':
              description: Internal Server Error
              content:
                application/json:
                  schema:
                    $ref: '#/components/responses/ServerError'
    
        get:
          tags:
            - Home
          summary: View Home Project section
          description: Fetch the Home Project section details.
          operationId: viewHomeProjects
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
          responses:
            '200':
              description: Project section fetched successfully.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      data:
                        type: object
                        $ref: '#/components/schemas/HomeInspiration'
                      message:
                        type: string
                        example: PROJECT_SECTION_FETCHED_SUCCESSFULLY
            '404':
              description: Inspiration section not found.
              content:
                application/json:
                  schema:
                    $ref: '#/components/responses/BadRequestError'
            '500':
              description: Internal Server Error
              content:
                application/json:
                  schema:
                    $ref: '#/components/responses/ServerError'
                    
    /home/<USER>
        post:
          tags:
            - Home
          summary: Add a new carousel item to Home Inspiration
          description: Add a new item to the carousel in the Home Inspiration section.
          operationId: addHomeProjectTiles
          security:
            - ApiKeyAuth: []
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'  
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    HomeProjectTitle:
                      type: string
                      example: "Nature's Wonders"
                    HomeProjectDescription:
                      type: string
                      example: "https://example.com/scene1-updated.jpg"
                    HomeProjectImage:
                      type: string
                      example: "https://example.com/scene2-updated.jpg"
                    HomeProjectURL:
                      type: string
                      example: "https://example.com/scene3-updated.jpg"
                    isActive:
                      type: boolean
                      example: true
                    sortOrder:
                      type: integer
                      example: 2
          responses:
            '200':
              description: Tile added successfully.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      data:
                        type: object
                      message:
                        type: string
                        example: CAROUSEL_ITEM_ADDED_SUCCESSFULLY
            '500':
              description: Internal Server Error
              content:
                application/json:
                  schema:
                    $ref: '#/components/responses/ServerError'                
               
    /home/<USER>
        put:
          tags:
            - Home
          summary: Update a carousel item in Home Projects
          description: Update an existing carousel item in the Home Projects section.
          operationId: updateHomeProjectsAllCarousel
          security:
            - ApiKeyAuth: []
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    HomeProjectTitle:
                      type: string
                      example: "Nature's Wonders"
                    HomeProjectDescription:
                      type: string
                      example: "https://example.com/scene1-updated.jpg"
                    HomeProjectImage:
                      type: string
                      example: "https://example.com/scene2-updated.jpg"
                    HomeProjectURL:
                      type: string
                      example: "https://example.com/scene3-updated.jpg"
                    isActive:
                      type: boolean
                      example: true
                    sortOrder:
                      type: integer
                      example: 2
          responses:
            '200':
              description: Carousel item updated successfully.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      data:
                        type: object
                        $ref: '#/components/schemas/HomeInspiration'
                      message:
                        type: string
                        example: CAROUSEL_ITEM_UPDATED_SUCCESSFULLY
            '404':
              description: Carousel item or section not found.
              content:
                application/json:
                  schema:
                    $ref: '#/components/responses/BadRequestError'
            '500':
              description: Internal Server Error
              content:
                application/json:
                  schema:
                    $ref: '#/components/responses/ServerError'
    /home/<USER>/{id}:
        put:
          tags:
            - Home
          summary: Update a carousel item in Home Projects
          description: Update an existing carousel item in the Home Projects section.
          operationId: updateHomeProjectsCarousel
          security:
            - ApiKeyAuth: []
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: id
              in: path
              required: true
              description: ID of the carousel item to update
              schema:
                type: string
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    HomeProjectTitle:
                      type: string
                      example: "Nature's Wonders"
                    HomeProjectDescription:
                      type: string
                      example: "https://example.com/scene1-updated.jpg"
                    HomeProjectImage:
                      type: string
                      example: "https://example.com/scene2-updated.jpg"
                    HomeProjectURL:
                      type: string
                      example: "https://example.com/scene3-updated.jpg"
                    isActive:
                      type: boolean
                      example: true
                    sortOrder:
                      type: integer
                      example: 2
          responses:
            '200':
              description: Carousel item updated successfully.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      data:
                        type: object
                        $ref: '#/components/schemas/HomeInspiration'
                      message:
                        type: string
                        example: CAROUSEL_ITEM_UPDATED_SUCCESSFULLY
            '404':
              description: Carousel item or section not found.
              content:
                application/json:
                  schema:
                    $ref: '#/components/responses/BadRequestError'
            '500':
              description: Internal Server Error
              content:
                application/json:
                  schema:
                    $ref: '#/components/responses/ServerError'                
                
    /home/<USER>
        get:
            summary: View Our Commitments Section
            operationId: viewOurCommitments
            tags:
                - Home
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'
        post:
            summary: Add Our Commitments Section
            operationId: addOurCommitments
            tags:
                - Home
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                ourCommitments:
                                    type: object
                                    properties:
                                        HomeCommitmentImage:
                                            type: string
                                            description: URL of the image
                                        linkUrl:
                                            type: string
                                        HomeCommitmentImageMobi:
                                            type: string
                                            description: URL of the image
                                        linkUrlMobi:
                                            type: string    
                            required:
                                - ourCommitments
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'

        put:
            summary: Update Our Commitments Section
            operationId: updateOurCommitments
            tags:
                - Home
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                ourCommitments:
                                    type: object
                                    properties:
                                        HomeCommitmentImage:
                                            type: string
                                            description: URL of the image
                                        linkUrl:
                                            type: string
                                        HomeCommitmentImageMobi:
                                            type: string
                                            description: URL of the image
                                        linkUrlMobi:
                                            type: string    
                            required:
                                - ourCommitments
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /home/<USER>
        get:
            summary: View Contact Us Banner Section
            operationId: viewContactUsBanner
            tags:
                - Home
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'
        post:
            summary: Add Contact Us Banner Section
            operationId: addContactUsBanner
            tags:
                - Home
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                contactUsBanner:
                                    type: object
                                    properties:
                                        bannerImage:
                                            type: string
                                            description: URL of the banner image
                                        bannerImageMobi:
                                            type: string
                                            description: URL of the banner image    
                                        text:
                                            type: string
                                        link:
                                            type: string
                            required:
                                - contactUsBanner
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'

        put:
            summary: Update Contact Us Banner Section
            operationId: updateContactUsBanner
            tags:
                - Home
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                contactUsBanner:
                                    type: object
                                    properties:
                                        bannerImage:
                                            type: string
                                            description: URL of the banner image
                                        bannerImageMobi:
                                            type: string
                                            description: URL of the banner image    
                                        text:
                                            type: string
                                        link:
                                            type: string
                            required:
                                - contactUsBanner
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /home/<USER>
        get:
            summary: View Footer Content
            operationId: viewFooterContent
            tags:
                - Home
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'
        put:
            summary: Update Footer Content
            operationId: updateFooterContent
            tags:
                - Home
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                FooterSocialMediaInstaURL:
                                    type: string
                                    description: URL of the Instagram page
                                FooterSocialMediaXURL:
                                    type: string
                                    description: URL of the X (formerly Twitter) page
                                FooterSocialMediaFBURL:
                                    type: string
                                    description: URL of the Facebook page
                                FooterSocialMediaLinkedINURL:
                                    type: string
                                    description: URL of the LinkedIn page
                                FooterPrivacyURL:
                                    type: string
                                    description: URL of the Privacy Policy page
                                FooterWarrantyURL:
                                    type: string
                                    description: URL of the Warranty page
                                FooterTermsURL:
                                    type: string
                                    description: URL of the Terms and Conditions page
                                FooterCopyrightURL:
                                    type: string
                                    description: URL of the Copyright page
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'
                    
                    
    /inspirations/banner:
        get:
          tags:
            - Inspiration
          summary: Get the banner for an inspiration
          description: Fetch the banner details for the inspiration section
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
          responses:
            200:
              description: Banner fetched successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      data:
                        type: object
                        properties:
                          mediaType:
                            type: string
                          fileTitle:
                            type: string
                          fileName:
                            type: string
                          mediaTypeMobi:
                            type: string
                          fileTitleMobi:
                            type: string
                          fileNameMobi:
                            type: string
                      message:
                        type: string
            404:
              description: Banner not found
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string
            500:
              description: Internal server error
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string
        post:
          tags:
            - Inspiration
          summary: Add a new banner to an inspiration
          description: Add a banner on inspiration
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    mediaType:
                      type: string
                    fileTitle:
                      type: string
                    fileName:
                      type: string
                    mediaTypeMobi:
                      type: string
                    fileTitleMobi:
                      type: string
                    fileNameMobi:
                      type: string  
          responses:
            200:
              description: Scene added successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      data:
                        type: object
                      message:
                        type: string
            500:
              description: Internal server error
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string     
    
    /inspirations:
        get:
          tags:
            - Inspiration
          summary: Retrieve all inspirations
          description: Fetch all inspirations.
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: FilterProjectType
              in: query
              required: false
              description: Filter by project type IDs (comma-separated)
              schema:
                type: string  
          responses:
            200:
              description: Inspirations fetched successfully.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Inspiration'
                      message:
                        type: string
                        example: "INSPIRATIONS_FETCHED_SUCCESSFULLY"
            500:
              description: Internal server error.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: false
                      message:
                        type: string
                        example: "INTERNAL_SERVER_ERROR"
      
        post:
          tags:
            - Inspiration
          summary: Add or update an inspiration
          description: Add or update an inspiration.
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/Inspiration'
          responses:
            200:
              description: Inspiration added or updated successfully.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      data:
                        $ref: '#/components/schemas/Inspiration'
                      message:
                        type: string
                        example: "INSPIRATION_CREATED_SUCCESSFULLY"
            400:
              description: Bad request.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: false
                      message:
                        type: string
                        example: "Invalid input parameters"
            500:
              description: Internal server error.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: false
                      message:
                        type: string
                        example: "INTERNAL_SERVER_ERROR"               
    
    /inspirations/{id}:
        get:
          tags:
            - Inspiration
          summary: Get an inspiration by ID
          description: Fetch an inspiration by its ID.
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: id
              in: path
              required: true
              schema:
                type: string
              description: ID of the inspiration to fetch.
          responses:
            200:
              description: Inspiration fetched successfully.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      data:
                        $ref: '#/components/schemas/Inspiration'
                      message:
                        type: string
                        example: "INSPIRATION_FETCHED_SUCCESSFULLY"
            404:
              description: Inspiration not found.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: false
                      message:
                        type: string
                        example: "INSPIRATION_NOT_FOUND"
            500:
              description: Internal server error.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: false
                      message:
                        type: string
                        example: "INTERNAL_SERVER_ERROR"
      
        patch:
          tags:
            - Inspiration
          summary: Update an inspiration by ID
          description: Update an existing inspiration by its ID. Only fields provided in the request body will be updated.
          parameters:
            - name: id
              in: path
              required: true
              schema:
                type: string
              description: ID of the inspiration to update.
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    videoFileName:
                      type: string
                      description: Video file name of the inspiration.
                    isActive:
                      type: boolean
                      description: Whether the inspiration is active.
                    sortOrder:
                      type: integer
                      description: The order in which the inspiration appears.
                    scenes:
                      type: array
                      items:
                        $ref: '#/components/schemas/Scene'
                    circadianLighting:
                      $ref: '#/components/schemas/CircadianLighting'
                    colour:
                      $ref: '#/components/schemas/Colour'
                    builtWithAASATTI:
                      $ref: '#/components/schemas/BuiltWithAASATTI'
          responses:
            200:
              description: Inspiration updated successfully.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      data:
                        $ref: '#/components/schemas/Inspiration'
                      message:
                        type: string
                        example: "INSPIRATION_UPDATED_SUCCESSFULLY"
            404:
              description: Inspiration not found.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: false
                      message:
                        type: string
                        example: "INSPIRATION_NOT_FOUND"
            400:
              description: Bad request, invalid input parameters.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: false
                      message:
                        type: string
                        example: "Invalid input parameters"
            500:
              description: Internal server error.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: false
                      message:
                        type: string
        
        delete:
          tags:
            - Inspiration
          summary: Delete an inspiration by ID
          description: Delete a specific inspiration by its ID.
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: id
              in: path
              required: true
              schema:
                type: string
              description: ID of the inspiration to delete.
          responses:
            200:
              description: Inspiration deleted successfully.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      message:
                        type: string
                        example: "INSPIRATION_DELETED_SUCCESSFULLY"
            404:
              description: Inspiration not found.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: false
                      message:
                        type: string
                        example: "INSPIRATION_NOT_FOUND"
            500:
              description: Internal server error.
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: false
                      message:
                        type: string
                        example: "INTERNAL_SERVER_ERROR"
    
                        
    /inspirations/{id}/scene:
        post:
          tags:
            - Inspiration
          summary: Add a new scene to an inspiration
          description: Add a scene to an existing inspiration by its ID.
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - in: path
              name: id
              required: true
              schema:
                type: string
              description: The ID of the inspiration to add the scene to
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    mediaType:
                      type: string
                    buttonLabel:
                      type: string
                    fileName:
                      type: string
          responses:
            200:
              description: Scene added successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      data:
                        type: object
                      message:
                        type: string
            500:
              description: Internal server error
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string                    
                    
    /inspirations/{id}/scene/{sceneId}:
        put:
          tags:
            - Inspiration
          summary: Update a specific scene in an inspiration
          description: Update an existing scene of an inspiration by the scene's ID.
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - in: path
              name: id
              required: true
              schema:
                type: string
              description: The ID of the inspiration
            - in: path
              name: sceneId
              required: true
              schema:
                type: string
              description: The ID of the scene to update
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    mediaType:
                      type: string
                    buttonLabel:
                      type: string
                    fileName:
                      type: string
          responses:
            200:
              description: Scene updated successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      data:
                        type: object
                      message:
                        type: string
            500:
              description: Internal server error
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string
    
        delete:
          tags:
            - Inspiration
          summary: Delete a specific scene in an inspiration
          description: Delete a scene from an inspiration by the scene's ID.
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - in: path
              name: id
              required: true
              schema:
                type: string
              description: The ID of the inspiration
            - in: path
              name: sceneId
              required: true
              schema:
                type: string
              description: The ID of the scene to delete
          responses:
            200:
              description: Scene deleted successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string
            404:
              description: Scene not found
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string
            500:
              description: Internal server error
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string                

    /users/profile:
        get:
            summary: get user profile
            operationId: getProfile
            tags:
                - User
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: id
                  in: query
                  description: ObjectId of user
                  required: false
                  schema:
                      type: string
                      format: objectId
            responses:
                200:
                    description: User profile
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                properties:
                                    data:
                                        $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'
        put:
            summary: update user profile
            operationId: updateProfile
            tags:
                - User
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            type: 'PROFILE_UPDATE'
                            fullName: 'Girish Sharma'
                            age: '22'
                            userName: 'girish.s'
                            bio: 'my bio'
                            description: 'my description'
                            avatar: ''
                        schema:
                            type: object
                            required:
                                - type
                                - fullName
                                - age
                                - userName
                            properties:
                                type:
                                    type: string
                                    description: Available update type i.e. PROFILE_UPDATE, ACCOUNT_COMPLETE,
                                fullName:
                                    type: string
                                    description: Full name of user
                                age:
                                    type: string
                                    description: Age of user
                                userName:
                                    type: string
                                    description: user-name of user
                                bio:
                                    type: string
                                    description: Bio of user
                                description:
                                    type: string
                                    description: Description of user
                                avatar:
                                    type: string
                                    description: Profile picture of user
            responses:
                200:
                    description: Message indicating profile has been updated successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                properties:
                                    data:
                                        $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'
    /users/password:
        put:
            summary: update user pasword
            operationId: updatePassword
            tags:
                - User
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            currentPassword: 'asdf@1234'
                            newPassword: 'asdf@12345'
                        schema:
                            type: object
                            required:
                                - currentPassword
                                - newPassword
                            properties:
                                currentPassword:
                                    type: string
                                    description: Current password of registered user
                                newPassword:
                                    type: string
                                    format: password
                                    description: Password of user
            responses:
                200:
                    description: Message indicating password changed successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                400:
                    $ref: '#/components/responses/BadRequestError'
    /users/email:
        put:
            summary: update user email
            operationId: updateEmail
            tags:
                - User
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            currentPassword: 'asdf@1234'
                            email: '<EMAIL>'
                        schema:
                            type: object
                            required:
                                - currentPassword
                                - email
                            properties:
                                currentPassword:
                                    type: string
                                    description: Current password of registered user
                                email:
                                    type: string
                                    description: Email of user
            responses:
                200:
                    description: Message indicating email updated successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                properties:
                                    data:
                                        $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'
    /users/phone:
        put:
            summary: update user phone
            operationId: updatePhone
            tags:
                - User
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            currentPassword: 'asdf@1234'
                            countryCode: '+1'
                            phone: '0001112223'
                        schema:
                            type: object
                            required:
                                - currentPassword
                                - countryCode
                                - phone
                            properties:
                                currentPassword:
                                    type: string
                                    description: Current password of registered user
                                countryCode:
                                    type: string
                                    description: Country code of user
                                phone:
                                    type: string
                                    description: Phone of user
            responses:
                200:
                    description: Message indicating phone updated successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                properties:
                                    data:
                                        $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'
    /users/notification-toggle:
        get:
            summary: turn on or off push notifications
            operationId: notificationToggle
            tags:
                - User
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                200:
                    description: Message indicating push notifications is on or off
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                properties:
                                    data:
                                        $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /whatsnew:
        get:
            summary: View What's New Section
            operationId: viewWhatsNewSection
            tags:
                - Whats-New
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'
        post:
            summary: Add a new WhatsNew entry
            operationId: addWhatsNewEntry
            tags:
                - Whats-New
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                whatsNew:
                                    type: object
                                    properties:
                                        whatsNewTitle:
                                            type: string
                                            description: Title of the WhatsNew section
                                        WhatsNewOverview:
                                            type: string
                                            description: Overview of the WhatsNew section
                            required:
                                - whatsNew
            responses:
                '201':
                    description: The WhatsNew entry was successfully created
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'

    /whatsnew/carousel:
        post:
            summary: Add a carousel item to an existing WhatsNew entry
            operationId: addWhatsNewCarousel
            tags:
                - Whats-New
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                image:
                                    type: string
                                    description: URL of the image
                                caption:
                                    type: string
                                    description: The caption text
                                connectLink:
                                    type: string
                                    description: The connection link
                                isActive:
                                    type: boolean
                                    description: Whether the carousel item is active
                                sortOrder:
                                    type: integer
                                    description: The order of the carousel item
                            required:
                                - carousel
            responses:
                '201':
                    description: The carousel item was successfully added
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: The WhatsNew entry was not found
                '500':
                    $ref: '#/components/responses/ServerError'

    /whatsnew/carousel/{carouselId}:
        put:
            summary: Update a carousel item in an existing WhatsNew entry
            operationId: updateWhatsNewCarousel
            tags:
                - Whats-New
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: carouselId
                  in: path
                  required: true
                  schema:
                      type: string
                  description: The ID of the carousel item
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                image:
                                    type: string
                                    description: URL of the image
                                caption:
                                    type: string
                                    description: The caption text
                                connectLink:
                                    type: string
                                    description: The connection link
                                isActive:
                                    type: boolean
                                    description: Whether the carousel item is active
                                sortOrder:
                                    type: integer
                                    description: The order of the carousel item
                            required:
                                - carousel
            responses:
                '200':
                    description: The carousel item was successfully updated
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: The WhatsNew entry or carousel item was not found
                '500':
                    $ref: '#/components/responses/ServerError'
        delete:
            summary: Delete a carousel item from an existing WhatsNew entry
            operationId: deleteWhatsNewCarousel
            tags:
                - Whats-New
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: carouselId
                  in: path
                  required: true
                  schema:
                      type: string
                  description: The ID of the carousel item to delete
            responses:
                '200':
                    description: The carousel item was successfully deleted
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: The WhatsNew entry or carousel item was not found
                '500':
                    $ref: '#/components/responses/ServerError'            

    /pages/{slug}:
        get:
            summary: get static page
            operationId: getStaticPage
            tags:
                - Page
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: slug
                  in: path
                  description: slug for static page
                  required: true
                  schema:
                      type: string
                      enum:
                          - privacy-policy
                          - terms-conditions
                          - about-us
            responses:
                200:
                    description: static page data
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: object
                                              properties:
                                                  slug:
                                                      type: string
                                                  title:
                                                      type: string
                                                  description:
                                                      type: string
                400:
                    $ref: '#/components/responses/BadRequestError'
                    
    /pages/company:
        get:
          tags:
            - Page
          summary: Retrieve company data
          description: Fetch the complete company data including profile years, commitments, and other details.
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
          responses:
            200:
              description: Data fetched successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      data:
                        type: object
                      message:
                        type: string
            404:
              description: Data not found
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string
            500:
              description: Internal server error
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string 
                        
        put:
          tags:
            - Page
          summary: Create or update company data
          description: Create or update the company profile information including banners, profiles, commitments, and legal/career information.
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
          security:
                - ApiKeyAuth: []      
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    CompanyBannerTitle:
                      type: string
                    CompanyBannerFileName:
                      type: string
                    ProfileMainTitle:
                      type: string
                    ProfileMainCaption:
                      type: string
                    ProfileYears:
                      type: array
                      items:
                        type: object
                    VisionMissionValuesImageFileName:
                      type: string
                    VisionImageFileName:
                      type: string
                    MissionImageFileName:
                      type: string
                    ValuesImageFileName:
                      type: string  
                    MissionImageFileNameMobi:
                      type: string
                    BrandsBannerImageFileName:
                      type: string
                    Commitments:
                      type: array
                      items:
                        type: object
                    CompanyLegalTitle:
                      type: string
                    CompanyLegalImageFileName:
                      type: string
                    CompanyLegalURL:
                      type: string
                    CompanyCareersTitle:
                      type: string
                    CompanyCareersImageFileName:
                      type: string
                    CompanyCareersURL:
                      type: string
          responses:
            200:
              description: Data updated successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      data:
                        type: object
                      message:
                        type: string
            500:
              description: Internal server error
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string  
                        
    /pages/company/profile-year:
        post:
          tags:
            - Page
          summary: Add a profile year to the company data
          description: Add a new profile year entry to the company's profile.
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
          security:
                - ApiKeyAuth: []      
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    ProfileYearImage:
                      type: string
                    ProfileYearLabel:
                      type: string
                    ProfileYearCaption:
                      type: string
          responses:
            200:
              description: Profile year added successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      data:
                        type: object
                      message:
                        type: string
            500:
              description: Internal server error
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string      
                        
    /pages/company/commitment:
        post:
          tags:
            - Page
          summary: Add a commitment to the company data
          description: Add a new commitment entry to the company's data.
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
          security:
                - ApiKeyAuth: []   
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    title:
                      type: string
                    overviewImageFileName:
                      type: string
                    detailImageFileName:
                      type: string
                    label:
                      type: string
                    sortOrder:
                      type: integer
          responses:
            200:
              description: Company commitment added successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      data:
                        type: object
                      message:
                        type: string
            500:
              description: Internal server error
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string
                        
    /pages/company/profile-year/{id}:
        delete:
          tags:
            - Page
          summary: Delete a profile year from the company data
          description: Delete a specific profile year entry by its ID.
          security:
                - ApiKeyAuth: []
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - in: path
              name: id
              required: true
              schema:
                type: string
              description: The ID of the profile year to delete
          responses:
            200:
              description: Profile year deleted successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string
            404:
              description: Profile year not found
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string
            500:
              description: Internal server error
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string
                    
    /pages/company/commitment/{id}:
        delete:
          tags:
            - Page
          summary: Delete a commitment from the company data
          description: Delete a specific commitment entry by its ID.
          security:
                - ApiKeyAuth: []
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - in: path
              name: id
              required: true
              schema:
                type: string
              description: The ID of the commitment to delete
          responses:
            200:
              description: Company commitment deleted successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string
            404:
              description: Company commitment not found
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string
            500:
              description: Internal server error
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string
                
                    
    /pages/contact:
        get:
            summary: View Contact Section
            operationId: getContactContent
            tags:
                - Page
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'
                    
        put:
          summary: Add or Update Contact Section
          operationId: addOrUpdateContact
          tags:
              - Page
          security:
              - ApiKeyAuth: []
          parameters:
              - $ref: '#/components/parameters/headerLanguage'
              - $ref: '#/components/parameters/headerPlatform'
              - $ref: '#/components/parameters/headerVersion'
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          type: object
                          properties:
                              ContactBannerType:
                                  type: string
                                  description: Type of the contact banner (image/video)
                                  enum: ['image', 'video']
                              ContactBannerTitle:
                                  type: string
                                  description: Title of the contact banner
                              ContactBannerFileName:
                                  type: string
                                  description: File name of the banner image or video
                              ContactCaption:
                                  type: string
                                  description: Caption for the contact section
                              ContactAddressLabel:
                                  type: string
                                  description: Label for the contact address
                              ContactAddress:
                                  type: string
                                  description: Contact address details
                              ContactServiceLabel:
                                  type: string
                                  description: Label for the service hours
                              ContactServiceHours:
                                  type: string
                                  description: Details of the service hours
                          required:
                              - ContactBannerType
                              - ContactBannerTitle
                              - ContactBannerFileName
                              - ContactCaption
                              - ContactAddressLabel
                              - ContactAddress
                              - ContactServiceLabel
                              - ContactServiceHours
          responses:
              '200':
                  description: The contact section was successfully updated
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '500':
                  $ref: '#/components/responses/ServerError'            
                    
    /pages/legal:
        get:
            summary: View Legal Section
            operationId: viewLegalSection
            tags:
                - Page
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'
                    
        put:
            summary: Add or Update Legal Section
            operationId: addOrUpdateLegalSection
            tags:
                - Page
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                LegalBannerType:
                                    type: string
                                    description: The type of banner (image/video)
                                    enum: ['image', 'video']
                                LegalBannerTitle:
                                    type: string
                                    description: Title of the legal section banner
                                LegalBannerFileName:
                                    type: string
                                    description: File name of the banner image or video
                                LegalDocs:
                                    type: array
                                    items:
                                        type: object
                                        properties:
                                            LegalDocLabel:
                                                type: string
                                                description: Label of the legal document
                                            LegalDocContent:
                                                type: string
                                                description: Content of the legal document
                                            LegalDocFileName:
                                                type: string
                                                description: File name of the legal document
                                            isActive:
                                                type: boolean
                                                description: Whether the legal document is active
                                            sortOrder:
                                                type: integer
                                                description: Sort order of the legal document
                            required:
                                - LegalBannerType
                                - LegalBannerTitle
                                - LegalBannerFileName
            responses:
                '200':
                    description: The legal section was successfully updated
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError' 
                    
    /pages/legal/docs:
        post:
            summary: Add a New Legal Document
            operationId: addLegalDocument
            tags:
                - Page
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                LegalDocLabel:
                                    type: string
                                    description: Label of the legal document
                                LegalDocContent:
                                    type: string
                                    description: Content of the legal document
                                LegalDocFileName:
                                    type: string
                                    description: File name of the legal document
                                isActive:
                                    type: boolean
                                    description: Whether the legal document is active
                            required:
                                - LegalDocLabel
                                - LegalDocContent
                                - LegalDocFileName
                                - isActive
            responses:
                '201':
                    description: The legal document was successfully added
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'                
                
                    
    /pages/resource:
        get:
            summary: Get resource content
            operationId: getResourceContent
            tags:
                - Page
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Success response with the resource content
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Resource content not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
                    
        put:
            summary: Add or update resource content
            operationId: addOrUpdateResource
            tags:
                - Page
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                ResourceBannerType:
                                    type: string
                                    description: Type of the resource banner
                                ResourcesBannerTitle:
                                    type: string
                                    description: Title of the resources banner
                                ResourcesBannerFileName:
                                    type: string
                                    description: Filename of the resources banner image
                                isTextVisible:
                                    type: boolean
                                    description: Whether the text is visible
                                pdfDriveUrl:
                                    type: string
                                    description: Whether the drive url update
                            required:
                                - ResourcesBannerTitle
                                - ResourcesBannerFileName
            responses:
                '200':
                    description: Resource content was successfully added or updated
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
                    
    /pages/projects:
        get:
            summary: Get project content
            operationId: getProjectContent
            tags:
                - Page
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Success response with the project content
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Project content not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
        put:
            summary: Add or update the project banner
            operationId: addUpdateProjectBanner
            tags:
                - Page
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                ProjectsBannerType:
                                    type: string
                                    description: Type of the project banner (image or video)
                                ProjectsBannerTitle:
                                    type: string
                                    description: Title of the project banner
                                ProjectsBannerFileName:
                                    type: string
                                    description: Filename of the project banner
                                isTextVisible:
                                    type: boolean
                                    description: Whether the banner text is visible
                            required:
                                - ProjectsBannerType
                                - ProjectsBannerTitle
                                - ProjectsBannerFileName
            responses:
                '200':
                    description: Project banner was successfully added or updated
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError' 
                    
    /projects/card:
        get:
          summary: Get all project cards
          operationId: viewProjectCards
          tags:
              - Page
          parameters:
              - $ref: '#/components/parameters/headerLanguage'
              - $ref: '#/components/parameters/headerPlatform'
              - $ref: '#/components/parameters/headerVersion'
          responses:
              '200':
                  description: Successfully retrieved the list of project cards
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '404':
                  description: No project cards found
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '500':
                  $ref: '#/components/responses/ServerError'
        post:
            summary: Add a new project card
            operationId: addProjectCard
            tags:
                - Page
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                mediaType:
                                    type: string
                                    description: Type of media (image or video)
                                ProjectImage:
                                    type: string
                                    description: URL of the project image
                                ProjectName:
                                    type: string
                                    description: Name of the project
                                ProjectOverview:
                                    type: string
                                    description: Overview of the project
                                ProjectURL:
                                    type: string
                                    description: URL link for the project
                                isActive:
                                    type: boolean
                                    description: Whether the project card is active
                                sortOrder:
                                    type: integer
                                    description: Sort order of the project card
                            required:
                                - mediaType
                                - ProjectImage
                                - ProjectName
            responses:
                '200':
                    description: Project card was successfully added
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
                    
    /projects/card/{id}:
        put:
            summary: Edit an existing project card
            operationId: editProjectCard
            tags:
                - Page
            security:
                - ApiKeyAuth: []
            parameters:
                - name: id
                  in: path
                  required: true
                  description: The ID of the project card to update
                  schema:
                      type: string
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                mediaType:
                                    type: string
                                    description: Type of media (image or video)
                                ProjectImage:
                                    type: string
                                    description: URL of the project image
                                ProjectName:
                                    type: string
                                    description: Name of the project
                                ProjectOverview:
                                    type: string
                                    description: Overview of the project
                                ProjectURL:
                                    type: string
                                    description: URL link for the project
                                isActive:
                                    type: boolean
                                    description: Whether the project card is active
                            required:
                                - mediaType
                                - ProjectName
            responses:
                '200':
                    description: Project card was successfully updated
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '404':
                    description: Project card not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '500':
                    $ref: '#/components/responses/ServerError'
        delete:
          summary: Delete an existing project card
          operationId: deleteProjectCard
          tags:
              - Page
          security:
              - ApiKeyAuth: []
          parameters:
              - name: id
                in: path
                required: true
                description: The ID of the project card to delete
                schema:
                    type: string
              - $ref: '#/components/parameters/headerLanguage'
              - $ref: '#/components/parameters/headerPlatform'
              - $ref: '#/components/parameters/headerVersion'
          responses:
              '200':
                  description: Project card was successfully deleted
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '404':
                  description: Project card not found
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              '500':
                  $ref: '#/components/responses/ServerError'      
                  
                  
    
    /projects/banner:
        get:
          tags:
            - Projects
          summary: Get the banner for an Projects
          description: Fetch the banner details for the Projects section
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
          responses:
            200:
              description: Banner fetched successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      data:
                        type: object
                        properties:
                          mediaType:
                            type: string
                          fileTitle:
                            type: string
                          fileName:
                            type: string
                          mediaTypeMobi:
                            type: string
                          fileTitleMobi:
                            type: string
                          fileNameMobi:
                            type: string
                      message:
                        type: string
            404:
              description: Banner not found
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string
            500:
              description: Internal server error
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string
        post:
          tags:
            - Projects
          summary: Add a new banner to an Projects
          description: Add a banner on Projects
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    mediaType:
                      type: string
                    fileTitle:
                      type: string
                    fileName:
                      type: string
                    mediaTypeMobi:
                      type: string
                    fileTitleMobi:
                      type: string
                    fileNameMobi:
                      type: string  
          responses:
            200:
              description: Scene added successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      data:
                        type: object
                      message:
                        type: string
            500:
              description: Internal server error
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string 
    
    /projects:
      get:
        summary: Get all projects with optional filters
        operationId: listProjects
        tags:
          - Projects
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: search
            in: query
            required: false
            description: Search by project title or description
            schema:
              type: string
          - name: isActive
            in: query
            required: false
            description: Filter projects by active status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: isFeatured
            in: query
            required: false
            description: Filter projects by featured status (true, false, or omit for all)
            schema:
              type: string
              enum: ['true', 'false']
          - name: minSortOrder
            in: query
            required: false
            description: Minimum sort order value
            schema:
              type: number
          - name: maxSortOrder
            in: query
            required: false
            description: Maximum sort order value
            schema:
              type: number
          - name: id
            in: query
            required: false
            description: Filter by project IDs (comma-separated)
            schema:
              type: string    
          - name: FilterProjectBrand
            in: query
            required: false
            description: Filter by project brand IDs (comma-separated)
            schema:
              type: string
          - name: FilterProjectType
            in: query
            required: false
            description: Filter by project type IDs (comma-separated)
            schema:
              type: string
          - name: FilterProjectApplication
            in: query
            required: false
            description: Filter by project application IDs (comma-separated)
            schema:
              type: string
          - name: FilterProjectSeries
            in: query
            required: false
            description: Filter by project series IDs (comma-separated)
            schema:
              type: string
          - name: FilterProjectProductFamily
            in: query
            required: false
            description: Filter by project product family IDs (comma-separated)
            schema:
              type: string
          - name: page
            in: query
            required: false
            description: Page number for pagination
            schema:
              type: number
              default: 1
          - name: limit
            in: query
            required: false
            description: Limit the number of results per page
            schema:
              type: number
              default: 10
        responses:
          '200':
            description: Successfully retrieved the list of projects
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: No projects found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'

      
      post:
          summary: Create a new project
          operationId: createProject
          tags:
            - Projects
          security:
            - ApiKeyAuth: []
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    featuredImage:
                      type: string
                      description: URL of the featured image
                    isFeatured:
                      type: boolean
                      description: Whether the project is featured
                    projectDetailBanner:
                      type: array
                      items:
                        type: object
                        properties:
                          mediaType:
                            type: string
                            enum: ['image', 'video']
                          fileName:
                            type: string
                            description: File name for the banner
                          fileTitle:
                            type: string
                            description: File name for the banner  
                    FilterProjectBrand:
                      type: array
                      items:
                        type: string
                        description: Brand filter IDs
                    FilterProjectType:
                      type: array
                      items:
                        type: string
                        description: Type filter IDs
                    FilterProjectApplication:
                      type: array
                      items:
                        type: string
                        description: Application filter IDs
                    FilterProjectSeries:
                      type: array
                      items:
                        type: string
                        description: Series filter IDs
                    FilterProjectProductFamily:
                      type: array
                      items:
                        type: string
                        description: Product family filter IDs
                    ProjectTitle:
                      type: string
                      description: Title of the project
                    ProjectDescriptionShort:
                      type: string
                      description: ProjectDescriptionShort of the project
                    ProjectDescriptionDetail:
                      type: string
                      description: ProjectDescriptionDetail of the project  
                    SocialMediaLinks:
                      type: object
                      description: Social media links for the project
                      properties:
                        instagram:
                          type: string
                          description: Instagram URL
                        x:
                          type: string
                          description: X (formerly Twitter) URL
                        facebook:
                          type: string
                          description: Facebook URL
                        linkedin:
                          type: string
                          description: LinkedIn URL
                    ProjectPictures:
                      type: array
                      items:
                        type: string
                        description: URLs of project pictures
                    ProjectTabs:
                      type: array
                      description: Tabs for the project
                      items:
                        type: object
                        properties:
                          tabTitle:
                            type: string
                            description: URL of the banner
                            example: "https://example.com/banner.jpg"
                          
                          banner:
                            type: array
                            description: Array of URLs for the banner
                            items:
                              type: object
                              properties:
                                mediaType:
                                  type: string
                                  enum: ['image', 'video']
                                mediaUrl:
                                  type: string
                                  description: File name for the banner
                          description:
                            type: string
                            description: Description for the tab
                          isActive:
                            type: boolean
                            description: Whether the tab is active
                          sortOrder:
                            type: integer
                            example: 1   
                    similarProjects:
                      type: array
                      description: Array of URLs for similar projects
                      items:
                        type: string
                        description: Valid URL of similar projects
                    isActive:
                      type: boolean
                      description: Whether the project is active
                  required:
                    - ProjectTitle
          responses:
            '200':
              description: Project created successfully
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '400':
              description: Missing required fields or invalid input
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/ApiResponse'
            '500':
              $ref: '#/components/responses/ServerError'

      
    /projects/{id}:
      put:
        summary: Edit an existing project
        operationId: editProject
        tags:
          - Projects
        security:
            - ApiKeyAuth: []  
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: ID of the project to be edited
            schema:
              type: string
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  featuredImage:
                    type: string
                    description: URL of the featured image
                  isFeatured:
                    type: boolean
                    description: Whether the project is featured
                  projectDetailBanner:
                      type: array
                      items:
                        type: object
                        properties:
                          mediaType:
                            type: string
                            enum: ['image', 'video']
                          fileName:
                            type: string
                            description: File name for the banner
                          fileTitle:
                            type: string
                            description: File name for the banner
                  FilterProjectBrand:
                    type: array
                    items:
                      type: string
                      description: Brand filter IDs
                  FilterProjectType:
                    type: array
                    items:
                      type: string
                      description: Type filter IDs
                  FilterProjectApplication:
                    type: array
                    items:
                      type: string
                      description: Application filter IDs
                  FilterProjectSeries:
                    type: array
                    items:
                      type: string
                      description: Series filter IDs
                  FilterProjectProductFamily:
                    type: array
                    items:
                      type: string
                      description: Product family filter IDs
                  ProjectTitle:
                    type: string
                    description: Title of the project
                  ProjectDescriptionShort:
                    type: string
                    description: Short description of the project
                  ProjectDescriptionDetail:
                    type: string
                    description: Detailed description of the project
                  SocialMediaLinks:
                    type: object
                    description: Social media links for the project
                    properties:
                      instagram:
                        type: string
                        description: Instagram URL
                      x:
                        type: string
                        description: X (formerly Twitter) URL
                      facebook:
                        type: string
                        description: Facebook URL
                      linkedin:
                        type: string
                        description: LinkedIn URL
                  ProjectPictures:
                    type: array
                    items:
                      type: string
                      description: URLs of project pictures
                  ProjectTabs:
                      type: array
                      description: Tabs for the project
                      items:
                        type: object
                        properties:
                          tabTitle:
                            type: string
                            description: URL of the banner
                            example: "https://example.com/banner.jpg"
                          
                          banner:
                            type: array
                            description: Array of URLs for the banner
                            items:
                              type: object
                              properties:
                                mediaType:
                                  type: string
                                  enum: ['image', 'video']
                                mediaUrl:
                                  type: string
                                  description: File name for the banner
                          description:
                            type: string
                            description: Description for the tab
                          isActive:
                            type: boolean
                            description: Whether the tab is active
                          sortOrder:
                            type: integer
                            example: 1   
                  similarProjects:
                    type: array
                    description: Array of URLs for similar projects
                    items:
                      type: string
                      description: Valid URL of similar projects
                  isActive:
                    type: boolean
                    description: Whether the project is active
                required:
                  - ProjectTitle
        responses:
          '200':
            description: Project updated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Missing required fields or invalid input
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Project not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
      
      delete:
        summary: Delete an existing project
        operationId: deleteProject
        tags:
          - Projects
        security:
            - ApiKeyAuth: []  
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: path
            required: true
            description: ID of the project to be deleted
            schema:
              type: string
        responses:
          '200':
            description: Project deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '404':
            description: Project not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'

    /products/banner:
        get:
          tags:
            - Products
          summary: Get the banner for an Products
          description: Fetch the banner details for the Products section
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
          responses:
            200:
              description: Banner fetched successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      data:
                        type: object
                        properties:
                          mediaType:
                            type: string
                          fileTitle:
                            type: string
                          fileName:
                            type: string
                          mediaTypeMobi:
                            type: string
                          fileTitleMobi:
                            type: string
                          fileNameMobi:
                            type: string
                      message:
                        type: string
            404:
              description: Banner not found
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string
            500:
              description: Internal server error
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string
        post:
          tags:
            - Products
          summary: Add a new banner to an Products
          description: Add a banner on Products
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    mediaType:
                      type: string
                    fileTitle:
                      type: string
                    fileName:
                      type: string
                    mediaTypeMobi:
                      type: string
                    fileTitleMobi:
                      type: string
                    fileNameMobi:
                      type: string  
          responses:
            200:
              description: Scene added successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      data:
                        type: object
                      message:
                        type: string
            500:
              description: Internal server error
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                      message:
                        type: string 
    
    /products:
      get:
        summary: Get a list of all product families
        operationId: listProductsFamily
        tags:
          - Products
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: id
            in: query
            description: Filter by product family IDs (comma-separated)
            required: false
            schema:
              type: string
          - name: search
            in: query
            description: Search for product families by title or description
            required: false
            schema:
              type: string
          - name: isActive
            in: query
            description: Filter by active status (`true` or `false`)
            required: false
            schema:
              type: string
              enum: ['true', 'false']
          - name: minSortOrder
            in: query
            description: Minimum sort order value
            required: false
            schema:
              type: integer
          - name: maxSortOrder
            in: query
            description: Maximum sort order value
            required: false
            schema:
              type: integer
          - name: page
            in: query
            description: Page number for pagination (default is 1)
            required: false
            schema:
              type: integer
              default: 1
          - name: limit
            in: query
            description: Number of items per page for pagination (default is 10)
            required: false
            schema:
              type: integer
              default: 10
          - name: getAllRecords
            in: query
            description: Set to `true` to fetch all records without pagination
            required: false
            schema:
              type: string
              enum: ['true', 'false']
              default: 'false'
          - name: FilterProductFamily
            in: query
            description: Filter by product family names (comma-separated)
            required: false
            schema:
              type: string
          - name: FilterBrand
            in: query
            description: Filter by brand names (comma-separated)
            required: false
            schema:
              type: string
          - name: FilterCategory
            in: query
            description: Filter by category names (comma-separated)
            required: false
            schema:
              type: string
          - name: FilterCollection
            in: query
            description: Filter by collection names (comma-separated)
            required: false
            schema:
              type: string
          - name: FilterMaterial
            in: query
            description: Filter by material names (comma-separated)
            required: false
            schema:
              type: string
          - name: FilterType
            in: query
            description: Filter by type names (comma-separated)
            required: false
            schema:
              type: string
          - name: FilterSeries
            in: query
            description: Filter by series names (comma-separated)
            required: false
            schema:
              type: string
          - name: FilterApplication
            in: query
            description: Filter by application names (comma-separated)
            required: false
            schema:
              type: string
          - name: sortBy
            in: query
            description: sort by 
            required: false
            schema:
              type: string
              enum: ['createdAt', 'sortOrder']
              default: 'createdAt'
          - name: sortOrder
            in: query
            description: sorting in asc order or desc order
            required: false
            schema:
              type: string
              enum: ['asc', 'desc']
              default: 'desc'
        responses:
          '200':
            description: Product families retrieved successfully
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
                      example: "PRODUCTS_FETCHED_SUCCESSFULLY"
                    data:
                      type: object
                      properties:
                        items:
                          type: array
                          items:
                            type: object
                            description: A product family object
                        count:
                          type: integer
                          description: Total number of product families
                        page:
                          type: integer
                          description: Current page number
                        limit:
                          type: integer
                          description: Number of items per page
          '500':
            description: Internal server error
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
                      example: "INTERNAL_SERVER_ERROR"
   
      post:
        summary: Add a new product
        operationId: addProduct
        tags:
          - Products
        security:
          - ApiKeyAuth: []
        parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  bannerSection:
                    type: object
                    properties:
                      banner:
                        type: array
                        items:
                          type: object
                          properties:
                            mediaType:
                              type: string
                              enum: ['image', 'video']
                            mediaUrl:
                              type: string
                      title:
                        type: string
                      isTitleVisible:
                            type: boolean  
                      familyName:
                        type: object
                        properties:
                          label:
                            type: string
                          isVisible:
                            type: boolean
                      modelsAndSpecs:
                        type: object
                        properties:
                          label:
                            type: string
                          isVisible:
                            type: boolean
                      downloads:
                        type: object
                        properties:
                          label:
                            type: string
                          isVisible:
                            type: boolean
                      itemConfigurator:
                        type: object
                        properties:
                          label:
                            type: string
                          isVisible:
                            type: boolean
                      similarProducts:
                        type: array
                        description: Array of URLs for similar products
                        items:
                          type: string
                          description: Valid URL of similar products      
                  productDetailFamilyRecordInternal:
                    type: object
                    properties:
                      family:
                        type: object
                        properties:
                          id:
                            type: string
                          name:
                            type: string
                      brand:
                        type: object
                        properties:
                          id:
                            type: string
                          name:
                            type: string
                      series:
                        type: object
                        properties:
                          id:
                            type: string
                          name:
                            type: string
                      category:
                        type: object
                        properties:
                          id:
                            type: string
                          name:
                            type: string
                      collections:
                        type: object
                        properties:
                          id:
                            type: string
                          name:
                            type: string
                      material:
                        type: object
                        properties:
                          id:
                            type: string
                          name:
                            type: string
                      itemCategory:
                        type: string
                      resourceImage:
                        type: object
                        properties:
                          mediaType:
                            type: string
                            enum: ['image', 'video']
                          mediaUrl:
                            type: string
                      productTileImage:
                        type: object
                        properties:
                          mediaType:
                            type: string
                            enum: ['image', 'video']
                          mediaUrl:
                            type: string
                      productHoverImage:
                        type: object
                        properties:
                          mediaType:
                            type: string
                            enum: ['image', 'video']
                          mediaUrl:
                            type: string
                      categoryTileImage:
                        type: object
                        properties:
                          mediaType:
                            type: string
                            enum: ['image', 'video']
                          mediaUrl:
                            type: string
                      categoryHoverImage:
                        type: object
                        properties:
                          mediaType:
                            type: string
                            enum: ['image', 'video']
                          mediaUrl:
                            type: string
                      collectionImage:
                        type: object
                        properties:
                          mediaType:
                            type: string
                            enum: ['image', 'video']
                          mediaUrl:
                            type: string
                      collectionHoverImage:
                        type: object
                        properties:
                          mediaType:
                            type: string
                            enum: ['image', 'video']
                          mediaUrl:
                            type: string
                      finishTileImage:
                        type: object
                        properties:
                          mediaType:
                            type: string
                            enum: ['image', 'video']
                          mediaUrl:
                            type: string
                      finishHoverImage:
                        type: object
                        properties:
                          mediaType:
                            type: string
                            enum: ['image', 'video']
                          mediaUrl:
                            type: string
                  BoardContainer:
                    type: array
                    items:
                      type: object
                      properties:
                        mediaType:
                          type: string
                          enum: ['image', 'video']
                        mediaUrl:
                          type: string
                        mediaTypeMobi:
                          type: string
                          enum: ['image', 'video']
                        mediaUrlMobi:
                          type: string  
                        isDelete:
                          type: boolean
                        isActive:
                          type: boolean
                        sortOrder:
                          type: number
                  productModels:
                    type: array
                    items:
                      type: object
                      properties:
                        title:
                          type: string
                        description:
                          type: string
                        modelSKU:
                          type: string
                        productModelImages:
                          type: array
                          items:
                            type: object
                            properties:
                              mediaType:
                                type: string
                                enum: ['image', 'video']
                              mediaUrl:
                                type: string
                              isDelete:
                                type: boolean
                              isActive:
                                type: boolean
                              sortOrder:
                                type: number
                        downloads:
                          type: object
                          properties:
                            specificationDownload:
                              type: object
                              properties:
                                fileName:
                                  type: string
                                isAvailable:
                                  type: boolean
                            installationDownload:
                              type: object
                              properties:
                                fileName:
                                  type: string
                                isAvailable:
                                  type: boolean
                            iesDownload:
                              type: object
                              properties:
                                fileName:
                                  type: string
                                isAvailable:
                                  type: boolean
                            cadDownload:
                              type: object
                              properties:
                                fileName:
                                  type: string
                                isAvailable:
                                  type: boolean
                            videoDownload:
                              type: object
                              properties:
                                fileName:
                                  type: string
                                isAvailable:
                                  type: boolean
                  downloads:
                    type: object
                    properties:
                      specificationDownload:
                        type: object
                        properties:
                          fileName:
                            type: string
                          isAvailable:
                            type: boolean
                      installationDownload:
                        type: object
                        properties:
                          fileName:
                            type: string
                          isAvailable:
                            type: boolean
                      iesDownload:
                        type: object
                        properties:
                          fileName:
                            type: string
                          isAvailable:
                            type: boolean
                      cadDownload:
                        type: object
                        properties:
                          fileName:
                            type: string
                          isAvailable:
                            type: boolean
                      videoDownload:
                        type: object
                        properties:
                          fileName:
                            type: string
                          isAvailable:
                            type: boolean
                      lookbookDownload:
                        type: object
                        properties:
                          fileName:
                            type: string
                          isAvailable:
                            type: boolean
                      catalogueDownload:
                        type: object
                        properties:
                          fileName:
                            type: string
                          isAvailable:
                            type: boolean 
                  sortOrder :
                    type: integer
                    description: Sort order of the product in the catalog. 1 is the first product in the catalog.
                    default: 1
        responses:
          '201':
            description: Product created successfully
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
                    data:
                      type: object
                      description: The newly created product details
          '400':
            description: Bad request
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
          '500':
            description: Internal server error
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
    
    /products/{productId}:
      get:
        summary: Get details of a specific product
        operationId: getProductDetail
        tags:
          - Products
        parameters:
          - name: productId
            in: path
            required: true
            description: The ID of the product
            schema:
              type: string
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        responses:
          '200':
            description: Product details retrieved successfully
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
                    data:
                      type: object
                      description: The product details
          '404':
            description: Product not found
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
          '500':
            description: Internal server error
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
      
      put:
        summary: Edit product family details
        operationId: editProductFamily
        tags:
          - Products
        security:
          - ApiKeyAuth: []
        parameters:
          - name: productId
            in: path
            required: true
            description: The ID of the product
            schema:
              type: string
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  sortOrder :
                    type: integer
                    description: Sort order of the product in the catalog. 1 is the first product in the catalog.
                    default: 1
                  bannerSection:
                    type: object
                    properties:
                      banner:
                        type: array
                        items:
                          type: object
                          properties:
                            mediaType:
                              type: string
                              enum: ['image', 'video']
                            mediaUrl:
                              type: string
                  # Other properties as per the create API
        responses:
          '200':
            description: Product family edited successfully
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
                    data:
                      type: object
                      description: The updated product details
          '400':
            description: Bad request
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
          '404':
            description: Product not found
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
          '500':
            description: Internal server error
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
    
    /{productId}/board-container:
      post:
        summary: Add a new board container (or board image) to a specific product
        operationId: addBoardContainer
        tags:
          - Products
        security:
          - ApiKeyAuth: []
        parameters:
          - name: productId
            in: path
            required: true
            description: The ID of the product
            schema:
              type: string
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  mediaType:
                    type: string
                    enum: ['image', 'video']
                    description: The media type (either image or video)
                  mediaUrl:
                    type: string
                    description: The URL of the media
                  sortOrder:
                    type: integer
                    description: The sorting order of the board container
                  isActive:
                    type: boolean
                    description: Whether the board container is active
                  isDelete:
                    type: boolean
                    description: Whether the board container is marked for deletion
        responses:
          '201':
            description: Board container added successfully
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
                    data:
                      type: object
                      description: The created board container
          '400':
            description: Bad request
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
          '404':
            description: Product not found
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
          '500':
            description: Internal server error
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
    
    /{productId}/board-container/{boardId}:
      put:
        summary: Update an existing board container (or board image) for a specific product
        operationId: updateBoardContainer
        tags:
          - Products
        security:
          - ApiKeyAuth: []
        parameters:
          - name: productId
            in: path
            required: true
            description: The ID of the product
            schema:
              type: string
          - name: boardId
            in: path
            required: true
            description: The ID of the board container to update
            schema:
              type: string
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  mediaType:
                    type: string
                    enum: ['image', 'video']
                    description: The media type (either image or video)
                  mediaUrl:
                    type: string
                    description: The URL of the media
                  mediaTypeMobi:
                    type: string
                    enum: ['image', 'video']
                    description: The media type (either image or video)
                  mediaUrlMobi:
                    type: string
                    description: The URL of the media  
                  sortOrder:
                    type: integer
                    description: The sorting order of the board container
                  isActive:
                    type: boolean
                    description: Whether the board container is active
                  isDelete:
                    type: boolean
                    description: Whether the board container is marked for deletion
        responses:
          '200':
            description: Board container updated successfully
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
                    data:
                      type: object
                      description: The updated board container
          '400':
            description: Bad request
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
          '404':
            description: Product or Board container not found
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
          '500':
            description: Internal server error
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
  
      delete:
        summary: Delete a board container (or board image) from a specific product
        operationId: deleteBoardContainer
        tags:
          - Products
        security:
          - ApiKeyAuth: []
        parameters:
          - name: productId
            in: path
            required: true
            description: The ID of the product
            schema:
              type: string
          - name: boardId
            in: path
            required: true
            description: The ID of the board container to delete
            schema:
              type: string
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        responses:
          '200':
            description: Board container deleted successfully
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
          '404':
            description: Product or Board container not found
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
          '500':
            description: Internal server error
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
    
    /{productId}/product-models:
      post:
        summary: Add a new product model to a specific product
        operationId: addProductModel
        tags:
          - Products
        security:
          - ApiKeyAuth: []
        parameters:
          - name: productId
            in: path
            required: true
            description: The ID of the product
            schema:
              type: string
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  title:
                    type: string
                    description: The title of the product model
                  description:
                    type: string
                    description: The description of the product model
                  modelSKU:
                    type: string
                    description: The SKU of the product model
                  productModelImages:
                    type: array
                    items:
                      type: object
                      properties:
                        mediaType:
                          type: string
                          enum: ['image', 'video']
                        mediaUrl:
                          type: string
                        isDelete:
                          type: boolean
                        isActive:
                          type: boolean
                        sortOrder:
                          type: integer
                  downloads:
                    type: object
                    properties:
                      specificationDownload:
                        type: object
                        properties:
                          fileName:
                            type: string
                          isAvailable:
                            type: boolean
                      installationDownload:
                        type: object
                        properties:
                          fileName:
                            type: string
                          isAvailable:
                            type: boolean
                      iesDownload:
                        type: object
                        properties:
                          fileName:
                            type: string
                          isAvailable:
                            type: boolean
                      cadDownload:
                        type: object
                        properties:
                          fileName:
                            type: string
                          isAvailable:
                            type: boolean
                      videoDownload:
                        type: object
                        properties:
                          fileName:
                            type: string
                          isAvailable:
                            type: boolean
                  isDelete:
                    type: boolean
                    description: Whether the product model is marked for deletion
                  isActive:
                    type: boolean
                    description: Whether the product model is active
                  sortOrder:
                    type: integer
                    description: The sorting order of the product model
        responses:
          '201':
            description: Product model added successfully
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
                    data:
                      type: object
                      description: The created product model
          '400':
            description: Bad request
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
          '404':
            description: Product not found
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
          '500':
            description: Internal server error
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
                      
    /{productId}/product-models/{modelId}:
      put:
        summary: Update an existing product model for a specific product
        operationId: updateProductModel
        tags:
          - Products
        security:
          - ApiKeyAuth: []
        parameters:
          - name: productId
            in: path
            required: true
            description: The ID of the product
            schema:
              type: string
          - name: modelId
            in: path
            required: true
            description: The ID of the product model to update
            schema:
              type: string
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  title:
                    type: string
                    description: The title of the product model
                  description:
                    type: string
                    description: The description of the product model
                  modelSKU:
                    type: string
                    description: The SKU of the product model
                  productModelImages:
                    type: array
                    items:
                      type: object
                      properties:
                        mediaType:
                          type: string
                          enum: ['image', 'video']
                        mediaUrl:
                          type: string
                        isDelete:
                          type: boolean
                        isActive:
                          type: boolean
                        sortOrder:
                          type: integer
                  downloads:
                    type: object
                    properties:
                      specificationDownload:
                        type: object
                        properties:
                          fileName:
                            type: string
                          isAvailable:
                            type: boolean
                      installationDownload:
                        type: object
                        properties:
                          fileName:
                            type: string
                          isAvailable:
                            type: boolean
                      iesDownload:
                        type: object
                        properties:
                          fileName:
                            type: string
                          isAvailable:
                            type: boolean
                      cadDownload:
                        type: object
                        properties:
                          fileName:
                            type: string
                          isAvailable:
                            type: boolean
                      videoDownload:
                        type: object
                        properties:
                          fileName:
                            type: string
                          isAvailable:
                            type: boolean
                  isDelete:
                    type: boolean
                    description: Whether the product model is marked for deletion
                  isActive:
                    type: boolean
                    description: Whether the product model is active
                  sortOrder:
                    type: integer
                    description: The sorting order of the product model
        responses:
          '200':
            description: Product model updated successfully
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
                    data:
                      type: object
                      description: The updated product model
          '400':
            description: Bad request
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
          '404':
            description: Product or Product model not found
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
          '500':
            description: Internal server error
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
  
      delete:
        summary: Delete a product model from a specific product
        operationId: deleteProductModel
        tags:
          - Products
        security:
          - ApiKeyAuth: []
        parameters:
          - name: productId
            in: path
            required: true
            description: The ID of the product
            schema:
              type: string
          - name: modelId
            in: path
            required: true
            description: The ID of the product model to delete
            schema:
              type: string
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        responses:
          '200':
            description: Product model deleted successfully
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
          '404':
            description: Product or Product model not found
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
          '500':
            description: Internal server error
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string

    
    /products/bulk-item-configuration:
      get:
        summary: Retrieve all item configurations
        operationId: listItemConfigurations
        tags:
          - Products
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: search
            in: query
            description: Search term to filter item configurations by brand, series, model, etc.
            required: false
            schema:
              type: string
          - name: brand
            in: query
            description: Filter by brand name
            required: false
            schema:
              type: string
          - name: series
            in: query
            description: Filter by series name
            required: false
            schema:
              type: string
          - name: category
            in: query
            description: Filter by category name
            required: false
            schema:
              type: string
          - name: itemCategory
            in: query
            description: Filter by item category (e.g., "ITEM_CATEGORY (FAMILY-MODEL)")
            required: false
            schema:
              type: string
          - name: family
            in: query
            description: Filter by family name
            required: false
            schema:
              type: string
          - name: model
            in: query
            description: Filter by model name
            required: false
            schema:
              type: string
          - name: modelSKU
            in: query
            description: Filter by model SKU
            required: false
            schema:
              type: string
          - name: application
            in: query
            description: Filter by application type (comma-separated values)
            required: false
            schema:
              type: string
          - name: modelSKUDescription
            in: query
            description: Filter by model SKU description (comma-separated values)
            required: false
            schema:
              type: string
          - name: cutsheetTitle
            in: query
            description: Filter by cutsheet title (comma-separated values)
            required: false
            schema:
              type: string
          - name: antiGlare
            in: query
            description: Filter by anti-glare option (comma-separated values)
            required: false
            schema:
              type: string
          - name: beamAngle
            in: query
            description: Filter by beam angle (comma-separated values)
            required: false
            schema:
              type: string
          - name: bend
            in: query
            description: Filter by bend type (comma-separated values)
            required: false
            schema:
              type: string
          - name: connectionCable
            in: query
            description: Filter by connection cable type (comma-separated values)
            required: false
            schema:
              type: string
          - name: driver
            in: query
            description: Filter by driver type (comma-separated values)
            required: false
            schema:
              type: string
          - name: finish
            in: query
            description: Filter by finish type (comma-separated values)
            required: false
            schema:
              type: string
          - name: length
            in: query
            description: Filter by length (comma-separated values)
            required: false
            schema:
              type: string
          - name: lens
            in: query
            description: Filter by lens type (comma-separated values)
            required: false
            schema:
              type: string
          - name: lightColour
            in: query
            description: Filter by light colour (comma-separated values)
            required: false
            schema:
              type: string
          - name: operatingVoltage
            in: query
            description: Filter by operating voltage (comma-separated values)
            required: false
            schema:
              type: string
          - name: suspension
            in: query
            description: Filter by suspension type (comma-separated values)
            required: false
            schema:
              type: string
          - name: trim
            in: query
            description: Filter by trim type (comma-separated values)
            required: false
            schema:
              type: string
          - name: watts
            in: query
            description: Filter by wattage (comma-separated values)
            required: false
            schema:
              type: string
          - name: dealerCost
            in: query
            description: Filter by minimum dealer cost
            required: false
            schema:
              type: number
              format: float
          - name: distributorCost
            in: query
            description: Filter by minimum distributor cost
            required: false
            schema:
              type: number
              format: float
          - name: contractorCost
            in: query
            description: Filter by minimum contractor cost
            required: false
            schema:
              type: number
              format: float
          - name: endUserCost
            in: query
            description: Filter by minimum end-user cost
            required: false
            schema:
              type: number
              format: float
          - name: page
            in: query
            description: Page number for pagination (default is 1)
            required: false
            schema:
              type: integer
              default: 1
          - name: limit
            in: query
            description: Number of items per page (default is 10)
            required: false
            schema:
              type: integer
              default: 10
          - name: getAllRecords
            in: query
            description: Set to 'true' to fetch all records without pagination
            required: false
            schema:
              type: string
              enum: ['true', 'false']
              default: 'false'    
        responses:
          '200':
            description: List of item configurations
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    items:
                      type: array
                      items:
                        $ref: ''
                    count:
                      type: integer
                      description: Total number of items
                    page:
                      type: integer
                      description: Current page number
                    limit:
                      type: integer
                      description: Number of items per page
          '400':
            description: Invalid query parameters or missing required fields
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
      
      post:
        summary: Bulk import item configurations
        operationId: bulkItemConfigurationImport
        tags:
          - Products
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    description: Array of items for bulk import
                    items:
                      type: object
                      properties:
                        brand:
                          type: string
                          description: Brand name
                        series:
                          type: string
                          description: Series name
                        category:
                          type: string
                          description: Category name
                        itemCategory:
                          type: string
                          description: Item category
                        itemCategoryCode:
                          type: string
                          description: Item category code  
                        family:
                          type: string
                          description: Family name
                        model:
                          type: string
                          description: Model name
                        modelSKU:
                          type: string
                          description: Model SKU
                        application:
                          type: array
                          items:
                            type: string
                          description: Application types
                        modelSKUDescription:
                          type: array
                          items:
                            type: string
                          description: Model SKU descriptions
                        cutsheetTitle:
                          type: array
                          items:
                            type: string
                          description: Titles for cutsheets
                        websiteModelAccordionTitle:
                          type: string
                          description: Title for model accordion on website
                        antiGlare:
                          type: array
                          items:
                            type: string
                          description: Anti-glare options
                        beamAngle:
                          type: array
                          items:
                            type: string
                          description: Beam angles
                        bend:
                          type: array
                          items:
                            type: string
                          description: Bend types
                        connectionCable:
                          type: array
                          items:
                            type: string
                          description: Connection cable options
                        driver:
                          type: array
                          items:
                            type: string
                          description: Driver options
                        finish:
                          type: array
                          items:
                            type: string
                          description: Finish options
                        length:
                          type: array
                          items:
                            type: string
                          description: Length options
                        lens:
                          type: array
                          items:
                            type: string
                          description: Lens options
                        lightColour:
                          type: array
                          items:
                            type: string
                          description: Light color options
                        operatingVoltage:
                          type: array
                          items:
                            type: string
                          description: Operating voltage options
                        suspension:
                          type: array
                          items:
                            type: string
                          description: Suspension options
                        trim:
                          type: array
                          items:
                            type: string
                          description: Trim options
                        watts:
                          type: array
                          items:
                            type: string
                          description: Wattage options
                        dealerCost:
                          type: number
                          description: Cost for dealers
                        dealerMSRP:
                          type: array
                          items:
                            type: number
                          description: MSRP for dealers
                        repCost:
                          type: number
                          description: Cost for representatives
                        distributorCost:
                          type: number
                          description: Cost for distributors
                        contractorCost:
                          type: number
                          description: Cost for contractors
                        endUserCost:
                          type: number
                          description: Cost for end users
                        acctyp:
                          type: array
                          items:
                            type: string
                          description: acctyp type
                        cabType:
                          type: array
                          items:
                            type: string
                          description: cab type 
                        chan:
                          type: array
                          items:
                              type: string
                          description: chan
                        connection:
                          type: array
                          items:
                              type: string
                          description: connection
                        reflector:
                          type: array
                          items:
                            type: string
                          description: reflector 
                        material:
                          type: array
                          items:
                            type: string
                          description: reflector
                        collections:
                          type: array
                          items:
                            type: string
                          description: reflector  
                        type:
                          type: array
                          items:
                            type: string
                          description: type  
                        wattsPerUOM:
                          type: string
                          description: Title for wattsPerUOM
                        lumensPerUOM:
                          type: string
                          description: Title for lumensPerUOM
                        wattsPerFixtureRange:
                          type: string
                          description: Title for wattsPerFixtureRange
                        lumensPerFixtureRange:
                          type: string
                          description: Title for lumensPerFixtureRange 
                required:
                  - data
        responses:
          '200':
            description: Items imported successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Invalid data format or missing fields
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
    
    /products/bulk-family-item-configuration:
      get:
        summary: Retrieve all family item configurations
        operationId: listFamilyItemConfigurations
        tags:
          - Products
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: family
            in: query
            description: Filter by family name
            required: false
            schema:
              type: string
          - name: model
            in: query
            description: Filter by model name
            required: false
            schema:
              type: string
          - name: page
            in: query
            description: Page number for pagination
            required: false
            schema:
              type: integer
              default: 1
          - name: limit
            in: query
            description: Number of items per page
            required: false
            schema:
              type: integer
              default: 10
          - name: getAllRecords
            in: query
            description: Set to 'true' to fetch all records without pagination
            required: false
            schema:
              type: string
              enum: ['true', 'false']
              default: 'false'
        responses:
          '200':
            description: List of family item configurations
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    items:
                      type: array
                      items:
                        type: object
                    count:
                      type: integer
                    page:
                      type: integer
                    limit:
                      type: integer
          '400':
            description: Invalid query parameters
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'
  
      
      post:
        summary: Bulk import family item configurations
        operationId: bulkFamilyItemConfigurationImport
        tags:
          - Products
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    description: Array of family item configurations
                    items:
                      type: object
                      properties:
                        brand:
                          type: string
                        series:
                          type: string
                        family:
                          type: string
                        model:
                          type: string
                        modelSKU:
                          type: string
                        configurationName:
                          type: string
                        configurationAttributes:
                          type: object
                          description: Dynamic object holding configuration details
                          additionalProperties: true
                required:
                  - data
        responses:
          '200':
            description: Family item configurations imported successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '400':
            description: Invalid data format or missing fields
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'

   
    /products/delete-All-family-Item-Configurations:
      delete:
        summary: Delete all family item configurations
        operationId: deleteAllFamilyItemConfigurations
        tags:
          - Products
        security:
          - ApiKeyAuth: []
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
        responses:
          '200':
            description: All family item configurations deleted successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ApiResponse'
          '500':
            $ref: '#/components/responses/ServerError'

    /utils/upload-file:
        get:
            summary: request signed urls for uploading documents
            operationId: uploadFile
            tags:
                - Utility

            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: location
                  in: query
                  required: true
                  schema:
                      type: string
                - name: fileName
                  in: query
                  required: false
                  schema:
                      type: string
                - name: type
                  in: query
                  required: true
                  schema:
                      type: string
                - name: count
                  in: query
                  required: true
                  schema:
                      type: string
                      format: int32
                      pattern: '^[\d]+$'
            responses:
                200:
                    description: Array of signed urls
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: array
                                              items:
                                                  type: object
                                                  properties:
                                                      url:
                                                          type: string
                                                          description: URL on which the document needs to be uploaded
                                                      preview:
                                                          type: string
                                                          description: URL for fetching the document
     
    /utils/delete-file:
      delete:
          summary: Delete an object from S3
          operationId: deleteFile
          tags:
              - Utility
  
          parameters:
              - $ref: '#/components/parameters/headerLanguage'
              - $ref: '#/components/parameters/headerPlatform'
              - $ref: '#/components/parameters/headerVersion'
              - name: key
                in: query
                required: true
                schema:
                    type: string
                description: The key of the object to be deleted from S3
  
          responses:
              200:
                  description: File deleted successfully
                  content:
                      application/json:
                          schema:
                              allOf:
                                  - $ref: '#/components/schemas/ApiResponse'
                                  - type: object
                                    properties:
                                        message:
                                            type: string
                                            description: Success message indicating the file was deleted
              400:
                  description: Missing or invalid parameters
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
              500:
                  description: Internal server error
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ApiResponse'
                                                      
                                                          

components:
    parameters:
        headerLanguage:
            name: Accept-Language
            in: header
            required: true
            schema:
                type: string
                default: en
                enum:
                    - en
                    - ru
                    - kk
        headerPlatform:
            name: X-SGI-Platform
            in: header
            required: true
            schema:
                type: string
                default: ios
                enum:
                    - ios
                    - android
        headerVersion:
            name: X-SGI-Version
            in: header
            required: true
            schema:
                type: string
                default: 1.0.0
                pattern: '^[\d]+\.[\d]+\.[\d]+$'
        queryPerPage:
            in: query
            name: perPage
            description: number of records per page
            allowEmptyValue: false
            required: true
            schema:
                type: integer
        queryPage:
            in: query
            name: page
            description: page number
            allowEmptyValue: false
            required: true
            schema:
                type: integer

    schemas:
        ApiResponse:
            required:
                - success
                - message
                - meta
            properties:
                success:
                    type: boolean
                message:
                    type: string
                data: {}
                meta:
                    type: object
                    properties:
                        version:
                            type: string
                        forceUpdate:
                            type: boolean
                        maintenance:
                            type: boolean
                        hasUpdate:
                            type: boolean
        Admin:
            type: object
            properties:
                firstName:
                    type: string
                lastName:
                    type: string
                email:
                    type: string
                countryCode:
                    type: string
                contactNumber:
                    type: string                    
        Category:
            type: object
            properties:
                id:
                    type: string
                    description: Category ID
                name:
                    type: string
                    description: Category name
                slug:
                    type: string
                    description: URL-friendly identifier
                image:
                    type: string
                    description: URL of the category image
                description:
                    type: string
                    description: Detailed description of the category
                parentId:
                    type: string
                    description: ID of the parent category
                isSuspended:
                    type: boolean
                    description: Whether the category is suspended
                isDeleted:
                    type: boolean
                    description: Whether the category is deleted
        
        HomeInspiration:
          type: object
          properties:
            InspirationMainTitle:
              type: string
              example: "Our Inspirations"
            InspriationMainCopy:
              type: string
              example: "Discover the inspirations behind our designs."
            carousel:
              type: array
              items:
                type: object
                properties:
                  InspirationTitle:
                    type: string
                    example: "Nature's Beauty"
                  Scene1Image:
                    type: string
                    example: "https://example.com/scene1.jpg"
                  Scene2Image:
                    type: string
                    example: "https://example.com/scene2.jpg"
                  Scene3Image:
                    type: string
                    example: "https://example.com/scene3.jpg"
                  Scene4Image:
                    type: string
                    example: "https://example.com/scene4.jpg"
                  Scene5Image:
                    type: string
                    example: "https://example.com/scene5.jpg"
                  isActive:
                    type: boolean
                    example: true
                  sortOrder:
                    type: integer
                    example: 1  
                    
        Inspiration:
          type: object
          properties:
            videoFileName:
              type: string
              description: Video file name of the inspiration.
            isActive:
              type: boolean
              description: Whether the inspiration is active.
            sortOrder:
              type: integer
              description: The order in which the inspiration appears.
            scenes:
              type: array
              items:
                $ref: '#/components/schemas/Scene'
            circadianLighting:
              $ref: '#/components/schemas/CircadianLighting'
            colour:
              $ref: '#/components/schemas/Colour'
            builtWithAASATTI:
              $ref: '#/components/schemas/BuiltWithAASATTI'
            landing:
              $ref: '#/components/schemas/landing'  
    
        Scene:
          type: object
          properties:
            mediaType:
              type: string
              enum: [image, video]
              description: Type of media for the scene.
            buttonLabel:
              type: string
              description: Label for the scene button.
            fileName:
              type: string
              description: File name for the scene.
    
        CircadianLighting:
          type: object
          properties:
            images:
              type: object
              properties:
                2500:
                  type: string
                  description: Image file for 2500K lighting.
                3000:
                  type: string
                  description: Image file for 3000K lighting.
                3500:
                  type: string
                  description: Image file for 3500K lighting.
                4000:
                  type: string
                  description: Image file for 4000K lighting.
                4500:
                  type: string
                  description: Image file for 4500K lighting.
                5000:
                  type: string
                  description: Image file for 5000K lighting.
                5500:
                  type: string
                  description: Image file for 5500K lighting.
                6000:
                  type: string
                  description: Image file for 6000K lighting.
                6500:
                  type: string
                  description: Image file for 6500K lighting.
    
        Colour:
          type: object
          properties:
            totalBlocks:
              type: integer
              description: Number of color blocks to display.
            blocks:
              type: array
              items:
                $ref: '#/components/schemas/ColourBlock'
    
        ColourBlock:
          type: object
          properties:
            hex:
              type: string
              description: HEX code for the color block.
            imageFileName:
              type: string
              description: Image file for the color block.
    
        BuiltWithAASATTI:
          type: object
          properties:
            inspired:
              $ref: '#/components/schemas/Media'
            built:
              $ref: '#/components/schemas/Media'
    
        Media:
          type: object
          properties:
            mediaType:
              type: string
              enum: [image, video]
              description: Type of media.
            fileName:
              type: string
              description: File name for the media. 
        
        landing:
          type: object
          properties:
            tileImage:
              type: string
              description: Type of media.
            tileTitle:
              type: string
              description: File name for the media. 
            tileOverview:
              type: string
              description: File name for the media. 
            type:
              type: string
              example: '66ea8a74da7c6ccd52ebce2b'
              description: File name for the media.  
              
        FamilyFilter:
          type: object
          properties:
            name:
              type: string
              description: The name of the family filter
            displayName:
                      type: string
                      description: The display name of the family to be added
            sortOrder:
              type: integer
              description: The sorting order of the family filter  
            brands:
              type: array
              items:
                type: string
                format: uuid
              description: List of Brand IDs associated with the filter
            isActive:
              type: boolean
              description: Indicates if the filter is active
            isDelete:
              type: boolean
              description: Indicates if the filter is deleted
          required:
            - name      

        User:
            properties:
                fullName:
                    type: string
                age:
                    type: number
                email:
                    type: string
                    format: email
                countryCode:
                    type: string
                phone:
                    type: string
                formattedPhone:
                    type: string
                avatar:
                    type: string
                pushNotificationAllowed:
                    type: string

        WhatsNew:
            type: object
            properties:
                whatsNewTitle:
                    type: string
                    description: Title of the WhatsNew section
                WhatsNewOverview:
                    type: string
                    description: Overview of the WhatsNew section
                carousel:
                    type: array
                    items:
                        $ref: '#/components/schemas/CarouselItem'
        CarouselItem:
            type: object
            properties:
                image:
                    type: string
                    description: URL of the image
                caption:
                    type: string
                    description: The caption text
                connectLink:
                    type: string
                    description: The connection link
                isActive:
                    type: boolean
                    description: Whether the carousel item is active
                sortOrder:
                    type: integer
                    description: The order of the carousel item
                isDeleted:
                    type: boolean
                    description: Whether the carousel item is deleted
    securitySchemes:
        ApiKeyAuth:
            type: apiKey
            in: header
            name: Authorization

    responses:
        ServerError:
            description: Internal Server Error
            content:
                application/json:
                    schema:
                        type: object
                        properties:
                            success:
                                type: boolean
                            message:
                                type: string
        BadRequestError:
            description: Request fails validation or doesn't meet all conditions for request
            content:
                application/json:
                    schema:
                        example:
                            success: false
                            message: 'Validation failed'
                            data: ''
                        allOf:
                            - $ref: '#/components/schemas/ApiResponse'
                            - type: object
                              properties:
                                  data:
                                      type: object
                                      nullable: true
        NotFoundError:
            description: Requested resource was not found
        UnauthorizedError:
            
            description: Access token is missing or invalid