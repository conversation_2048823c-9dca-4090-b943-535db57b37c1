module.exports = {
    Admin: require('./Admin.model'),
    AdminSettings: require('./AdminSettings.model'),
    Category: require('./Category.model'),
    Otp: require('./Otp.model'),
    Page: require('./Page.model'),
    User: require('./User.model'),
    Home: require('./Home.model'),
    WhatsNew: require('./WhatsNew.model'),
    PopupMenu: require('./PopupMenu.model'),
    HomeIntroducing: require('./HomeIntroducing.model'),
    HomeInspiration: require('./HomeInspiration.model'),
    HomeProjects: require('./HomeProjects.model'),
    Footer: require('./Footer.model'),
    BrandPopUpSGi: require('./BrandPopUpSGi.model'),
    BrandPopUpGENIE: require('./BrandPopUpGENIE.model'),
    BrandPopUpEfficacy: require('./BrandPopUpEfficacy.model'),
    BrandPopUpAASATI: require('./BrandPopUpAASATI.model'),
    Career: require('./Career.model'),
    Resource: require('./Resource.model'),
    Legal: require('./Legal.model'),
    Contact: require('./Contact.model'),
    Company: require('./Company.model'),
    Project: require('./Project.model'),
    ProjectLanding: require('./ProjectLanding.model'),
    Inspiration: require('./Inspiration.model'),
    InspirationLanding: require('./InspirationLanding.model'),
    ProductFilter: require('./ProductFilter.model'),
    BrandFilter: require('./BrandFilter.model'),
    SeriesFilter: require('./SeriesFilter.model'),
    ApplicationFilter: require('./ApplicationFilter.model'),
    CategoryFilter: require('./CategoryFilter.model'),
    FamilyFilter: require('./FamilyFilter.model'),
    LightColorsFilter: require('./LightColorsFilter.model'),
    OutputLumensFixtureFilter: require('./OutputLumensFixtureFilter.model'),
    OutputLumensFootFilter: require('./OutputLumensFootFilter.model'),
    OutputWattsFixtureFilter: require('./OutputWattsFixtureFilter.model'),
    OutputWattsFootFilter: require('./OutputWattsFootFilter.model'),
    TypeFilter: require('./TypeFilter.model'),
    AntiGlareAttribute: require('./AntiGlareAttribute.model'),
    BeamAngleAttribute: require('./BeamAngleAttribute.model'),
    BendAttribute: require('./BendAttribute.model'),
    ConnectionCableAttribute: require('./ConnectionCableAttribute.model'),
    DriverAttribute: require('./DriverAttribute.model'),
    FinishAttribute: require('./FinishAttribute.model'),
    LensAttribute: require('./LensAttribute.model'),
    LightColourAttribute: require('./LightColourAttribute.model'),
    SuspensionAttribute: require('./SuspensionAttribute.model'),
    TrimAttribute: require('./TrimAttribute.model'),
    WattsAttribute: require('./WattsAttribute.model'),
    ItemConfigurationMaster: require('./ItemConfigurationMaster.model'),
    FamilyItemConfigurationMaster: require('./FamilyItemConfigurationMaster'),
    Product: require('./Product.model'),
    ProductLanding: require('./ProductLanding.model'),
    MaterialFilter: require('./MaterialFilter.model'),
    CollectionFilter: require('./CollectionFilter.model'),
};
