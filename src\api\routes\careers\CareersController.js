const {
    models: { Career },
} = require('../../../../lib/models');

class CareersController {
    async addorUpdateCareer(req, res) {

        try {
            const { CareersBannerType, CareersBannerTitle, CareersBannerFileName, LifeatSGiTitle, LifeatSGiSummary, LifeatSGiDetail, CareersImage, WeAreHiringText } = req.body;

            const content = await Career.findOneAndUpdate(
                {},
                { $set: { CareersBannerType, CareersBannerTitle, CareersBannerFileName, LifeatSGiTitle, LifeatSGiSummary, LifeatSGiDetail, CareersImage, WeAreHiringText } },
                {
                    new: true,
                    upsert: true,
                }
            );
            return res.success(content, req.__('CAREER_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding whats new section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async getCareers(req, res) {
        try {
           
            const content = await Career.findOne({});
            if (!content) {
                return res.notFound({ success: false, message: 'Career not found' });
            }

            
            return res.success(content, req.__('CAREERS_FETCH_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching careers:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

}
module.exports = new CareersController();
