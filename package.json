{"name": "SGi-Connect", "version": "0.0.1", "description": "Repository for SGi-Connect", "private": true, "repository": "https://github.com/AadvikLabs/ConnWEB-FE.git", "engines": {"node": ">= 20.11.0", "yarn": ">= 1.12.3"}, "scripts": {"start": "nodemon src/api/index.js"}, "lint-staged": {"src/**/*.{js,json}": ["eslint --fix", "prettier --write", "git add"]}, "dependencies": {"@sendgrid/mail": "^6.5.0", "aws-sdk": "^2.618.0", "axios": "^0.21.1", "bcrypt": "^4.0.1", "body-parser": "^1.19.0", "compare-versions": "^3.6.0", "compression": "^1.7.4", "connect-flash": "^0.1.1", "connect-mongo": "^3.0.0", "cookie-parser": "^1.4.4", "cors": "^2.8.5", "custom-env": "^1.0.2", "dotenv": "^8.1.0", "ejs-locals": "^1.0.2", "email-templates": "^7.0.2", "express": "~4.16.1", "express-async-errors": "^3.1.1", "express-fileupload": "^1.1.7-alpha.3", "express-mung": "^0.5.1", "express-session": "^1.17.0", "fcm-node": "^1.5.2", "joi": "^14.3.1", "joi-i18n": "^13.1.4", "jsonwebtoken": "^8.5.1", "moment": "^2.24.0", "mongoose": "^5.11.7", "nodemon": "^3.1.7", "randomstring": "^1.1.5", "saslprep": "^1.0.3", "slugify": "^1.6.6", "socket.io": "2.2.0", "stripe": "^8.68.0", "swagger-ui-express": "^4.1.4", "uuid": "^8.1.0", "yamljs": "^0.3.0"}, "devDependencies": {"@types/node": "^12.7.5", "eslint": "^5.16.0", "lint-staged": "^9.3.0", "morgan": "^1.9.1", "prettier": "^1.18.2"}}