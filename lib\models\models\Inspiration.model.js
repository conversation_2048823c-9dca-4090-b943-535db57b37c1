const mongoose = require('mongoose');
const { Schema } = mongoose;

// Schema for Scenes
const SceneSchema = new Schema({
    mediaType: {
        type: String,
        enum: ['image', 'video'],
        required: true,
    },
    buttonLabel: {
        type: String,
        description: 'Label for the scene button.',
    },
    fileName: {
        type: String,
        description: 'File name for the scene.',
    },
});

// Schema for Circadian Lighting
const CircadianLightingSchema = new Schema({
    images: {
        2500: { type: String },
        3000: { type: String },
        3500: { type: String },
        4000: { type: String },
        4500: { type: String },
        5000: { type: String },
        5500: { type: String },
        6000: { type: String },
        6500: { type: String },
    },
});

// Schema for Colour Block
const ColourBlockSchema = new Schema({
    hex: {
        type: String,
        description: 'HEX Code for the colour block.',
    },
    imageFileName: {
        type: String,
        description: 'File name of the image to be displayed when this colour block is selected.',
    },
});

// Schema for Colour
const ColourSchema = new Schema({
    totalBlocks: {
        type: Number,
        description: 'Number of colour blocks to display.',
    },
    blocks: [ColourBlockSchema],
});

// Schema for Built With AASATTI
const BuiltWithAASATTISchema = new Schema({
    inspired: {
        mediaType: {
            type: String,
            enum: ['image', 'video'],
            default: 'image',
            required: true,
        },
        fileName: {
            type: String,
        },
    },
    built: {
        mediaType: {
            type: String,
            enum: ['image', 'video'],
            default: 'image',
            required: true,
        },
        fileName: {
            type: String,
        },
    },
});

// Schema for Inspired By AASATTI
const InspiredByAASATTISchema = new Schema({
    videoFileName: {
        type: String,
        description: 'This is the video file name for the AASATTI Inspiration rendering.',
    },
    isActive: {
        type: Boolean,
        default: true,
        description: 'Whether this inspiration is visible or hidden globally.',
    },
    sortOrder: {
        type: Number,
        description: 'The order in which the inspiration appears in the list.',
    },
    scenes: [SceneSchema],
    circadianLighting: CircadianLightingSchema,
    colour: ColourSchema,
    builtWithAASATTI: BuiltWithAASATTISchema,
});

const BannerSchema = new Schema({
    mediaType: {
        type: String,
        enum: ['image', 'video'],
        required: true,
    },
    fileName: {
        type: String,
        default: '',
    },
    fileTitle: {
        type: String,
        default: '',
    },
});
const LandingSchema = new Schema({
    tileImage: {
        type: String,
        default: '',
    },
    tileTitle: {
        type: String,
        default: '',
    },
    tileOverview: {
        type: String,
        default: '',
    },
    type: [
        {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'TypeFilter',
        },
    ],
});

// Main Inspiration Schema
const InspirationSchema = new Schema({
    videoFileName: {
        type: String,
        description: 'This is the video file name for the AASATTI Inspiration rendering.',
    },
    isDelete: {
        type: Boolean,
        default: false,
        description: 'Whether this inspiration is visible or hidden globally.',
    },
    isActive: {
        type: Boolean,
        default: true,
        description: 'Whether this inspiration is visible or hidden globally.',
    },
    sortOrder: {
        type: Number,
        description: 'The order in which the inspiration appears in the list.',
    },
    scenes: [SceneSchema],
    circadianLighting: CircadianLightingSchema,
    colour: ColourSchema,
    builtWithAASATTI: BuiltWithAASATTISchema,
    banner: BannerSchema,
    landing : LandingSchema,
    
});

module.exports = mongoose.model('Inspiration', InspirationSchema);
