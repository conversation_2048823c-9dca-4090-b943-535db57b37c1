const mongoose = require('mongoose');
const { Schema } = mongoose;

// Main Inspiration Schema
const InspirationLandingSchema = new Schema({
    
    mediaType: {
        type: String,
        enum: ['image', 'video'],
        required: true,
    },
    fileName: {
        type: String,
        default: '',
    },
    fileTitle: {
        type: String,
        default: '',
    },
    mediaTypeMobi: {
        type: String,
        enum: ['image', 'video'],
        required: true,
    },
    fileNameMobi: {
        type: String,
        default: '',
    },
    fileTitleMobi: {
        type: String,
        default: '',
    }
    
});

module.exports = mongoose.model('InspirationLanding', InspirationLandingSchema);
