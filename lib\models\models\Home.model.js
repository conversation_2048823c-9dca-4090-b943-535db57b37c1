const mongoose = require('mongoose'),
    Schema = mongoose.Schema,
    ObjectId = mongoose.Types.ObjectId;

const CarouselImageSchema = new Schema({
    banner: {
        type: String,
        required: true,
    },
    mediaType: {
        type: String,
        enum: ['image', 'video'],
        required: true,
    },
    bannerMobi: {
        type: String,
        required: true,
    },
    mediaTypeMobi: {
        type: String,
        enum: ['image', 'video'],
        required: true,
    },
    textOne: String,
    textTwo: String,
    link: String,
    isTextVisible: {
        type: Boolean,
        default: false,
    },
});

const HeroSectionSchema = new Schema({
    carouselImages: [CarouselImageSchema],
});

const CommitmentSchema = new Schema({
    icon: String,
    title: String,
    description: String,
});

const OurCommitmentsSchema = new Schema({
    HomeCommitmentImage: {
        type: String,
    },
    linkUrl: {
        type: String,
        default : ""
    },
    HomeCommitmentImageMobi: {
        type: String,
    },
    linkUrlMobi: {
        type: String,
        default : ""
    }
});

const ContactUsBannerSchema = new Schema({
    bannerImage: {
        type: String,
        required: true,
    },
    bannerImageMobi: {
        type: String,
        required: true,
    },
    text: String,
    link: String,
});

const HomeSchema = new Schema(
    {
        heroSection: HeroSectionSchema,
       
        ourCommitments: OurCommitmentsSchema,
        contactUsBanner: ContactUsBannerSchema,
    },
    {
        timestamps: {
            createdAt: 'created',
            updatedAt: 'updated',
        },
        id: false,
        toJSON: {
            getters: true,
        },
        toObject: {
            getters: true,
        },
    }
);

module.exports = mongoose.model('Home', HomeSchema);
