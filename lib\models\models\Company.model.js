const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Schema for Profile Years
const ProfileYearSchema = new Schema({
    ProfileYearImage: {
        type: String,
        default: ''
    },
    ProfileYearLabel: {
        type: String,
        default: ''
    },
    ProfileYearCaption: {
        type: String,
        default: ''
    },
    sortOrder: {
        type: Number,
        default: 0
    }
});

// Schema for Commitments
const CommitmentSchema = new Schema({
    title: {
        type: String,
        default: ''
    },
    overviewImageFileName: {
        type: String,
        default: ''
    },
    detailImageFileName: {
        type: String,
        default: ''
    },
    label: {
        type: String,
        default: ''
    },
    sortOrder: {
        type: Number,
        default: 0
    }
});

const CompanySchema = new Schema({
    // Banner Section
    CompanyBannerTitle: {
        type: String,
        default: ''
    },
    CompanyBannerFileName: {
        type: String,
        default: ''
    },

    // Profile Section
    ProfileMainTitle: {
        type: String,
        default: ''
    },
    ProfileMainCaption: {
        type: String,
        default: ''
    },
    ProfileYears: [ProfileYearSchema],

    // Vision-Mission-Values Section
    VisionMissionValuesImageFileName: {
        type: String,
        default: ''
    },
    VisionImageFileName: {
        type: String,
        default: ''
    },
    MissionImageFileName: {
        type: String,
        default: ''
    },
    ValuesImageFileName: {
        type: String,
        default: ''
    },
    MissionImageFileNameMobi: {
        type: String,
        default: ''
    },
    // Brands Section
    BrandsBannerImageFileName: {
        type: String,
        default: ''
    },

    // Commitment Section
    Commitments: [CommitmentSchema],

    // Legal Section
    CompanyLegalTitle: {
        type: String,
        default: ''
    },
    CompanyLegalImageFileName: {
        type: String,
        default: ''
    },
    CompanyLegalURL: {
        type: String,
        default: ''
    },
    CompanyCareersTitle: {
        type: String,
        default: ''
    },
    CompanyCareersImageFileName: {
        type: String,
        default: ''
    },
    CompanyCareersURL: {
        type: String,
        default: ''
    }
},
{
    timestamps: {
        createdAt: 'created',
        updatedAt: 'updated',
    },
    id: false,
    toJSON: {
        getters: true,
    },
    toObject: {
        getters: true,
    },
});

module.exports = mongoose.model('Company', CompanySchema);
