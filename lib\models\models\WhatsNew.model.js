const mongoose = require('mongoose'),
    Schema = mongoose.Schema,
    ObjectId = mongoose.Types.ObjectId;

const WhatsNewCarouselSchema = new Schema({
    image: {
        type: String,
        default: '',
    },
    caption: {
        type: String,
        default: '',
    },
    connectLink: {
        type: String,
        default: '',
    },
    isActive: {
        type: Boolean,
        default: false,
    },
    sortOrder: {
        type: Number,
        default: 0,
    },
    isDeleted: {
        type: Boolean,
        default: false,
    },
});

const WhatsNewSchema = new Schema(
    {
        whatsNewTitle: {
            type: String,
            default: '',
        },
        WhatsNewOverview: {
            type: String,
            default: '',
        },
        carousel: [WhatsNewCarouselSchema],
    },
    {
        timestamps: {
            createdAt: 'created',
            updatedAt: 'updated',
        },
        id: false,
        toJSON: {
            getters: true,
        },
        toObject: {
            getters: true,
        },
    }
);

// Pre-save middleware to auto-increment sortOrder for new carousel items
WhatsNewSchema.pre('save', function(next) {
    const doc = this;

    if (doc.isNew) {
        if (doc.carousel && doc.carousel.length > 0) {
            doc.carousel.forEach((carouselItem, index) => {
                if (carouselItem.isNew) {
                    const maxSortOrder = Math.max(...doc.carousel.map(item => item.sortOrder), 0);
                    carouselItem.sortOrder = maxSortOrder + 1;
                }
            });
        }
    }
    next();
});

module.exports = mongoose.model('WhatsNew', WhatsNewSchema);
