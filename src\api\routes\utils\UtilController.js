class UtilController {
    async uploadFile(req, res) {
        const { location, fileName, type, count = 1 } = req.query;
        // const extensions = { IMAGE: 'jpg', 'DOCUMENT.PDF': 'pdf' };
      const extensions = {
    // Images
    'image/jpeg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'image/bmp': 'bmp',
    'image/tiff': 'tiff',

    // Documents
    'application/pdf': 'pdf',
    'application/msword': 'doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
    'application/vnd.ms-excel': 'xls',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
    'application/vnd.ms-powerpoint': 'ppt',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
    'text/plain': 'txt',
    'application/rtf': 'rtf',

    // Video
    'video/mp4': 'mp4',
    'video/webm': 'webm',
    'video/ogg': 'ogv',
    'video/avi': 'avi',
    'video/mpeg': 'mpeg',
    'video/quicktime': 'mov',

    // Archives / Compressed
    'application/zip': 'zip',
    'application/x-7z-compressed': '7z',
    'application/x-rar-compressed': 'rar',
    'application/gzip': 'gz',
    'application/x-tar': 'tar'
};

        const extension = extensions[type] || '';
        if (!extension) return res.warn('', req.__('INVALID_FILE_TYPE'));

        const uploader = require('../../../../lib/uploader');
        const promises = [];
        for (let i = 1; i <= count; i++) {
            promises.push(uploader.getSignedUrl(location.endsWith('/') ? location : `${location}/`, extension, fileName));
        }

        const urls = await Promise.all(promises);
        return res.success(urls);
    }

    // New API for deleting an object from S3
    async deleteFile(req, res) {
        const { key } = req.query;
        if (!key) return res.warn('', req.__('MISSING_FILE_KEY'));

        try {
            const uploader = require('../../../../lib/uploader');
            const isDeleted = await uploader.deleteObj(key);
            if (isDeleted) {
                return res.success('', req.__('FILE_DELETED_SUCCESSFULLY'));
            } else {
                return res.warn('', req.__('FILE_DELETION_FAILED'));
            }
        } catch (err) {
            return res.error(err.message);
        }
    }
}

module.exports = new UtilController();
