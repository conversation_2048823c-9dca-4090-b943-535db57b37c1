const mongoose = require('mongoose');

// Main Product Filter Schema
const BrandFilterSchema = new mongoose.Schema({
    name: {
        type: String,
        default: '',
    },
    displayName: {
        type: String,
        default: '',
    },
    logo: {
        type: String,
        default: '',
    },
    isActive: {
        type: Boolean,
        default: true,
    },
    isDelete: {
        type: Boolean,
        default: false,
    },
});

module.exports = mongoose.model('BrandFilter', BrandFilterSchema);
