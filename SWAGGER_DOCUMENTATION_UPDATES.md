# Swagger Documentation Updates for Family Item Configuration APIs

## Overview
Updated the Swagger documentation to reflect the new Family Item Configuration API endpoints with enhanced functionality and proper schema definitions.

## Updated Endpoints

### 1. **POST /products/bulk-family-item-configuration**
**Purpose**: Bulk import family item configurations with automatic filter record management

#### Request Body:
```json
{
  "data": [
    {
      "productFamilyCode": "SGI-001-006",
      "productFamilyDescription": "SGI.  Specification Series.  FLEX.  CLASSIC",
      "brand": "SGI",
      "series": "SPECIFICATION",
      "category": "FLEX",
      "family": "CLASSIC",
      "associatedProducts": ["GEN-003-041", "GEN-003-042"],
      "similarProducts": ["GEN-001-030", "SGI-001-024"]
    }
  ],
  "filename": "revXXXXXXX"
}
```

#### Response:
```json
{
  "success": true,
  "message": "Family items imported successfully",
  "data": {
    "processedCount": 1000,
    "filename": "revXXXXXXX"
  }
}
```

#### Key Features:
- ✅ Accepts array of family item configurations
- ✅ Optional filename for batch tracking
- ✅ Automatic creation of missing Brand/Series/Category/Family records
- ✅ Optimized for thousands of records
- ✅ Proper error handling and validation

### 2. **GET /products/bulk-item-family-configuration**
**Purpose**: Retrieve family item configurations with advanced filtering

#### Query Parameters:
- `search`: Text search across all fields
- `brand`: Filter by brand name(s) - comma-separated
- `series`: Filter by series name(s) - comma-separated  
- `category`: Filter by category name(s) - comma-separated
- `family`: Filter by family name(s) - comma-separated
- `page`: Page number for pagination
- `limit`: Items per page (1-1000)
- `getAllRecords`: Set to 'true' to get all records

#### Example Request:
```
GET /products/bulk-item-family-configuration?brand=SGI,GENIE&series=SPECIFICATION&page=1&limit=50
```

#### Response:
```json
{
  "success": true,
  "message": "Family configurations retrieved successfully",
  "data": {
    "items": [...],
    "count": 1500,
    "page": 1,
    "limit": 50
  }
}
```

#### Key Features:
- ✅ Advanced filtering by multiple criteria
- ✅ Full-text search capability
- ✅ Pagination support
- ✅ Option to get all records
- ✅ Optimized queries with proper indexing

### 3. **DELETE /products/delete-All-family-Item-Configurations**
**Purpose**: Delete all family item configurations

#### Response:
```json
{
  "success": true,
  "message": "All family item configurations deleted successfully",
  "data": {
    "deletedCount": 1500
  }
}
```

#### Key Features:
- ✅ Bulk deletion of all records
- ✅ Returns count of deleted records
- ✅ Requires admin authentication

## Schema Definitions

### FamilyItemConfiguration (Input Schema)
Used for bulk import requests:

```yaml
FamilyItemConfiguration:
  type: object
  properties:
    productFamilyCode:
      type: string
      description: Unique product family code
      example: "SGI-001-006"
    productFamilyDescription:
      type: string
      description: Description of the product family
    brand:
      type: string
      description: Brand name
      example: "SGI"
    series:
      type: string
      description: Series name
      example: "SPECIFICATION"
    category:
      type: string
      description: Category name
      example: "FLEX"
    family:
      type: string
      description: Family name
      example: "CLASSIC"
    associatedProducts:
      type: array
      items:
        type: string
      description: Array of associated product codes
    similarProducts:
      type: array
      items:
        type: string
      description: Array of similar product codes
  required:
    - productFamilyCode
```

### FamilyItemConfigurationResponse (Output Schema)
Used for API responses:

```yaml
FamilyItemConfigurationResponse:
  type: object
  properties:
    _id:
      type: string
      description: MongoDB ObjectId
    productFamilyCode:
      type: string
      description: Unique product family code
    brand:
      type: object
      properties:
        id:
          type: string
          description: MongoDB ObjectId reference to BrandFilter
        name:
          type: string
          description: Brand name (uppercase)
    series:
      type: object
      properties:
        id:
          type: string
          description: MongoDB ObjectId reference to SeriesFilter
        name:
          type: string
          description: Series name (uppercase)
    # ... similar structure for category and family
    associatedProducts:
      type: array
      items:
        type: string
    similarProducts:
      type: array
      items:
        type: string
    fileName:
      type: string
      description: Filename of the import batch
    isActive:
      type: boolean
    isDeleted:
      type: boolean
    createdAt:
      type: string
      format: date-time
    updatedAt:
      type: string
      format: date-time
```

## Authentication & Security

All endpoints require proper authentication:
- **POST** and **DELETE** endpoints: Admin or User token required
- **GET** endpoints: Public access (but may require token based on configuration)

Security scheme:
```yaml
securitySchemes:
  ApiKeyAuth:
    type: apiKey
    in: header
    name: Authorization
```

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "message": "Invalid data format. 'data' should be an array.",
  "data": null
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "No family configurations found",
  "data": null
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Internal server error",
  "data": null
}
```

## Usage Examples

### 1. Bulk Import Example:
```bash
curl -X POST "http://api.example.com/products/bulk-family-item-configuration" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "data": [
      {
        "productFamilyCode": "SGI-001-006",
        "productFamilyDescription": "SGI.  Specification Series.  FLEX.  CLASSIC",
        "brand": "SGI",
        "series": "SPECIFICATION",
        "category": "FLEX",
        "family": "CLASSIC",
        "associatedProducts": ["GEN-003-041"],
        "similarProducts": ["GEN-001-030"]
      }
    ],
    "filename": "import_batch_001"
  }'
```

### 2. List with Filters Example:
```bash
curl -X GET "http://api.example.com/products/bulk-item-family-configuration?brand=SGI&series=SPECIFICATION&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. Search Example:
```bash
curl -X GET "http://api.example.com/products/bulk-item-family-configuration?search=SGI%20FLEX%20CLASSIC" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Benefits of Updated Documentation

1. **🔍 Clear API Structure**: Well-defined request/response schemas
2. **📝 Comprehensive Examples**: Real-world usage examples
3. **🛡️ Security Documentation**: Clear authentication requirements
4. **⚡ Performance Notes**: Optimized for bulk operations
5. **🔧 Developer Friendly**: Easy to understand and implement
6. **📊 Filtering Options**: Detailed parameter documentation

## Testing with Swagger UI

The updated documentation can be tested directly in Swagger UI:
1. Navigate to your Swagger UI endpoint
2. Find the "Products" section
3. Test the family item configuration endpoints
4. Use the provided examples as starting points

This comprehensive documentation ensures developers can easily understand and implement the Family Item Configuration APIs with confidence.
