const mongoose = require('mongoose');

// Beam <PERSON>le Attribute Schema
const LightColourAttributeSchema = new mongoose.Schema({
    name: {
        type: String,
        default: '',
    },
    modelSlug: {
        type: String,
        default: 'lc',
    },
    isActive: {
        type: Boolean,
        default: true,
    },
    isDelete: {
        type: Boolean,
        default: false,
    },
});

module.exports = mongoose.model('LightColourAttribute', LightColourAttributeSchema);
