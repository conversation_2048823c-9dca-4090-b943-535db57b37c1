const mongoose = require('mongoose'),
    Schema = mongoose.Schema;

const HomeIntroducingSchema = new Schema(
    {
        IntroducingTitle: {
            type: String,
            default: '',
        },
        IntroducingCopy: {
            type: String,
            default: '',
        },
        IntroducingImage1: {
            type: String,
            default: '',
        },
        IntroducingImage2: {
            type: String,
            default: '',
        },
    },
    {
        timestamps: {
            createdAt: 'created',
            updatedAt: 'updated',
        },
        id: false,
        toJSON: {
            getters: true,
        },
        toObject: {
            getters: true,
        },
    }
);

module.exports = mongoose.model('HomeIntroducing', HomeIntroducingSchema);
