const express = require('express');
const router = express.Router();
const ProductController = require('./ProductController');
const validations = require('./ProductValidations');
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenUserOrAdmin } = require('../../util/auth');
// get  inspiration banner 
router.get('/banner', ProductController.getBanner);
router.post('/:productId/board-container', verifyTokenUserOrAdmin,  ProductController.addBoardContainer);
router.put('/:productId/board-container/:boardId', verifyTokenUserOrAdmin,  ProductController.updateBoardContainer);
router.delete('/:productId/board-container/:boardId', verifyTokenUserOrAdmin,  ProductController.deleteBoardContainer);
router.post('/:productId/product-models', verifyTokenUserOrAdmin,  ProductController.addProductModel);
router.put('/:productId/product-models/:modelId', verifyTokenUserOrAdmin,  ProductController.updateProductModel);
router.delete('/:productId/product-models/:modelId', verifyTokenUserOrAdmin,  ProductController.deleteProductModel);
router.post('/bulk-item-configuration', verifyTokenUserOrAdmin,  ProductController.bulkItemConfigurationImport);
router.get('/bulk-item-configuration',  ProductController.listItemConfigurations);
router.delete('/delete-All-Item-Configurations', verifyTokenUserOrAdmin,  ProductController.deleteAllItemConfigurations);
router.post('/bulk-family-item-configuration', verifyTokenUserOrAdmin,  ProductController.bulkFamilyItemConfigurationImport);
router.get('/bulk-item-family-configuration',  ProductController.listFamilyItemConfigurations);
router.delete('/delete-All-family-Item-Configurations', verifyTokenUserOrAdmin,  ProductController.deleteAllFamilyItemConfigurations);
router.get('/:productId',  ProductController.getProductDetail);
router.put('/:productId', verifyTokenUserOrAdmin,  ProductController.editProductFamily);

router.get('/', ProductController.listProductsFamily);
router.post('/', verifyTokenUserOrAdmin,  ProductController.createProductFamily);
// Add or Update inspiration banner by ID
router.post('/banner', ProductController.addOrUpdatebanner);


module.exports = router;
