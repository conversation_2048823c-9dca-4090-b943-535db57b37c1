const express = require('express');
const router = express.Router();
const InspirationsController = require('./InspirationsController');
const validations = require('./InspirationsValidations');
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenUserOrAdmin, verifyTokenAdmin } = require('../../util/auth');

// get  inspiration banner 
router.get('/banner', InspirationsController.getBanner);
// Add a new scene to an inspiration
router.post('/:id/scene', InspirationsController.addScene);

// Update a scene in an inspiration
router.put('/:id/scene/:sceneId', InspirationsController.updateScene);

// Delete a scene from an inspiration
router.delete('/:id/scene/:sceneId', InspirationsController.deleteScene);

// Fetch all inspirations
router.get('/', InspirationsController.getAllInspirations);

// Fetch a specific inspiration by ID
router.get('/:id', InspirationsController.getInspiration);

// Add a new inspiration
router.post('/', InspirationsController.addOrUpdateInspiration);

// Update an existing inspiration by ID
router.patch('/:id', InspirationsController.addOrUpdateInspiration);

// Delete an inspiration by ID
router.delete('/:id', InspirationsController.deleteInspiration);

// Add or Update inspiration banner by ID
router.post('/banner', InspirationsController.addOrUpdatebanner);




module.exports = router;
