# WTP Server

## Getting Started

### Installing dependencies
Run `npm install` in project root

### Running servers
Admin: Run `npm run start:admin` in project root
API: Run `npm run start:api` in project root

## Adding dependencies
To add dependencies to a package use
`npm install dependency-name`

## Creating new packages
The preferred location for creating new packages is `lib` folder, and for servers is `src` folder.

```
cd lib 
mkdir package-name && cd package-name
```