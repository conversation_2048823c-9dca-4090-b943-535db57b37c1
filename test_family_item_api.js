/**
 * Test script for the updated Family Item Configuration API
 * This demonstrates how to use the new JSON format for bulk import
 */

const axios = require('axios');

// Sample data in the format you specified
const testData = {
    "data": [
        {
            "productFamilyCode": "SGI-001-006",
            "productFamilyDescription": "SGI.  Specification Series.  FLEX.  CLASSIC",
            "brand": "SGI",
            "series": "SPECIFICATION",
            "category": "FLEX",
            "family": "CLASSIC",
            "associatedProducts": [
                "GEN-003-041",
                "GEN-003-042",
                "GEN-003-043"
            ],
            "similarProducts": [
                "GEN-001-030",
                "SGI-001-024",
                "SGI-001-021"
            ]
        },
        {
            "productFamilyCode": "SGI-001-019",
            "productFamilyDescription": "SGI.  Specification Series.  FLEX.  GRAZER",
            "brand": "SGI",
            "series": "SPECIFICATION",
            "category": "FLEX",
            "family": "GRAZER",
            "associatedProducts": [
                "AAS-008-061",
                "AAS-010-058"
            ],
            "similarProducts": [
                "SGI-001-024",
                "SGI-003-019",
                "GEN-003-050"
            ]
        },
        {
            "productFamilyCode": "SGI-001-021",
            "productFamilyDescription": "SGI.  Specification Series.  FLEX.  LINE",
            "brand": "SGI",
            "series": "SPECIFICATION",
            "category": "FLEX",
            "family": "LINE",
            "associatedProducts": [
                "SGI-001-022",
                "GEN-003-041",
                "GEN-003-042"
            ],
            "similarProducts": [
                "GEN-001-029",
                "GEN-001-030",
                "SGI-001-006"
            ]
        }
    ],
    "filename": "revXXXXXXX"
};

async function testFamilyItemImport() {
    try {
        console.log('Testing Family Item Configuration Import...');
        console.log('Data to be imported:', JSON.stringify(testData, null, 2));

        // Replace with your actual API endpoint and authentication token
        const API_BASE_URL = 'http://localhost:3000/api'; // Adjust as needed
        const AUTH_TOKEN = 'your-auth-token-here'; // Add your actual token

        const response = await axios.post(
            `${API_BASE_URL}/products/bulk-family-item-configuration`,
            testData,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${AUTH_TOKEN}` // Adjust auth header as needed
                }
            }
        );

        console.log('✅ Import successful!');
        console.log('Response:', response.data);

        // Test listing the imported data
        console.log('\nTesting list endpoint...');
        const listResponse = await axios.get(
            `${API_BASE_URL}/products/bulk-item-family-configuration`,
            {
                headers: {
                    'Authorization': `Bearer ${AUTH_TOKEN}`
                }
            }
        );

        console.log('✅ List successful!');
        console.log('Listed items:', listResponse.data.items?.length || 0);
        
        // Show first item structure
        if (listResponse.data.items && listResponse.data.items.length > 0) {
            console.log('Sample item structure:');
            console.log(JSON.stringify(listResponse.data.items[0], null, 2));
        }

    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
    }
}

// Uncomment to run the test
// testFamilyImport();

console.log(`
📋 Family Item Configuration API Test

This script demonstrates the updated API format. The API now:

1. ✅ Accepts data in the format you specified with 'data' array and 'filename'
2. ✅ Automatically finds or creates Brand, Series, Category, and Family records
3. ✅ Updates FamilyItemConfigurationMaster with proper ID references
4. ✅ Optimized for bulk operations with thousands of records

Key Features:
- Batch processing for performance
- Automatic creation of missing filter records
- Proper ID referencing in the schema
- Maintains backward compatibility

To test:
1. Update API_BASE_URL and AUTH_TOKEN in this file
2. Uncomment the testFamilyItemImport() call
3. Run: node test_family_item_api.js

Expected Response Structure:
{
  "success": true,
  "message": "Family items imported successfully",
  "data": {
    "processedCount": 3,
    "filename": "revXXXXXXX"
  }
}
`);

module.exports = { testData, testFamilyItemImport };
