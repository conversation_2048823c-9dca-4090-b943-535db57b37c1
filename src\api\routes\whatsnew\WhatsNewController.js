const {
    models: { WhatsNew },
} = require('../../../../lib/models');
const { utcDateTime } = require('../../../../lib/util');

class WhatsNewController {
    async addWhatsNew(req, res) {
        try {
            const { whatsNew } = req.body;

            const homeContent = await WhatsNew.findOneAndUpdate(
                {},
                { $set: whatsNew },
                {
                    new: true,
                    upsert: true,
                }
            );
            return res.success(homeContent, req.__('WHATS_NEW_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding whats new section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async addWhatsNewCarousel(req, res) {
        try {
            const { image, caption, connectLink, isActive } = req.body;
            const doc = await WhatsNew.findOne({});
            if (!doc) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const maxSortOrder = doc.carousel.length > 0 ? Math.max(...doc.carousel.map(item => item.sortOrder)) : 0;
            const newItem = {
                image,
                caption,
                connectLink,
                isActive,
                sortOrder: maxSortOrder + 1,
            };

            doc.carousel.push(newItem);
            await doc.save();
            return res.success(doc, req.__('CAROUSEL_ITEM_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding new carousel:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async updateWhatsNewCarousel(req, res) {
        try {
            const { carouselId } = req.params;
            const { image, caption, connectLink, isActive, sortOrder } = req.body;
            const doc = await WhatsNew.findOne({});
            if (!doc) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const carouselItem = doc.carousel.id(carouselId);
            if (!carouselItem) {
                return res.notFound({ success: false, message: 'Carousel item not found' });
            }

            // Update the carousel item
            carouselItem.image = image !== undefined ? image : carouselItem.image;
            carouselItem.caption = caption !== undefined ? caption : carouselItem.caption;
            carouselItem.connectLink = connectLink !== undefined ? connectLink : carouselItem.connectLink;
            carouselItem.isActive = isActive !== undefined ? isActive : carouselItem.isActive;
            carouselItem.sortOrder = sortOrder !== undefined ? sortOrder : carouselItem.sortOrder;

            await doc.save();

            return res.success(doc, req.__('CAROUSEL_ITEM_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating carousel item:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async viewWhatsNew(req, res) {
        const { isActive = 'true' } = req.query; // Default to 'true' if not provided
        try {
            const WhatsNewContent = await WhatsNew.findOne({});

            if (!WhatsNewContent) {
                return res.warn({}, req.__('WHATS_NEW_NOT_FOUND'));
            }

            return res.success(WhatsNewContent, req.__('WHATS_NEW_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching whats new section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async deleteCarousel(req, res){
        try {
            const { carouselId } = req.params;
    
            // Find the WhatsNew document by ID
            const whatsNew = await WhatsNew.findOne({});
            if (!whatsNew) {
                return res.warn({},req.__('WhatsNew item not found') );
            }
    
            // Find the index of the carousel item to delete
            const carouselIndex = whatsNew.carousel.findIndex(item => item._id.toString() === carouselId);
            if (carouselIndex === -1) {
                return res.warn({},req.__('Carousel item not found') );
            }
    
            // Mark the carousel item as deleted or remove it completely
            // Option 1: Mark as deleted
            // whatsNew.carousel[carouselIndex].isDeleted = true;
    
            // Option 2: Remove the item completely from the array
            whatsNew.carousel.splice(carouselIndex, 1);
    
            // Save the updated document
            await whatsNew.save();
    
            res.success( whatsNew, req.__('Carousel item deleted successfully'));
    
        } catch (error) {
            console.error('Error deleting carousel item:', error);
            res.status(500).json({ message: 'Internal server error' });
        }
    }
}
module.exports = new WhatsNewController();
