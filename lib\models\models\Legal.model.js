const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const LegalDocSchema = new Schema({
    LegalDocLabel: {
        type: String,
        default: ''
    },
    LegalDocContent: {
        type: String,
        default: ''
    },
    LegalDocFileName: {
        type: String,
        default: ''
    },
    isActive: {
        type: Boolean,
        default: false,
    },
    sortOrder: {
        type: Number,
        default: 0,
    },
    isDeleted: {
        type: Boolean,
        default: false,
    },
});

const LegalSchema = new Schema(
    {
        LegalBannerType: {
            type: String,
            default:"image",
            enum: ['image', 'video'],
        },
        LegalBannerTitle: {
            type: String,
            default: ''
        },
        LegalBannerFileName: {
            type: String,
            default: ''
        },
        LegalDocs: [LegalDocSchema]
    },
    {
        timestamps: {
            createdAt: 'created',
            updatedAt: 'updated',
        },
        id: false,
        toJSON: {
            getters: true,
        },
        toObject: {
            getters: true,
        },
    }
);

module.exports = mongoose.model('Legal', LegalSchema);
