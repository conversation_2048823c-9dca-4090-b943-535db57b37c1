const {
    models: { Home, PopupMenu, HomeIntroducing, HomeInspiration, HomeProjects, Footer },
} = require('../../../../lib/models');
const { utcDateTime } = require('../../../../lib/util');

class HomeController {
    async addHeroSection(req, res) {
        try {
            const { heroSection } = req.body;

            const homeContent = await Home.findOneAndUpdate(
                {},
                { heroSection },
                {
                    new: true, // Return the updated document
                    upsert: true, // Create the document if it does not exist
                }
            );

            return res.success(homeContent.heroSection, req.__('HERO_SECTION_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding hero section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async updateHeroSection(req, res) {
        try {
            const { heroSection } = req.body;

            const updatedHomeContent = await Home.findOneAndUpdate(
                {},
                { heroSection },
                {
                    new: true, // Return the updated document
                    upsert: true, // Create the document if it does not exist
                }
            );

            return res.success(updatedHomeContent.heroSection, req.__('HERO_SECTION_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating hero section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async viewHeroSection(req, res) {
        try {
            const homeContent = await Home.findOne({});

            if (!homeContent) {
                return res.warn({}, req.__('HERO_SECTION_NOT_FOUND'));
            }

            return res.success(homeContent.heroSection, req.__('HERO_SECTION_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching hero section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async addWhatsNew(req, res) {
        try {
            const { whatsNew } = req.body;

            const homeContent = await Home.findOneAndUpdate(
                {},
                { whatsNew },
                {
                    new: true,
                    upsert: true,
                }
            );

            return res.success(homeContent.whatsNew, req.__('WHATS_NEW_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding whats new section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async updateWhatsNew(req, res) {
        try {
            const { whatsNew } = req.body;

            const updatedHomeContent = await Home.findOneAndUpdate(
                {},
                { whatsNew },
                {
                    new: true,
                    upsert: true,
                }
            );

            return res.success(updatedHomeContent.whatsNew, req.__('WHATS_NEW_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating whats new section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async viewWhatsNew(req, res) {
        try {
            const homeContent = await Home.findOne({});

            if (!homeContent) {
                return res.warn({}, req.__('WHATS_NEW_NOT_FOUND'));
            }

            return res.success(homeContent.whatsNew, req.__('WHATS_NEW_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching whats new section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async addOurCommitments(req, res) {
        try {
            const { ourCommitments } = req.body;

            const homeContent = await Home.findOneAndUpdate(
                {},
                { ourCommitments },
                {
                    new: true,
                    upsert: true,
                }
            );

            return res.success(homeContent.ourCommitments, req.__('OUR_COMMITMENTS_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding our commitments section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async updateOurCommitments(req, res) {
        try {
            const { ourCommitments } = req.body;

            const updatedHomeContent = await Home.findOneAndUpdate(
                {},
                { ourCommitments },
                {
                    new: true,
                    upsert: true,
                }
            );

            return res.success(updatedHomeContent.ourCommitments, req.__('OUR_COMMITMENTS_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating our commitments section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async viewOurCommitments(req, res) {
        try {
            const homeContent = await Home.findOne({});

            if (!homeContent) {
                return res.warn({}, req.__('OUR_COMMITMENTS_NOT_FOUND'));
            }

            return res.success(homeContent.ourCommitments, req.__('OUR_COMMITMENTS_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching our commitments section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async addContactUsBanner(req, res) {
        try {
            const { contactUsBanner } = req.body;

            const homeContent = await Home.findOneAndUpdate(
                {},
                { contactUsBanner },
                {
                    new: true,
                    upsert: true,
                }
            );

            return res.success(homeContent.contactUsBanner, req.__('CONTACT_US_BANNER_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding contact us banner section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async updateContactUsBanner(req, res) {
        try {
            const { contactUsBanner } = req.body;

            const updatedHomeContent = await Home.findOneAndUpdate(
                {},
                { contactUsBanner },
                {
                    new: true,
                    upsert: true,
                }
            );

            return res.success(updatedHomeContent.contactUsBanner, req.__('CONTACT_US_BANNER_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating contact us banner section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async viewContactUsBanner(req, res) {
        try {
            const homeContent = await Home.findOne({});

            if (!homeContent) {
                return res.warn({}, req.__('CONTACT_US_BANNER_NOT_FOUND'));
            }

            return res.success(homeContent.contactUsBanner, req.__('CONTACT_US_BANNER_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching contact us banner section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async updateHeroSectionPopupMenu(req, res) {
        try {
            const hoverImages = req.body;

            // Ensure only valid fields are updated
            const validFields = [
                'HeroPopUpMenuProductsImage',
                'HeroPopUpMenuAasattiImage',
                'HeroPopUpMenuInspirationImage',
                'HeroPopUpMenuProjectsImage',
                'HeroPopUpMenuResourcesImage',
                'HeroPopUpMenuCompanyImage',
                'HeroPopUpMenuContactImage',
                'HeroPopUpMenuCareersImage',
                'HeroPopUpMenuLegalImage'
            ];

            const updateData = {};
            validFields.forEach(field => {
                if (hoverImages[field]) {
                    updateData[field] = hoverImages[field];
                }
            });

            const updatedHomeContent = await PopupMenu.findOneAndUpdate(
                {},
                { $set: updateData },
                { new: true, upsert: true }
            );

            return res.success(updatedHomeContent, req.__('HERO_SECTION_POPUP_MENU_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating hero section popup menu:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async viewHeroSectionPopupMenu(req, res) {
        try {
            const getHomePopupMenuContent = await PopupMenu.findOne({});
            return res.success(getHomePopupMenuContent, req.__('HERO_SECTION_POPUP_MENU_FETCH_SUCCESSFULL'));
        } catch (error) {
            console.error('Error while getting hero section popup menu:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async updateHomeIntroducting(req, res) {
        try {
            const { IntroducingTitle, IntroducingCopy, IntroducingImage1, IntroducingImage2 } = req.body;

            const updateData = { IntroducingTitle, IntroducingCopy, IntroducingImage1, IntroducingImage2 };

            const updatedHomeIntroducing = await HomeIntroducing.findOneAndUpdate(
                {},
                { $set: updateData },
                { new: true, upsert: true }
            );

            return res.success(updatedHomeIntroducing, req.__('HOME_INTRODUCING_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating home introducing menu:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async viewHomeIntroducting(req, res) {
        try {
            const getHomeIntroducingContent = await HomeIntroducing.findOne({});
            return res.success(getHomeIntroducingContent, req.__('HOME_INTRODUCING_FETCH_SUCCESSFULL'));
        } catch (error) {
            console.error('Error while getting hero section popup menu:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async addOrUpdateHomeInspiration(req, res) {
        try {
            const { InspirationMainTitle, InspriationMainCopy } = req.body;

            const inspirationContent = await HomeInspiration.findOneAndUpdate(
                {},
                { $set: { InspirationMainTitle, InspriationMainCopy } },
                {
                    new: true,
                    upsert: true,
                }
            );
            return res.success(inspirationContent, req.__('INSPIRATION_SECTION_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating inspiration section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add a new item to the carousel
    async addHomeInspirationCarousel(req, res) {
        try {
            const {
                InspirationTitle,
                Scene1Image,
                Scene2Image,
                Scene3Image,
                Scene4Image,
                Scene5Image,
                isActive,
            } = req.body;
            const doc = await HomeInspiration.findOne({});
            if (!doc) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const maxSortOrder = doc.carousel.length > 0 ? Math.max(...doc.carousel.map(item => item.sortOrder)) : 0;
            const newItem = {
                InspirationTitle,
                Scene1Image,
                Scene2Image,
                Scene3Image,
                Scene4Image,
                Scene5Image,
                isActive,
                sortOrder: maxSortOrder + 1,
            };

            doc.carousel.push(newItem);
            await doc.save();
            return res.success(doc, req.__('CAROUSEL_ITEM_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding new carousel item:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Update an existing carousel item
    async updateHomeInspirationCarousel(req, res) {
        try {
            const { carouselId } = req.params;
            const {
                InspirationTitle,
                Scene1Image,
                Scene2Image,
                Scene3Image,
                Scene4Image,
                Scene5Image,
                isActive,
                sortOrder,
            } = req.body;
            const doc = await HomeInspiration.findOne({});
            if (!doc) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const carouselItem = doc.carousel.id(carouselId);
            if (!carouselItem) {
                return res.notFound({ success: false, message: 'Carousel item not found' });
            }

            // Update the carousel item
            carouselItem.InspirationTitle =
                InspirationTitle !== undefined ? InspirationTitle : carouselItem.InspirationTitle;
            carouselItem.Scene1Image = Scene1Image !== undefined ? Scene1Image : carouselItem.Scene1Image;
            carouselItem.Scene2Image = Scene2Image !== undefined ? Scene2Image : carouselItem.Scene2Image;
            carouselItem.Scene3Image = Scene3Image !== undefined ? Scene3Image : carouselItem.Scene3Image;
            carouselItem.Scene4Image = Scene4Image !== undefined ? Scene4Image : carouselItem.Scene4Image;
            carouselItem.Scene5Image = Scene5Image !== undefined ? Scene5Image : carouselItem.Scene5Image;
            carouselItem.isActive = isActive !== undefined ? isActive : carouselItem.isActive;
            carouselItem.sortOrder = sortOrder !== undefined ? sortOrder : carouselItem.sortOrder;

            await doc.save();

            return res.success(doc, req.__('CAROUSEL_ITEM_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating carousel item:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    // Update an existing carousel item
    async updateHomeInspirationAllCarousel(req, res) {
        try {
            const { carouselId } = req.params;

            const doc = await HomeInspiration.findOneAndUpdate({}, { $set: req.body }, { new: true });
            // if (!doc) {
            //     return res.notFound({ success: false, message: 'Document not found' });
            // }

            // const carouselItem = doc.carousel.id(carouselId);
            // if (!carouselItem) {
            //     return res.notFound({ success: false, message: 'Carousel item not found' });
            // }

            // // Update the carousel item
            // carouselItem.InspirationTitle =
            //     InspirationTitle !== undefined ? InspirationTitle : carouselItem.InspirationTitle;
            // carouselItem.Scene1Image = Scene1Image !== undefined ? Scene1Image : carouselItem.Scene1Image;
            // carouselItem.Scene2Image = Scene2Image !== undefined ? Scene2Image : carouselItem.Scene2Image;
            // carouselItem.Scene3Image = Scene3Image !== undefined ? Scene3Image : carouselItem.Scene3Image;
            // carouselItem.Scene4Image = Scene4Image !== undefined ? Scene4Image : carouselItem.Scene4Image;
            // carouselItem.Scene5Image = Scene5Image !== undefined ? Scene5Image : carouselItem.Scene5Image;
            // carouselItem.isActive = isActive !== undefined ? isActive : carouselItem.isActive;
            // carouselItem.sortOrder = sortOrder !== undefined ? sortOrder : carouselItem.sortOrder;

            // await doc.save();

            return res.success(doc, req.__('CAROUSEL_ITEM_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating carousel item:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // View the HomeInspiration section
    async viewHomeInspiration(req, res) {
        try {
            const inspirationContent = await HomeInspiration.findOne({});

            if (!inspirationContent) {
                return res.warn({}, req.__('INSPIRATION_SECTION_NOT_FOUND'));
            }

            return res.success(inspirationContent, req.__('INSPIRATION_SECTION_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching inspiration section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add or update Home projects
    async addOrUpdateHomeProjects(req, res) {
        try {
            const { HomeProjectsMainTitle } = req.body;

            const HomeProjectContent = await HomeProjects.findOneAndUpdate(
                {},
                { $set: { HomeProjectsMainTitle } },
                {
                    new: true,
                    upsert: true,
                }
            );
            return res.success(HomeProjectContent, req.__('PROJECT_SECTION_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating project section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add a new item to the tiles
    async addHomeProjectsTiles(req, res) {
        try {
            const {
                HomeProjectTitle,
                HomeProjectDescription,
                HomeProjectImage,
                HomeProjectURL,
                isActive,
                sortOrder
            } = req.body;
            const doc = await HomeProjects.findOne({});
            if (!doc) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const maxSortOrder = doc.carousel.length > 0 ? Math.max(...doc.carousel.map(item => item.sortOrder)) : 0;
            const newItem = {
                HomeProjectTitle,
                HomeProjectDescription,
                HomeProjectImage,
                HomeProjectURL,
                isActive,
                sortOrder: maxSortOrder + 1,
            };

            doc.carousel.push(newItem);
            await doc.save();
            return res.success(doc, req.__('TILE_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding new tile item:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Update an existing tiles 
    async updateHomeProjectTiles(req, res) {
        try {
            const { id } = req.params;
            const {
                HomeProjectTitle,
                HomeProjectDescription,
                HomeProjectImage,
                HomeProjectURL,
                isActive,
                sortOrder
            } = req.body;
            const doc = await HomeProjects.findOne({});
            if (!doc) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const carouselItem = doc.carousel.id(id);
            if (!carouselItem) {
                return res.notFound({ success: false, message: 'Carousel item not found' });
            }

            // Update the carousel item
            carouselItem.HomeProjectTitle =
            HomeProjectTitle !== undefined ? HomeProjectTitle : carouselItem.HomeProjectTitle;
            carouselItem.HomeProjectDescription = HomeProjectDescription !== undefined ? HomeProjectDescription : carouselItem.HomeProjectDescription;
            carouselItem.HomeProjectImage = HomeProjectImage !== undefined ? HomeProjectImage : carouselItem.HomeProjectImage;
            carouselItem.HomeProjectURL = HomeProjectURL !== undefined ? HomeProjectURL : carouselItem.HomeProjectURL;
            carouselItem.isActive = isActive !== undefined ? isActive : carouselItem.isActive;
            carouselItem.sortOrder = sortOrder !== undefined ? sortOrder : carouselItem.sortOrder;

            await doc.save();

            return res.success(doc, req.__('TILES_ITEM_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating carousel item:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    // Update an existing tiles 
    async updateHomeProjectAllTiles(req, res) {
        try {

            const doc = await HomeProjects.findOneAndUpdate({}, { $set: req.body }, { new: true });
            return res.success(doc, req.__('TILES_ITEM_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating tiles:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // View the HomeTiles section
    async viewHomeProjects(req, res) {
        try {
            const content = await HomeProjects.findOne({});

            if (!content) {
                return res.warn({}, req.__('PROJECTS_SECTION_NOT_FOUND'));
            }

            return res.success(content, req.__('PROJECTS_SECTION_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching project section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async getFooterData(req, res) {
        try {
            const footerData = await Footer.findOne({});
            if (!footerData) {
                return res.warn({}, req.__('FOOTER_DATA_NOT_FOUND'));
            }
            return res.success(footerData, req.__('FOOTER_DATA_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching footer data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async updateFooterData(req, res) {
        const {
            FooterSocialMediaInstaURL,
            FooterSocialMediaXURL,
            FooterSocialMediaFBURL,
            FooterSocialMediaLinkedINURL,
            FooterPrivacyURL,
            FooterWarrantyURL,
            FooterTermsURL,
            FooterCopyrightURL,
        } = req.body;
        try {
            const footerData = await Footer.findOneAndUpdate(
                {},
                {
                    $set: {
                        FooterSocialMediaInstaURL,
                        FooterSocialMediaXURL,
                        FooterSocialMediaFBURL,
                        FooterSocialMediaLinkedINURL,
                        FooterPrivacyURL,
                        FooterWarrantyURL,
                        FooterTermsURL,
                        FooterCopyrightURL,
                    },
                },
                { new: true, upsert: true }
            );

            return res.success(footerData, req.__('FOOTER_DATA_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating footer data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
}
module.exports = new HomeController();
