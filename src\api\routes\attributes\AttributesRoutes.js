const express = require('express');
const router = express.Router();
const AttributesController = require('./AttributesController');
const validations = require('./AttributesValidations');
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenUserOrAdmin } = require('../../util/auth');

// AntiGlare Attribute
router.get('/antiglare', AttributesController.listAntiGlareAttribute);
router.post('/antiglare', verifyTokenUserOrAdmin, AttributesController.addAntiGlareAttribute);
router.put('/antiglare/:id', verifyTokenUserOrAdmin, AttributesController.editAntiGlareAttribute);
router.delete('/antiglare/:id', verifyTokenUserOrAdmin, AttributesController.deleteAntiGlareAttribute);

// Beam <PERSON>le Attribute
router.get('/beam-angle', AttributesController.listBeamAngleAttribute);
router.post('/beam-angle', verifyTokenUserOrAdmin, AttributesController.addBeamAngleAttribute);
router.put('/beam-angle/:id', verifyTokenUserOrAdmin, AttributesController.editBeamAngleAttribute);
router.delete('/beam-angle/:id', verifyTokenUserOrAdmin, AttributesController.deleteBeamAngleAttribute);

// Bend Attribute
router.get('/bend', AttributesController.listBendAttribute);
router.post('/bend', verifyTokenUserOrAdmin, AttributesController.addBendAttribute);
router.put('/bend/:id', verifyTokenUserOrAdmin, AttributesController.editBendAttribute);
router.delete('/bend/:id', verifyTokenUserOrAdmin, AttributesController.deleteBendAttribute);


// Connection Cable Attributes
router.get('/connection-cable', AttributesController.listConnectionCableAttribute);
router.post('/connection-cable', verifyTokenUserOrAdmin, AttributesController.addConnectionCableAttribute);
router.put('/connection-cable/:id', verifyTokenUserOrAdmin, AttributesController.editConnectionCableAttribute);
router.delete('/connection-cable/:id', verifyTokenUserOrAdmin, AttributesController.deleteConnectionCableAttribute);

// Driver Attribute Routes
router.get('/driver', AttributesController.listDriverAttribute);
router.post('/driver', verifyTokenUserOrAdmin, AttributesController.addDriverAttribute);
router.put('/driver/:id', verifyTokenUserOrAdmin, AttributesController.editDriverAttribute);
router.delete('/driver/:id', verifyTokenUserOrAdmin, AttributesController.deleteDriverAttribute);

// Finish Attribute Routes
router.get('/finish', AttributesController.listFinishAttribute);
router.post('/finish', verifyTokenUserOrAdmin, AttributesController.addFinishAttribute);
router.put('/finish/:id', verifyTokenUserOrAdmin, AttributesController.editFinishAttribute);
router.delete('/finish/:id', verifyTokenUserOrAdmin, AttributesController.deleteFinishAttribute);

// Lens Attribute Routes
router.get('/lens', AttributesController.listLensAttribute);
router.post('/lens', verifyTokenUserOrAdmin, AttributesController.addLensAttribute);
router.put('/lens/:id', verifyTokenUserOrAdmin, AttributesController.editLensAttribute);
router.delete('/lens/:id', verifyTokenUserOrAdmin, AttributesController.deleteLensAttribute);

// Light Colour Attribute Routes
router.get('/light-colour', AttributesController.listLightColourAttribute);
router.post('/light-colour', verifyTokenUserOrAdmin, AttributesController.addLightColourAttribute);
router.put('/light-colour/:id', verifyTokenUserOrAdmin, AttributesController.editLightColourAttribute);
router.delete('/light-colour/:id', verifyTokenUserOrAdmin, AttributesController.deleteLightColourAttribute);

// Suspension Attribute Routes
router.get('/suspension', AttributesController.listSuspensionAttribute);
router.post('/suspension', verifyTokenUserOrAdmin, AttributesController.addSuspensionAttribute);
router.put('/suspension/:id', verifyTokenUserOrAdmin, AttributesController.editSuspensionAttribute);
router.delete('/suspension/:id', verifyTokenUserOrAdmin, AttributesController.deleteSuspensionAttribute);

// Trim Attribute Routes
router.get('/trim', AttributesController.listTrimAttribute);
router.post('/trim', verifyTokenUserOrAdmin, AttributesController.addTrimAttribute);
router.put('/trim/:id', verifyTokenUserOrAdmin, AttributesController.editTrimAttribute);
router.delete('/trim/:id', verifyTokenUserOrAdmin, AttributesController.deleteTrimAttribute);

// Watts Attribute Routes
router.get('/watts', AttributesController.listWattsAttribute);
router.post('/watts', verifyTokenUserOrAdmin, AttributesController.addWattsAttribute);
router.put('/watts/:id', verifyTokenUserOrAdmin, AttributesController.editWattsAttribute);
router.delete('/watts/:id', verifyTokenUserOrAdmin, AttributesController.deleteWattsAttribute);


module.exports = router;
