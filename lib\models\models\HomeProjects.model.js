const mongoose = require('mongoose'),
    Schema = mongoose.Schema,
    ObjectId = mongoose.Types.ObjectId;

const HomeProjectTileSchema = new Schema({
    HomeProjectTitle: {
        type: String,
        default: '',
    },
    HomeProjectDescription: {
        type: String,
        default: '',
    },
    HomeProjectImage: {
        type: String,
        default: '',
    },
    HomeProjectURL: {
        type: String,
        default: '',
    },
    isActive: {
        type: Boolean,
        default: false,
    },
    sortOrder: {
        type: Number,
        default: 0,
    },
    isDeleted: {
        type: Boolean,
        default: false,
    },
});

const HomeProjectsSchema = new Schema(
    {
        HomeProjectsMainTitle: {
            type: String,
            default: '',
        },
        
        carousel: [HomeProjectTileSchema],
    },
    {
        timestamps: {
            createdAt: 'created',
            updatedAt: 'updated',
        },
        id: false,
        toJSON: {
            getters: true,
        },
        toObject: {
            getters: true,
        },
    }
);

// Pre-save middleware to auto-increment sortOrder for new carousel items
HomeProjectsSchema.pre('save', function(next) {
    const doc = this;

    if (doc.isNew) {
        if (doc.carousel && doc.carousel.length > 0) {
            doc.carousel.forEach((carouselItem, index) => {
                if (carouselItem.isNew) {
                    const maxSortOrder = Math.max(...doc.carousel.map(item => item.sortOrder), 0);
                    carouselItem.sortOrder = maxSortOrder + 1;
                }
            });
        }
    }
    next();
});

module.exports = mongoose.model('HomeProjects', HomeProjectsSchema);
