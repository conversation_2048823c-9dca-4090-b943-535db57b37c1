const {
    models: { Page, Resource, Legal, Contact, Company, Project },
} = require('../../../../lib/models');

class PageController {
    async page(req, res) {
        const page = await Page.findOne({
            slug: req.params.slug,
            isSuspended: false,
        });

        if (!page) {
            return res.warn('', req.__('PAGE_NOT_EXISTS'));
        }

        return res.success(page);
    }

    async addOrUpdateResource(req, res) {
        try {
            const {
                ResourceBannerType,
                ResourcesBannerTitle,
                ResourcesBannerFileName,
                isTextVisible,
                pdfDriveUrl,
            } = req.body;

            const content = await Resource.findOneAndUpdate(
                {},
                {
                    $set: {
                        ResourceBannerType,
                        ResourcesBannerTitle,
                        ResourcesBannerFileName,
                        isTextVisible,
                        pdfDriveUrl,
                    },
                },
                {
                    new: true,
                    upsert: true,
                }
            );
            return res.success(content, req.__('RESOURCE_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async getResourceContent(req, res) {
        try {
            const content = await Resource.findOne({});
            if (!content) {
                return res.notFound({ success: false, message: 'Data not found' });
            }

            return res.success(content, req.__('RESOURCE_FETCH_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async addOrUpdateContact(req, res) {
        try {
            const {
                ContactBannerType,
                ContactBannerTitle,
                ContactBannerFileName,
                ContactCaption,
                ContactAddressLabel,
                ContactAddress,
                ContactServiceLabel,
                ContactServiceHours,
            } = req.body;

            const content = await Contact.findOneAndUpdate(
                {},
                {
                    $set: {
                        ContactBannerType,
                        ContactBannerTitle,
                        ContactBannerFileName,
                        ContactCaption,
                        ContactAddressLabel,
                        ContactAddress,
                        ContactServiceLabel,
                        ContactServiceHours,
                    },
                },
                {
                    new: true,
                    upsert: true,
                }
            );
            return res.success(content, req.__('DATA_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async getContactContent(req, res) {
        try {
            const content = await Contact.findOne({});
            if (!content) {
                return res.notFound({ success: false, message: 'Data not found' });
            }

            return res.success(content, req.__('DATA_FETCH_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add or update Home projects
    async addOrUpdateLegal(req, res) {
        try {
            const { LegalBannerType, LegalBannerTitle, LegalBannerFileName, LegalDocs } = req.body;

            const content = await Legal.findOneAndUpdate(
                {},
                { $set: { LegalBannerType, LegalBannerTitle, LegalBannerFileName, LegalDocs } },
                {
                    new: true,
                    upsert: true,
                }
            );
            return res.success(content, req.__('LEGAL_SECTION_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add a new item to the tiles
    async addLegalDocs(req, res) {
        try {
            const { LegalDocLabel, LegalDocContent, LegalDocFileName, isActive } = req.body;
            const doc = await Legal.findOne({});
            if (!doc) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const maxSortOrder = doc.LegalDocs.length > 0 ? Math.max(...doc.LegalDocs.map(item => item.sortOrder)) : 0;
            const newItem = {
                LegalDocLabel,
                LegalDocContent,
                LegalDocFileName,
                isActive,
                sortOrder: maxSortOrder + 1,
            };

            doc.LegalDocs.push(newItem);
            await doc.save();
            return res.success(doc, req.__('DOC_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding new doc item:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // View the HomeTiles section
    async viewLegal(req, res) {
        try {
            const content = await Legal.findOne({});

            if (!content) {
                return res.warn({}, req.__('DATA_SECTION_NOT_FOUND'));
            }

            return res.success(content, req.__('DATA_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async createOrUpdateCompany(req, res) {
        try {
            const {
                CompanyBannerTitle,
                CompanyBannerFileName,
                ProfileMainTitle,
                ProfileMainCaption,
                ProfileYears,
                VisionMissionValuesImageFileName,
                VisionImageFileName,
                MissionImageFileName,
                ValuesImageFileName,
                MissionImageFileNameMobi,
                BrandsBannerImageFileName,
                Commitments,
                CompanyLegalTitle,
                CompanyLegalImageFileName,
                CompanyLegalURL,
                CompanyCareersTitle,
                CompanyCareersImageFileName,
                CompanyCareersURL,
            } = req.body;

            const companyData = await Company.findOneAndUpdate(
                {},
                {
                    $set: {
                        CompanyBannerTitle,
                        CompanyBannerFileName,
                        ProfileMainTitle,
                        ProfileMainCaption,
                        ProfileYears,
                        VisionMissionValuesImageFileName,
                        VisionImageFileName,
                        MissionImageFileName,
                        ValuesImageFileName,
                        MissionImageFileNameMobi,
                        BrandsBannerImageFileName,
                        Commitments,
                        CompanyLegalTitle,
                        CompanyLegalImageFileName,
                        CompanyLegalURL,
                        CompanyCareersTitle,
                        CompanyCareersImageFileName,
                        CompanyCareersURL,
                    },
                },
                { new: true, upsert: true }
            );

            return res.success(companyData, req.__('DATA_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating company data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async getCompany(req, res) {
        try {
            const companyData = await Company.findOne({});
            if (!companyData) {
                return res.notFound({ success: false, message: 'Data not found' });
            }
            return res.success(companyData, req.__('DATA_FETCH_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching company data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async addCompnayProfileYear(req, res) {
        try {
            const { ProfileYearImage, ProfileYearLabel, ProfileYearCaption } = req.body;

            const profileYear = new Company.ProfileYears({
                ProfileYearImage,
                ProfileYearLabel,
                ProfileYearCaption,
            });

            await profileYear.save();

            return res.success(profileYear, req.__('PROFILE_YEAR_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error adding profile year:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async addCompnayCommitment(req, res) {
        try {
            const { title, overviewImageFileName, detailImageFileName, label, sortOrder } = req.body;

            const companyCommitment = new Company.Commitments({
                title,
                overviewImageFileName,
                detailImageFileName,
                label,
                sortOrder,
            });

            await companyCommitment.save();

            return res.success(companyCommitment, req.__('COMPANY_COMMITMENT_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error adding profile year:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async deleteCompanyProfileYear(req, res) {
        try {
            const { id } = req.params;

            const profileYear = await Company.ProfileYearSchema.findByIdAndDelete(id);

            if (!profileYear) {
                return res.notFound({}, req.__('PROFILE_YEAR_NOT_FOUND'));
            }

            return res.success({}, req.__('PROFILE_YEAR_DELETED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error deleting profile year:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async deleteCompanyCommitment(req, res) {
        try {
            const { id } = req.params;

            const companyCommitment = await Company.CommitmentSchema.findByIdAndDelete(id);

            if (!companyCommitment) {
                return res.notFound({}, req.__('COMPANY_COMMITMENT_NOT_FOUND'));
            }

            return res.success({}, req.__('COMPANY_COMMITMENT_DELETED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error deleting company commitment:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async addUpdateProjectBanner(req, res) {
        const { ProjectsBannerType, ProjectsBannerTitle, ProjectsBannerFileName, isTextVisible } = req.body;
        try {
            const content = await Project.findOneAndUpdate(
                {},
                { $set: { ProjectsBannerType, ProjectsBannerTitle, ProjectsBannerFileName, isTextVisible } },
                {
                    new: true,
                    upsert: true,
                }
            );
            return res.success(content, req.__('PROJECT_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error adding project banner:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async getProjectContent(req, res) {
        try {
            const content = await Project.findOne({});
            if (!content) {
                return res.notFound({ success: false, message: 'Data not found' });
            }
            return res.success(content, req.__('PROJECT_FETCH_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async addProjectCard(req, res) {
        try {
            const { mediaType, ProjectImage, ProjectName, ProjectOverview, ProjectURL, isActive, sortOrder } = req.body;

            const card = new Project.ProjectCard({
                mediaType,
                ProjectImage,
                ProjectName,
                ProjectOverview,
                ProjectURL,
                isActive,
                sortOrder,
            });

            await card.save();

            return res.success(card, req.__('PROJECT_CARD_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error adding project card:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async editProjectCard(req, res) {
        try {
            const { cardId } = req.params; // assuming cardId is passed in the URL params
            const { mediaType, ProjectImage, ProjectName, ProjectOverview, ProjectURL, isActive } = req.body;

            const project = await Project.findOneAndUpdate(
                { 'ProjectCard._id': cardId },
                {
                    $set: {
                        'ProjectCard.$.mediaType': mediaType,
                        'ProjectCard.$.ProjectImage': ProjectImage,
                        'ProjectCard.$.ProjectName': ProjectName,
                        'ProjectCard.$.ProjectOverview': ProjectOverview,
                        'ProjectCard.$.ProjectURL': ProjectURL,
                        'ProjectCard.$.isActive': isActive,
                    },
                },
                { new: true }
            );

            if (!project) {
                return res.warn({}, req.__('PROJECT_CARD_NOT_FOUND'));
            }

            return res.success(project, req.__('PROJECT_CARD_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error editing project card:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async deleteProjectCard(req, res) {
        try {
            const { cardId } = req.params;

            const project = await Project.findOneAndUpdate(
                {},
                { $pull: { ProjectCard: { _id: cardId } } },
                { new: true }
            );

            if (!project) {
                return res.warn({}, req.__('PROJECT_CARD_NOT_FOUND'));
            }

            return res.success(project, req.__('PROJECT_CARD_REMOVED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error deleting project card:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async viewProjectCards(req, res) {
        try {
            // If you want to filter out deleted cards, add { isDeleted: false } inside the $match stage
            const project = await Project.findOne({}, { ProjectCard: 1 });

            if (!project || project.ProjectCard.length === 0) {
                return res.warn({}, req.__('NO_PROJECT_CARDS_FOUND'));
            }

            // Optionally filter out deleted cards if you want to return only active cards
            const activeCards = project.ProjectCard.filter(card => !card.isDeleted);

            return res.success(activeCards, req.__('PROJECT_CARDS_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error fetching project cards:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
}

module.exports = new PageController();
