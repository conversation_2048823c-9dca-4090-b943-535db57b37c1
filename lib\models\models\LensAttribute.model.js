const mongoose = require('mongoose');

// Beam <PERSON>le Attribute Schema
const LensAttributeSchema = new mongoose.Schema({
    name: {
        type: String,
        default: '',
    },
    modelSlug: {
        type: String,
        default: 'lens',
    },
    isActive: {
        type: Boolean,
        default: true,
    },
    isDelete: {
        type: Boolean,
        default: false,
    },
});

module.exports = mongoose.model('LensAttribute', LensAttributeSchema);
