const express = require('express');
const router = express.Router();
const PageController = require('./PageController');
const validations = require('./PageValidations');
const { validate } = require('../../util/validations');

router.get('/company', PageController.getCompany);
router.put('/company', PageController.createOrUpdateCompany);
router.post('/company/profile-year', PageController.addCompnayProfileYear);
router.post('/company/commitment', PageController.addCompnayCommitment);
router.delete('/company/profile-year/:id', PageController.deleteCompanyProfileYear);
router.delete('/company/commitment/:id', PageController.deleteCompanyCommitment);
router.get('/contact', PageController.getContactContent);
router.put('/contact', PageController.addOrUpdateContact);
router.get('/resource', PageController.getResourceContent);
router.put('/resource', PageController.addOrUpdateResource);
router.get('/legal', PageController.viewLegal);
router.put('/legal', PageController.addOrUpdateLegal);
router.post('/legal/docs', PageController.addLegalDocs);
router.put('/projects', PageController.addUpdateProjectBanner);
router.get('/projects', PageController.getProjectContent);
router.get('/projects/card', PageController.viewProjectCards);
router.post('/projects/card', PageController.addProjectCard);
router.put('/projects/card/:id', PageController.editProjectCard);
router.delete('/projects/card/:id', PageController.deleteProjectCard);
router.get('/:slug', validate(validations.requireSlug, 'params', {}, '/'), PageController.page);

module.exports = router;
