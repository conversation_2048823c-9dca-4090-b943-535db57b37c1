const express = require('express');
const router = express.Router();
const FiltersController = require('./FiltersController');
const validations = require('./FiltersValidations');
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenUserOrAdmin } = require('../../util/auth');

//BRAND FILTERS
router.get('/brand',  FiltersController.listBrandFilters);
router.post('/brand', verifyTokenUserOrAdmin, FiltersController.addBrandFilter);
router.put('/brand/:id', verifyTokenUserOrAdmin, FiltersController.editBrandFilter);
router.delete('/brand/:id', verifyTokenUserOrAdmin, FiltersController.deleteBrandFilter);

//TYPE FILTERS
router.get('/type',  FiltersController.listTypeFilters);
router.post('/type', verifyTokenUserOrAdmin, FiltersController.addTypeFilter);
router.put('/type/:id', verifyTokenUserOrAdmin, FiltersController.editTypeFilter);
router.delete('/type/:id', verifyTokenUserOrAdmin, FiltersController.deleteTypeFilter);

//SERIES FILTERS
router.get('/series',  FiltersController.listSeriesFilters);
router.post('/series', verifyTokenUserOrAdmin, FiltersController.addSeriesFilter);
router.put('/series/:id', verifyTokenUserOrAdmin, FiltersController.editSeriesFilter);
router.delete('/series/:id', verifyTokenUserOrAdmin, FiltersController.deleteSeriesFilter);

//Application FILTERS
router.get('/application',  FiltersController.listApplicationFilters);
router.post('/application', verifyTokenUserOrAdmin, FiltersController.addApplicationFilter);
router.put('/application/:id', verifyTokenUserOrAdmin, FiltersController.editApplicationFilter);
router.delete('/application/:id', verifyTokenUserOrAdmin, FiltersController.deleteApplicationFilter);

//Category FILTERS
router.get('/category',  FiltersController.listCategoryFilters);
router.post('/category', verifyTokenUserOrAdmin, FiltersController.addCategoryFilter);
router.put('/category/:id', verifyTokenUserOrAdmin, FiltersController.editCategoryFilter);
router.delete('/category/:id', verifyTokenUserOrAdmin, FiltersController.deleteCategoryFilter);

//Family FILTERS
router.get('/family',  FiltersController.listFamilyFilters);
router.post('/family', verifyTokenUserOrAdmin, FiltersController.addFamilyFilter);
router.put('/family/:id', verifyTokenUserOrAdmin, FiltersController.editFamilyFilter);
router.delete('/family/:id', verifyTokenUserOrAdmin, FiltersController.deleteFamilyFilter);

//Light Colors FILTERS
router.get('/light-colors',  FiltersController.listLightColorsFilters);
router.post('/light-colors', verifyTokenUserOrAdmin, FiltersController.addLightColorsFilter);
router.put('/light-colors/:id', verifyTokenUserOrAdmin, FiltersController.editLightColorsFilter);
router.delete('/light-colors/:id', verifyTokenUserOrAdmin, FiltersController.deleteLightColorsFilter);

//Output Lemuns Fixture FILTERS
router.get('/output-lumens-fixture',  FiltersController.listOutputLumensFixtureFilters);
router.post('/output-lumens-fixture', verifyTokenUserOrAdmin, FiltersController.addOutputLumensFixtureFilter);
router.put('/output-lumens-fixture/:id', verifyTokenUserOrAdmin, FiltersController.editOutputLumensFixtureFilter);
router.delete('/output-lumens-fixture/:id', verifyTokenUserOrAdmin, FiltersController.deleteOutputLumensFixtureFilter);

//Output Lemuns Foot FILTERS
router.get('/output-lumens-foot',  FiltersController.listOutputLumensFootFilters);
router.post('/output-lumens-foot', verifyTokenUserOrAdmin, FiltersController.addOutputLumensFootFilter);
router.put('/output-lumens-foot/:id', verifyTokenUserOrAdmin, FiltersController.editOutputLumensFootFilter);
router.delete('/output-lumens-foot/:id', verifyTokenUserOrAdmin, FiltersController.deleteOutputLumensFootFilter);

//Output Watts Fixture FILTERS
router.get('/output-watts-fixture',  FiltersController.listOutputWattsFixtureFilters);
router.post('/output-watts-fixture', verifyTokenUserOrAdmin, FiltersController.addOutputWattsFixtureFilter);
router.put('/output-watts-fixture/:id', verifyTokenUserOrAdmin, FiltersController.editOutputWattsFixtureFilter);
router.delete('/output-watts-fixture/:id', verifyTokenUserOrAdmin, FiltersController.deleteOutputWattsFixtureFilter);

//Output Watts Foot FILTERS
router.get('/output-watts-foot',  FiltersController.listOutputWattsFootFilters);
router.post('/output-watts-foot', verifyTokenUserOrAdmin, FiltersController.addOutputWattsFootFilter);
router.put('/output-watts-foot/:id', verifyTokenUserOrAdmin, FiltersController.editOutputWattsFootFilter);
router.delete('/output-watts-foot/:id', verifyTokenUserOrAdmin, FiltersController.deleteOutputWattsFootFilter);

//Material FILTERS
router.get('/material',  FiltersController.listMaterialFilters);
router.post('/material', verifyTokenUserOrAdmin, FiltersController.addMaterialFilter);
router.put('/material/:id', verifyTokenUserOrAdmin, FiltersController.editMaterialFilter);
router.delete('/material/:id', verifyTokenUserOrAdmin, FiltersController.deleteMaterialFilter);

//Collection FILTERS
router.get('/collection',  FiltersController.listCollectionFilters);
router.post('/collection', verifyTokenUserOrAdmin, FiltersController.addCollectionFilter);
router.put('/collection/:id', verifyTokenUserOrAdmin, FiltersController.editCollectionFilter);
router.delete('/collection/:id', verifyTokenUserOrAdmin, FiltersController.deleteCollectionFilter);

module.exports = router;
