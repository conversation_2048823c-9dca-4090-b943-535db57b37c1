const mongoose = require('mongoose');

// Beam Angle Attribute Schema
const TrimAttributeSchema = new mongoose.Schema({
    name: {
        type: String,
        default: '',
    },
    modelSlug: {
        type: String,
        default: 'trm',
    },
    isActive: {
        type: Boolean,
        default: true,
    },
    isDelete: {
        type: Boolean,
        default: false,
    },
});

module.exports = mongoose.model('TrimAttribute', TrimAttributeSchema);
