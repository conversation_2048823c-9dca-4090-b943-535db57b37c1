const express = require('express');
const router = express.Router();
const { validate } = require('../../util/validations');
const { verifyTokenAdmin } = require('../../util/auth');
const BrandController = require('./BrandController');

// SGI BRAND ROUTES
router.post('/sgi-banner', verifyTokenAdmin, BrandController.sgiBrandAddHeroSection);
router.get('/sgi-banner', BrandController.sgiBrandViewBannerSection);
router.delete('/sgi-banner/:id', verifyTokenAdmin, BrandController.sgiBrandDeleteBannerSection);

router.put('/sgi-value-image', verifyTokenAdmin, BrandController.sgiBrandAddOrUpdateValueImage);
router.get('/sgi-value-image', BrandController.sgiBrandViewValueImage);

router.get('/sgi-product-tile', BrandController.sgiBranViewProductTiles);
router.post('/sgi-product-tile', verifyTokenAdmin, BrandController.sgiBranAddProductTile);
router.put('/sgi-product-tile/:id', verifyTokenAdmin, BrandController.sgiBranEditProductTile);
router.delete('/sgi-product-tile/:id', verifyTokenAdmin, BrandController.sgiBranDeleteProductTile);

// GENIE BRAND ROUTES
router.post('/genie-banner', verifyTokenAdmin, BrandController.genieBrandAddHeroSection);
router.get('/genie-banner', BrandController.genieBrandViewBannerSection);
router.delete('/genie-banner/:id', verifyTokenAdmin, BrandController.genieBrandDeleteBannerSection);

router.put('/genie-value-image', verifyTokenAdmin, BrandController.genieBrandAddOrUpdateValueImage);
router.get('/genie-value-image', BrandController.genieBrandViewValueImage);

router.get('/genie-product-tile', BrandController.genieBranViewProductTiles);
router.post('/genie-product-tile', verifyTokenAdmin, BrandController.genieBranAddProductTile);
router.put('/genie-product-tile/:id', verifyTokenAdmin, BrandController.genieBranEditProductTile);
router.delete('/genie-product-tile/:id', verifyTokenAdmin, BrandController.genieBranDeleteProductTile);

// EFFICACY BRAND ROUTES
router.post('/efficacy-banner', verifyTokenAdmin, BrandController.efficacyBrandAddHeroSection);
router.get('/efficacy-banner', BrandController.efficacyBrandViewBannerSection);
router.delete('/efficacy-banner/:id', verifyTokenAdmin, BrandController.efficacyBrandDeleteBannerSection);

router.put('/efficacy-value-image', verifyTokenAdmin, BrandController.efficacyBrandAddOrUpdateValueImage);
router.get('/efficacy-value-image', BrandController.efficacyBrandViewValueImage);

router.get('/efficacy-category-tile', BrandController.efficacyBranViewCategoryTiles);
router.post('/efficacy-category-tile', verifyTokenAdmin, BrandController.efficacyBranAddCategoryTile);
router.put('/efficacy-category-tile/:id', verifyTokenAdmin, BrandController.efficacyBranEditCategoryTile);
router.delete('/efficacy-category-tile/:id', verifyTokenAdmin, BrandController.efficacyBranDeleteCategoryTile);

router.get('/efficacy-collections-tile', BrandController.efficacyBranViewCollectionTiles);
router.post('/efficacy-collections-tile', verifyTokenAdmin, BrandController.efficacyBranAddCollectionTile);
router.put('/efficacy-collections-tile/:id', verifyTokenAdmin, BrandController.efficacyBranEditCollectionTile);
router.delete('/efficacy-collections-tile/:id', verifyTokenAdmin, BrandController.efficacyBranDeleteCollectionTile);

router.get('/efficacy-finishes-tile', BrandController.efficacyBranViewFinishesTiles);
router.post('/efficacy-finishes-tile', verifyTokenAdmin, BrandController.efficacyBranAddFinishesTile);
router.put('/efficacy-finishes-tile/:id', verifyTokenAdmin, BrandController.efficacyBranEditFinishesTile);
router.delete('/efficacy-finishes-tile/:id', verifyTokenAdmin, BrandController.efficacyBranDeleteFinishesTile);

//AASATI BRAND ROUTES
router.post('/aasatti-banner', verifyTokenAdmin, BrandController.aasatiBrandAddHeroSection);
router.get('/aasatti-banner', BrandController.aasatiBrandViewBannerSection);
router.delete('/aasatti-banner/:id', verifyTokenAdmin, BrandController.aasatiBrandDeleteBannerSection);

router.put('/aasatti-value-image', verifyTokenAdmin, BrandController.aasatiBrandAddOrUpdateValueImage);
router.get('/aasatti-value-image', BrandController.aasatiBrandViewValueImage);

router.get('/aasatti-inspiration', BrandController.aasatiBrandViewInspiration);
router.post('/aasatti-inspiration', verifyTokenAdmin, BrandController.aasatiBrandAddInspiration);
router.put('/aasatti-inspiration/:id', verifyTokenAdmin, BrandController.aasatiBrandEditInspiration);
router.delete('/aasatti-inspiration/:id', verifyTokenAdmin, BrandController.aasatiBrandDeleteInspiration);

router.get('/aasatti-product-tile', BrandController.aasatiBrandViewProductTiles);
router.post('/aasatti-product-tile', verifyTokenAdmin, BrandController.aasatiBrandAddProductTile);
router.put('/aasatti-product-tile/:id', verifyTokenAdmin, BrandController.aasatiBrandEditProductTile);
router.delete('/aasatti-product-tile/:id', verifyTokenAdmin, BrandController.aasatiBrandDeleteProductTile);
module.exports = router;
