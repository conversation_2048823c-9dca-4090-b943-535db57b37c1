const mongoose = require('mongoose');

// Main Product Filter Schema
const AntiGlareAttributeSchema = new mongoose.Schema({
    name: {
        type: String,
        default: '',
    },
    modelSlug: {
        type: String,
        default: 'ag',
    },
    isActive: {
        type: Boolean,
        default: true,
    },
    isDelete: {
        type: Boolean,
        default: false,
    },
});

module.exports = mongoose.model('AntiGlareAttribute', AntiGlareAttributeSchema);
