const {
    models: { BrandPopUpSGi, BrandPopUpGENIE, BrandPopUpEfficacy, BrandPopUpAASATI },
} = require('../../../../lib/models');
const { utcDateTime } = require('../../../../lib/util');

class BrandController {
    /*************************** SGi BRAND APIs Start ******************************** */
    //BRAND SIG BANNERS API
    async sgiBrandAddHeroSection(req, res) {
        try {
            const { banners } = req.body; // Expecting an array of banner objects

            let brandPopUp = await BrandPopUpSGi.findOne();
            if (!brandPopUp) {
                brandPopUp = new BrandPopUpSGi();
            }

            // Update the entire BrandPopUpGENIEBanners array
            brandPopUp.BrandPopUpSGiBanners = banners;

            await brandPopUp.save();
            return res.success(brandPopUp.BrandPopUpSGiBanners, req.__('Banners added/updated successfully'));
        } catch (error) {
            console.error('Error while adding banners section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async sgiBrandViewBannerSection(req, res) {
        const { isActive = 'true' } = req.query; // Default to 'true' if not provided

        try {
            const brandPopUp = await BrandPopUpSGi.findOne({});

            if (!brandPopUp) {
                return res.warn({}, req.__('BANNERS_NOT_FOUND'));
            }

            // Filter the banners according to the isActive condition
            const filteredBanners = brandPopUp.BrandPopUpSGiBanners.filter(
                banner => banner.isActive === (isActive === 'true')
            );

            return res.success(filteredBanners, req.__('DATA_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async sgiBrandDeleteBannerSection(req, res) {
        try {
            const { id } = req.params;

            const brandPopUp = await BrandPopUpSGi.findOne();
            if (!brandPopUp) {
                return res.warn({}, req.__('BANNERS_NOT_FOUND'));
            }

            const bannerIndex = brandPopUp.BrandPopUpSGiBanners.findIndex(b => b._id.toString() === id);
            if (bannerIndex > -1) {
                brandPopUp.BrandPopUpSGiBanners.splice(bannerIndex, 1);
                await brandPopUp.save();
                return res.success({}, req.__('BANNER_DELETE_SUCCESSFULLY'));
            } else {
                return res.warn({}, req.__('BANNER_NOT_FOUND'));
            }
        } catch (error) {
            console.error('Error while fetching data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Controller method
    async sgiBrandUpdateHeroSection(req, res) {
        try {
            const { banners } = req.body; // Expecting an array of banner objects

            // Check if the banners array exists in the request body
            if (!banners || !Array.isArray(banners)) {
                return res.warn({}, req.__('INVALID_BANNERS_DATA'));
            }

            let brandPopUp = await BrandPopUpSGi.findOne();
            if (!brandPopUp) {
                return res.notFound({}, req.__('BRAND_NOT_FOUND'));
            }

            // Update the entire BrandPopUpSGiBanners array with new banners
            brandPopUp.BrandPopUpSGiBanners = banners;

            await brandPopUp.save();

            return res.success(brandPopUp.BrandPopUpSGiBanners, req.__('Banners updated successfully'));
        } catch (error) {
            console.error('Error while updating banners section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    //BRAND SIG VIEW VALUE IMAGE API
    async sgiBrandAddOrUpdateValueImage(req, res) {
        try {
            const { BrandValueSGi, BrandValueSGiMobi } = req.body; // Expecting an array of banner objects

            let brandPopUp = await BrandPopUpSGi.findOne();
            if (!brandPopUp) {
                brandPopUp = new BrandPopUpSGi();
            }

            // Update the entire BrandPopUpSGiBanners array
            brandPopUp.BrandValueSGi = BrandValueSGi;
            brandPopUp.BrandValueSGiMobi = BrandValueSGiMobi;

            await brandPopUp.save();
            return res.success(
                {
                    BrandValueSGi: brandPopUp.BrandValueSGi,
                    BrandValueSGiMobi: brandPopUp.BrandValueSGiMobi,
                },
                req.__('DATA_UPDATE_SUCCESSFULLY')
            );
        } catch (error) {
            console.error('Error while adding banners section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async sgiBrandViewValueImage(req, res) {
        try {
            let brandPopUp = await BrandPopUpSGi.findOne();
            console.log(" brandPopUp >>",brandPopUp)

            if (!brandPopUp) {
                return res.warn({}, req.__('DATA_NOT_FOUND'));
            }

            return res.success(
                {
                    BrandValueSGi: brandPopUp.BrandValueSGi,
                    BrandValueSGiMobi: brandPopUp.BrandValueSGiMobi,
                },
                req.__('DATA_FETCHED_SUCCESSFULLY')
            );
        } catch (error) {
            console.error('Error while fetching data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    //BRAND SIG PRODUCT TILES API
    async sgiBranAddProductTile(req, res) {
        try {
            const { TileImage, TileHoverImage, isActive, sortOrder, BrandUrl } = req.body;

            const brandPopUp = await BrandPopUpSGi.findOne({});
            if (!brandPopUp) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const maxSortOrder =
                brandPopUp.BrandProductTilesSGi.length > 0
                    ? Math.max(...brandPopUp.BrandProductTilesSGi.map(item => item.sortOrder))
                    : 0;

            const newTile = {
                TileImage,
                TileHoverImage,
                isActive,
                BrandUrl,
                sortOrder: maxSortOrder + 1,
            };

            brandPopUp.BrandProductTilesSGi.push(newTile);
            await brandPopUp.save();

            return res.success(brandPopUp, req.__('PRODUCT_TILE_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding product tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async sgiBranEditProductTile(req, res) {
        try {
            const { id } = req.params;
            const { TileImage, TileHoverImage, isActive, sortOrder, BrandUrl } = req.body;

            const brandPopUp = await BrandPopUpSGi.findOne({});
            if (!brandPopUp) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const tileIndex = brandPopUp.BrandProductTilesSGi.findIndex(tile => tile._id.toString() === id);
            if (tileIndex === -1) {
                return res.notFound({ success: false, message: 'Product tile not found' });
            }

            brandPopUp.BrandProductTilesSGi[tileIndex] = {
                ...brandPopUp.BrandProductTilesSGi[tileIndex],
                TileImage,
                TileHoverImage,
                isActive,
                sortOrder,
                BrandUrl
            };

            await brandPopUp.save();

            return res.success(brandPopUp.BrandProductTilesSGi, req.__('PRODUCT_TILE_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while editing product tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // View all Brand Product Tiles
    async sgiBranViewProductTiles(req, res) {
        const { isActive = 'true' } = req.query; // Default to 'true' if not provided
        try {
            const brandPopUp = await BrandPopUpSGi.findOne({}, 'BrandProductTilesSGi');

            if (!brandPopUp) {
                return res.warn({}, req.__('TILES_NOT_FOUND'));
            }
            if (isActive !== 'all') {
                // Filter the banners according to the isActive condition
                const filteredTiles = brandPopUp.BrandProductTilesSGi.filter(
                    tile => tile.isActive === (isActive === 'true')
                );
                return res.success(filteredTiles, req.__('DATA_FETCHED_SUCCESSFULLY'));
            }
            return res.success(brandPopUp.BrandProductTilesSGi, req.__('DATA_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching product tiles:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete a Brand Product Tile
    async sgiBranDeleteProductTile(req, res) {
        try {
            const { id } = req.params;

            const brandPopUp = await BrandPopUpSGi.findOne();
            if (!brandPopUp) {
                return res.warn({}, req.__('TILES_NOT_FOUND'));
            }

            const tileIndex = brandPopUp.BrandProductTilesSGi.findIndex(t => t._id.toString() === id);
            if (tileIndex > -1) {
                brandPopUp.BrandProductTilesSGi.splice(tileIndex, 1);
                await brandPopUp.save();
                return res.success({}, req.__('TILE_DELETED_SUCCESSFULLY'));
            } else {
                return res.warn({}, req.__('TILE_NOT_FOUND'));
            }
        } catch (error) {
            console.error('Error while deleting product tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /*************************** SGi BRAND APIs End *********************************/
    /*************************** GENIE BRAND APIs Start *********************************/

    //BRAND GENIE BANNERS API
    async genieBrandAddHeroSection(req, res) {
        try {
            const { banners } = req.body; // Expecting an array of banner objects

            let brandPopUp = await BrandPopUpGENIE.findOne();
            if (!brandPopUp) {
                brandPopUp = new BrandPopUpGENIE();
            }

            // Update the entire BrandPopUpGENIEBanners array
            brandPopUp.BrandPopUpGENIEBanners = banners;

            await brandPopUp.save();
            return res.success(brandPopUp.BrandPopUpGENIEBanners, req.__('Banners updated successfully'));
        } catch (error) {
            console.error('Error while adding banners section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async genieBrandViewBannerSection(req, res) {
        const { isActive = 'true' } = req.query; // Default to 'true' if not provided

        try {
            const brandPopUp = await BrandPopUpGENIE.findOne({});

            if (!brandPopUp) {
                return res.warn({}, req.__('BANNERS_NOT_FOUND'));
            }

            // Filter the banners according to the isActive condition
            const filteredBanners = brandPopUp.BrandPopUpGENIEBanners.filter(
                banner => banner.isActive === (isActive === 'true')
            );

            return res.success(filteredBanners, req.__('DATA_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async genieBrandDeleteBannerSection(req, res) {
        try {
            const { id } = req.params;

            const brandPopUp = await BrandPopUpGENIE.findOne();
            if (!brandPopUp) {
                return res.warn({}, req.__('BANNERS_NOT_FOUND'));
            }

            const bannerIndex = brandPopUp.BrandPopUpGENIEBanners.findIndex(b => b._id.toString() === id);
            if (bannerIndex > -1) {
                brandPopUp.BrandPopUpGENIEBanners.splice(bannerIndex, 1);
                await brandPopUp.save();
                return res.success({}, req.__('BANNER_DELETE_SUCCESSFULLY'));
            } else {
                return res.warn({}, req.__('BANNER_NOT_FOUND'));
            }
        } catch (error) {
            console.error('Error while fetching data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    //BRAND GENIE VIEW VALUE IMAGE API
    async genieBrandAddOrUpdateValueImage(req, res) {
        try {
            const { BrandValueGENIEImage, BrandValueGENIEImageMobi } = req.body; // Expecting an array of banner objects

            let brandPopUp = await BrandPopUpGENIE.findOne();
            if (!brandPopUp) {
                brandPopUp = new BrandPopUpGENIE();
            }

            // Update the entire BrandPopUpGENIEBanners array
            brandPopUp.BrandValueGENIEImage = BrandValueGENIEImage;
            brandPopUp.BrandValueGENIEImageMobi = BrandValueGENIEImageMobi;

            await brandPopUp.save();
            return res.success(
                {
                    BrandValueGENIEImage: brandPopUp.BrandValueGENIEImage,
                    BrandValueGENIEImageMobi: brandPopUp.BrandValueGENIEImageMobi,
                }, req.__('DATA_UPDATE_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding banners section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async genieBrandViewValueImage(req, res) {
        try {
            const brandPopUp = await BrandPopUpGENIE.findOne({});

            if (!brandPopUp) {
                return res.warn({}, req.__('DATA_NOT_FOUND'));
            }

            return res.success({
                BrandValueGENIEImage: brandPopUp.BrandValueGENIEImage,
                BrandValueGENIEImageMobi: brandPopUp.BrandValueGENIEImageMobi,
            }, req.__('DATA_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    //BRAND GENIE PRODUCT TILES API
    async genieBranAddProductTile(req, res) {
        try {
            const { TileImage, TileHoverImage, isActive, sortOrder, BrandUrl } = req.body;

            const brandPopUp = await BrandPopUpGENIE.findOne({});
            if (!brandPopUp) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const maxSortOrder =
                brandPopUp.BrandProductTilesGENIE.length > 0
                    ? Math.max(...brandPopUp.BrandProductTilesGENIE.map(item => item.sortOrder))
                    : 0;

            const newTile = {
                TileImage,
                TileHoverImage,
                isActive,
                BrandUrl,
                sortOrder: maxSortOrder + 1,
            };

            brandPopUp.BrandProductTilesGENIE.push(newTile);
            await brandPopUp.save();

            return res.success(brandPopUp, req.__('PRODUCT_TILE_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding product tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async genieBranEditProductTile(req, res) {
        try {
            const { id } = req.params;
            const { TileImage, TileHoverImage, isActive, sortOrder, BrandUrl } = req.body;

            const brandPopUp = await BrandPopUpGENIE.findOne({});
            if (!brandPopUp) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const tileIndex = brandPopUp.BrandProductTilesGENIE.findIndex(tile => tile._id.toString() === id);
            if (tileIndex === -1) {
                return res.notFound({ success: false, message: 'Product tile not found' });
            }

            brandPopUp.BrandProductTilesGENIE[tileIndex] = {
                ...brandPopUp.BrandProductTilesGENIE[tileIndex],
                TileImage,
                TileHoverImage,
                isActive,
                sortOrder,
                BrandUrl
            };

            await brandPopUp.save();

            return res.success(brandPopUp.BrandProductTilesGENIE, req.__('PRODUCT_TILE_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while editing product tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // View all Brand Product Tiles
    async genieBranViewProductTiles(req, res) {
        const { isActive = 'true' } = req.query; // Default to 'true' if not provided
        try {
            const brandPopUp = await BrandPopUpGENIE.findOne({}, 'BrandProductTilesGENIE');

            if (!brandPopUp) {
                return res.warn({}, req.__('TILES_NOT_FOUND'));
            }
            if (isActive !== 'all') {
                // Filter the banners according to the isActive condition
                const filteredTiles = brandPopUp.BrandProductTilesGENIE.filter(
                    tile => tile.isActive === (isActive === 'true')
                );
                return res.success(filteredTiles, req.__('DATA_FETCHED_SUCCESSFULLY'));
            }

            return res.success(brandPopUp.BrandProductTilesGENIE, req.__('DATA_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching product tiles:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete a Brand Product Tile
    async genieBranDeleteProductTile(req, res) {
        try {
            const { id } = req.params;

            const brandPopUp = await BrandPopUpGENIE.findOne();
            if (!brandPopUp) {
                return res.warn({}, req.__('TILES_NOT_FOUND'));
            }

            const tileIndex = brandPopUp.BrandProductTilesGENIE.findIndex(t => t._id.toString() === id);
            if (tileIndex > -1) {
                brandPopUp.BrandProductTilesGENIE.splice(tileIndex, 1);
                await brandPopUp.save();
                return res.success({}, req.__('TILE_DELETED_SUCCESSFULLY'));
            } else {
                return res.warn({}, req.__('TILE_NOT_FOUND'));
            }
        } catch (error) {
            console.error('Error while deleting product tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /*************************** GENIE BRAND APIs End *********************************/

    /*************************** Efficacy BRAND APIs Start *********************************/
    //BRAND Efficacy BANNERS API
    async efficacyBrandAddHeroSection(req, res) {
        try {
            const { banners } = req.body; // Expecting an array of banner objects

            let brandPopUp = await BrandPopUpEfficacy.findOne();
            if (!brandPopUp) {
                brandPopUp = new BrandPopUpEfficacy();
            }

            // Update the entire BrandPopUpGENIEBanners array
            brandPopUp.BrandPopUpEfficacyBanners = banners;

            await brandPopUp.save();
            return res.success(brandPopUp.BrandPopUpEfficacyBanners, req.__('Banners added/updated successfully'));
        } catch (error) {
            console.error('Error while adding banners section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async efficacyBrandViewBannerSection(req, res) {
        const { isActive = 'true' } = req.query; // Default to 'true' if not provided

        try {
            const brandPopUp = await BrandPopUpEfficacy.findOne({});

            if (!brandPopUp) {
                return res.warn({}, req.__('BANNERS_NOT_FOUND'));
            }

            // Filter the banners according to the isActive condition
            const filteredBanners = brandPopUp.BrandPopUpEfficacyBanners.filter(
                banner => banner.isActive === (isActive === 'true')
            );

            return res.success(filteredBanners, req.__('DATA_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async efficacyBrandDeleteBannerSection(req, res) {
        try {
            const { id } = req.params;

            const brandPopUp = await BrandPopUpEfficacy.findOne();
            if (!brandPopUp) {
                return res.warn({}, req.__('BANNERS_NOT_FOUND'));
            }

            const bannerIndex = brandPopUp.BrandPopUpEfficacyBanners.findIndex(b => b._id.toString() === id);
            if (bannerIndex > -1) {
                brandPopUp.BrandPopUpEfficacyBanners.splice(bannerIndex, 1);
                await brandPopUp.save();
                return res.success({}, req.__('BANNER_DELETE_SUCCESSFULLY'));
            } else {
                return res.warn({}, req.__('BANNER_NOT_FOUND'));
            }
        } catch (error) {
            console.error('Error while fetching data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    //BRAND efficacy VIEW VALUE IMAGE API
    async efficacyBrandAddOrUpdateValueImage(req, res) {
        try {
            const { BrandValueImage, BrandValueImageMobi } = req.body; // Expecting an array of banner objects

            let brandPopUp = await BrandPopUpEfficacy.findOne();
            if (!brandPopUp) {
                brandPopUp = new BrandPopUpEfficacy();
            }

            // Update the entire BrandPopUpEfficacyBanners array
            brandPopUp.BrandValueImage = BrandValueImage;
            brandPopUp.BrandValueImageMobi = BrandValueImageMobi;

            await brandPopUp.save();
            return res.success({
                BrandValueImage: brandPopUp.BrandValueImage,
                BrandValueImageMobi: brandPopUp.BrandValueImageMobi,
            }, req.__('DATA_UPDATE_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding banners section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async efficacyBrandViewValueImage(req, res) {
        try {
            const brandPopUp = await BrandPopUpEfficacy.findOne({});

            if (!brandPopUp) {
                return res.warn({}, req.__('DATA_NOT_FOUND'));
            }

            return res.success({
                BrandValueImage: brandPopUp.BrandValueImage,
                BrandValueImageMobi: brandPopUp.BrandValueImageMobi,
            }, req.__('DATA_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    //BRAND EFFICACY CATEGORY TILES API
    async efficacyBranAddCategoryTile(req, res) {
        try {
            const { TileImage, TileHoverImage, isActive, sortOrder, BrandUrl } = req.body;

            const brandPopUp = await BrandPopUpEfficacy.findOne({});
            if (!brandPopUp) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const maxSortOrder =
                brandPopUp.BrandCategoryTiles.length > 0
                    ? Math.max(...brandPopUp.BrandCategoryTiles.map(item => item.sortOrder))
                    : 0;

            const newTile = {
                TileImage,
                TileHoverImage,
                isActive,
                BrandUrl,
                sortOrder: maxSortOrder + 1,
            };

            brandPopUp.BrandCategoryTiles.push(newTile);
            await brandPopUp.save();

            return res.success(brandPopUp, req.__('TILE_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async efficacyBranEditCategoryTile(req, res) {
        try {
            const { id } = req.params;
            const { TileImage, TileHoverImage, isActive, sortOrder, BrandUrl } = req.body;

            const brandPopUp = await BrandPopUpEfficacy.findOne({});
            if (!brandPopUp) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const tileIndex = brandPopUp.BrandCategoryTiles.findIndex(tile => tile._id.toString() === id);
            if (tileIndex === -1) {
                return res.notFound({ success: false, message: 'Product tile not found' });
            }

            brandPopUp.BrandCategoryTiles[tileIndex] = {
                ...brandPopUp.BrandCategoryTiles[tileIndex],
                TileImage,
                TileHoverImage,
                isActive,
                sortOrder,
                BrandUrl
            };

            await brandPopUp.save();

            return res.success(brandPopUp.BrandCategoryTiles, req.__('PRODUCT_TILE_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while editing product tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // View all Brand Product Tiles
    async efficacyBranViewCategoryTiles(req, res) {
        const { isActive = 'true' } = req.query; // Default to 'true' if not provided
        try {
            const brandPopUp = await BrandPopUpEfficacy.findOne({}, 'BrandCategoryTiles');

            if (!brandPopUp) {
                return res.warn({}, req.__('TILES_NOT_FOUND'));
            }
            if (isActive !== 'all') {
                // Filter the banners according to the isActive condition
                const filteredTiles = brandPopUp.BrandCategoryTiles.filter(
                    tile => tile.isActive === (isActive === 'true')
                );
                return res.success(filteredTiles, req.__('DATA_FETCHED_SUCCESSFULLY'));
            }
            return res.success(brandPopUp.BrandCategoryTiles, req.__('DATA_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching product tiles:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete a Brand Product Tile
    async efficacyBranDeleteCategoryTile(req, res) {
        try {
            const { id } = req.params;

            const brandPopUp = await BrandPopUpEfficacy.findOne();
            if (!brandPopUp) {
                return res.warn({}, req.__('TILES_NOT_FOUND'));
            }

            const tileIndex = brandPopUp.BrandCategoryTiles.findIndex(t => t._id.toString() === id);
            if (tileIndex > -1) {
                brandPopUp.BrandCategoryTiles.splice(tileIndex, 1);
                await brandPopUp.save();
                return res.success({}, req.__('TILE_DELETED_SUCCESSFULLY'));
            } else {
                return res.warn({}, req.__('TILE_NOT_FOUND'));
            }
        } catch (error) {
            console.error('Error while deleting product tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    //BRAND EFFICACY COLLECTIONS TILES API
    async efficacyBranAddCollectionTile(req, res) {
        try {
            const { TileImage, TileHoverImage, isActive, sortOrder, BrandUrl } = req.body;

            const brandPopUp = await BrandPopUpEfficacy.findOne({});
            if (!brandPopUp) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const maxSortOrder =
                brandPopUp.BrandCollectionsTiles.length > 0
                    ? Math.max(...brandPopUp.BrandCollectionsTiles.map(item => item.sortOrder))
                    : 0;

            const newTile = {
                TileImage,
                TileHoverImage,
                isActive,
                BrandUrl,
                sortOrder: maxSortOrder + 1,
            };

            brandPopUp.BrandCollectionsTiles.push(newTile);
            await brandPopUp.save();

            return res.success(brandPopUp, req.__('TILE_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async efficacyBranEditCollectionTile(req, res) {
        try {
            const { id } = req.params;
            const { TileImage, TileHoverImage, isActive, sortOrder, BrandUrl } = req.body;

            const brandPopUp = await BrandPopUpEfficacy.findOne({});
            if (!brandPopUp) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const tileIndex = brandPopUp.BrandCollectionsTiles.findIndex(tile => tile._id.toString() === id);
            if (tileIndex === -1) {
                return res.notFound({ success: false, message: 'Product tile not found' });
            }

            brandPopUp.BrandCollectionsTiles[tileIndex] = {
                ...brandPopUp.BrandCollectionsTiles[tileIndex],
                TileImage,
                TileHoverImage,
                isActive,
                BrandUrl,
                sortOrder,
            };

            await brandPopUp.save();

            return res.success(brandPopUp.BrandCollectionsTiles, req.__('PRODUCT_TILE_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while editing product tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // View all Brand Product Tiles
    async efficacyBranViewCollectionTiles(req, res) {
        const { isActive = 'true' } = req.query; // Default to 'true' if not provided
        try {
            const brandPopUp = await BrandPopUpEfficacy.findOne({}, 'BrandCollectionsTiles');

            if (!brandPopUp) {
                return res.warn({}, req.__('TILES_NOT_FOUND'));
            }
            if (isActive !== 'all') {
                // Filter the banners according to the isActive condition
                const filteredTiles = brandPopUp.BrandCollectionsTiles.filter(
                    tile => tile.isActive === (isActive === 'true')
                );
                return res.success(filteredTiles, req.__('DATA_FETCHED_SUCCESSFULLY'));
            }
            return res.success(brandPopUp.BrandCollectionsTiles, req.__('DATA_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching product tiles:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete a Brand Product Tile
    async efficacyBranDeleteCollectionTile(req, res) {
        try {
            const { id } = req.params;

            const brandPopUp = await BrandPopUpEfficacy.findOne();
            if (!brandPopUp) {
                return res.warn({}, req.__('TILES_NOT_FOUND'));
            }

            const tileIndex = brandPopUp.BrandCollectionsTiles.findIndex(t => t._id.toString() === id);
            if (tileIndex > -1) {
                brandPopUp.BrandCollectionsTiles.splice(tileIndex, 1);
                await brandPopUp.save();
                return res.success({}, req.__('TILE_DELETED_SUCCESSFULLY'));
            } else {
                return res.warn({}, req.__('TILE_NOT_FOUND'));
            }
        } catch (error) {
            console.error('Error while deleting product tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    //BRAND FINISHES CATEGORY TILES API
    async efficacyBranAddFinishesTile(req, res) {
        try {
            const { TileImage, TileHoverImage, isActive, sortOrder, BrandUrl } = req.body;

            const brandPopUp = await BrandPopUpEfficacy.findOne({});
            if (!brandPopUp) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const maxSortOrder =
                brandPopUp.BrandFinishesTiles.length > 0
                    ? Math.max(...brandPopUp.BrandFinishesTiles.map(item => item.sortOrder))
                    : 0;

            const newTile = {
                TileImage,
                TileHoverImage,
                isActive,
                BrandUrl,
                sortOrder: maxSortOrder + 1,
            };

            brandPopUp.BrandFinishesTiles.push(newTile);
            await brandPopUp.save();

            return res.success(brandPopUp, req.__('TILE_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async efficacyBranEditFinishesTile(req, res) {
        try {
            const { id } = req.params;
            const { TileImage, TileHoverImage, isActive, sortOrder, BrandUrl } = req.body;

            const brandPopUp = await BrandPopUpEfficacy.findOne({});
            if (!brandPopUp) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const tileIndex = brandPopUp.BrandFinishesTiles.findIndex(tile => tile._id.toString() === id);
            if (tileIndex === -1) {
                return res.notFound({ success: false, message: 'Product tile not found' });
            }

            brandPopUp.BrandFinishesTiles[tileIndex] = {
                ...brandPopUp.BrandFinishesTiles[tileIndex],
                TileImage,
                TileHoverImage,
                isActive,
                sortOrder,
                BrandUrl
            };

            await brandPopUp.save();

            return res.success(brandPopUp.BrandFinishesTiles, req.__('PRODUCT_TILE_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while editing product tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // View all Brand Product Tiles
    async efficacyBranViewFinishesTiles(req, res) {
        const { isActive = 'true' } = req.query; // Default to 'true' if not provided
        try {
            const brandPopUp = await BrandPopUpEfficacy.findOne({}, 'BrandFinishesTiles');

            if (!brandPopUp) {
                return res.warn({}, req.__('TILES_NOT_FOUND'));
            }
            if (isActive !== 'all') {
                // Filter the banners according to the isActive condition
                const filteredTiles = brandPopUp.BrandFinishesTiles.filter(
                    tile => tile.isActive === (isActive === 'true')
                );
                return res.success(filteredTiles, req.__('DATA_FETCHED_SUCCESSFULLY'));
            }
            return res.success(brandPopUp.BrandFinishesTiles, req.__('DATA_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching product tiles:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete a Brand Product Tile
    async efficacyBranDeleteFinishesTile(req, res) {
        try {
            const { id } = req.params;

            const brandPopUp = await BrandPopUpEfficacy.findOne();
            if (!brandPopUp) {
                return res.warn({}, req.__('TILES_NOT_FOUND'));
            }

            const tileIndex = brandPopUp.BrandFinishesTiles.findIndex(t => t._id.toString() === id);
            if (tileIndex > -1) {
                brandPopUp.BrandFinishesTiles.splice(tileIndex, 1);
                await brandPopUp.save();
                return res.success({}, req.__('TILE_DELETED_SUCCESSFULLY'));
            } else {
                return res.warn({}, req.__('TILE_NOT_FOUND'));
            }
        } catch (error) {
            console.error('Error while deleting product tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /*************************** Efficacy BRAND APIs End *********************************/

    /*************************** AASATI BRAND APIs Start *********************************/
    //BRAND AASATI BANNERS API
    async aasatiBrandAddHeroSection(req, res) {
        try {
            const { banners } = req.body; // Expecting an array of banner objects

            let brandPopUp = await BrandPopUpAASATI.findOne();
            if (!brandPopUp) {
                brandPopUp = new BrandPopUpAASATI();
            }

            // Update the entire BrandPopUpAASATIBanners array
            brandPopUp.BrandPopUpAASATIBanners = banners;

            await brandPopUp.save();
            return res.success(brandPopUp.BrandPopUpAASATIBanners, req.__('Banners added/updated successfully'));
        } catch (error) {
            console.error('Error while adding banners section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async aasatiBrandViewBannerSection(req, res) {
        const { isActive = 'true' } = req.query; // Default to 'true' if not provided

        try {
            const brandPopUp = await BrandPopUpAASATI.findOne({});

            if (!brandPopUp) {
                return res.warn({}, req.__('BANNERS_NOT_FOUND'));
            }

            // Filter the banners according to the isActive condition
            const filteredBanners = brandPopUp.BrandPopUpAASATIBanners.filter(
                banner => banner.isActive === (isActive === 'true')
            );

            return res.success(filteredBanners, req.__('DATA_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async aasatiBrandDeleteBannerSection(req, res) {
        try {
            const { id } = req.params;

            const brandPopUp = await BrandPopUpAASATI.findOne();
            if (!brandPopUp) {
                return res.warn({}, req.__('BANNERS_NOT_FOUND'));
            }

            const bannerIndex = brandPopUp.BrandPopUpAASATIBanners.findIndex(b => b._id.toString() === id);
            if (bannerIndex > -1) {
                brandPopUp.BrandPopUpAASATIBanners.splice(bannerIndex, 1);
                await brandPopUp.save();
                return res.success({}, req.__('BANNER_DELETE_SUCCESSFULLY'));
            } else {
                return res.warn({}, req.__('BANNER_NOT_FOUND'));
            }
        } catch (error) {
            console.error('Error while fetching data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    //BRAND aasati VIEW VALUE IMAGE API
    async aasatiBrandAddOrUpdateValueImage(req, res) {
        try {
            const { BrandValueImage, BrandValueImageMobi } = req.body; // Expecting an array of banner objects

            let brandPopUp = await BrandPopUpAASATI.findOne();
            if (!brandPopUp) {
                brandPopUp = new BrandPopUpAASATI();
            }

            // Update the entire BrandPopUpAASATIBanners array
            brandPopUp.BrandValueImage = BrandValueImage;
            brandPopUp.BrandValueImageMobi = BrandValueImageMobi;

            await brandPopUp.save();
            return res.success({
                BrandValueImage: brandPopUp.BrandValueImage,
                BrandValueImageMobi: brandPopUp.BrandValueImageMobi,
            }, req.__('DATA_UPDATE_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding banners section:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async aasatiBrandViewValueImage(req, res) {
        try {
            const brandPopUp = await BrandPopUpAASATI.findOne({});

            if (!brandPopUp) {
                return res.warn({}, req.__('DATA_NOT_FOUND'));
            }

            return res.success({
                BrandValueImage: brandPopUp.BrandValueImage,
                BrandValueImageMobi: brandPopUp.BrandValueImageMobi,
            }, req.__('DATA_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching data:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    //BRAND AASATI INSPIRATION APIs
    async aasatiBrandAddInspiration(req, res) {
        try {
            const { InspirationName, InspiredImage, BuiltImage, isActive, sortOrder } = req.body;

            const brandPopUp = await BrandPopUpAASATI.findOne({});
            if (!brandPopUp) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const maxSortOrder =
                brandPopUp.BrandInspirationImage.length > 0
                    ? Math.max(...brandPopUp.BrandInspirationImage.map(item => item.sortOrder))
                    : 0;

            const newTile = {
                InspirationName,
                InspiredImage,
                BuiltImage,
                isActive,
                sortOrder: maxSortOrder + 1,
            };

            brandPopUp.BrandInspirationImage.push(newTile);
            await brandPopUp.save();

            return res.success(brandPopUp, req.__('INSPIRATION_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding inspiration:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async aasatiBrandEditInspiration(req, res) {
        try {
            const { id } = req.params;
            const { InspirationName, InspiredImage, BuiltImage, isActive, sortOrder } = req.body;

            const brandPopUp = await BrandPopUpAASATI.findOne({});
            if (!brandPopUp) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const tileIndex = brandPopUp.BrandInspirationImage.findIndex(tile => tile._id.toString() === id);
            if (tileIndex === -1) {
                return res.notFound({ success: false, message: 'Product tile not found' });
            }

            brandPopUp.BrandInspirationImage[tileIndex] = {
                ...brandPopUp.BrandInspirationImage[tileIndex],
                InspirationName,
                InspiredImage,
                BuiltImage,
                isActive,
                sortOrder,
            };

            await brandPopUp.save();

            return res.success(brandPopUp.BrandInspirationImage, req.__('INSPIRATION_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while editing inspiration:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // View all Brand Inspiration
    async aasatiBrandViewInspiration(req, res) {
        const { isActive = 'true' } = req.query; // Default to 'true' if not provided
        try {
            const brandPopUp = await BrandPopUpAASATI.findOne({}, 'BrandInspirationImage');

            if (!brandPopUp) {
                return res.warn({}, req.__('TILES_NOT_FOUND'));
            }
            console.log('brandPopUp.BrandInspirationImage>>>', brandPopUp);
            if (isActive !== 'all') {
                // Filter the banners according to the isActive condition
                const filteredTiles = brandPopUp.BrandInspirationImage.filter(
                    tile => tile.isActive === (isActive === 'true')
                );
                return res.success(filteredTiles, req.__('DATA_FETCHED_SUCCESSFULLY'));
            }

            return res.success(brandPopUp.BrandInspirationImage, req.__('DATA_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching product tiles:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete a Brand Inspiration
    async aasatiBrandDeleteInspiration(req, res) {
        try {
            const { id } = req.params;

            const brandPopUp = await BrandPopUpAASATI.findOne();
            if (!brandPopUp) {
                return res.warn({}, req.__('TILES_NOT_FOUND'));
            }

            const tileIndex = brandPopUp.BrandInspirationImage.findIndex(t => t._id.toString() === id);
            if (tileIndex > -1) {
                brandPopUp.BrandInspirationImage.splice(tileIndex, 1);
                await brandPopUp.save();
                return res.success({}, req.__('TILE_DELETED_SUCCESSFULLY'));
            } else {
                return res.warn({}, req.__('TILE_NOT_FOUND'));
            }
        } catch (error) {
            console.error('Error while deleting product tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    //BRAND AASATI PRODUCT TILES API
    async aasatiBrandAddProductTile(req, res) {
        try {
            const { TileImage, TileHoverImage, isActive, sortOrder,BrandUrl } = req.body;

            const brandPopUp = await BrandPopUpAASATI.findOne({});
            if (!brandPopUp) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const maxSortOrder =
                brandPopUp.BrandProductTilesAASATI.length > 0
                    ? Math.max(...brandPopUp.BrandProductTilesAASATI.map(item => item.sortOrder))
                    : 0;

            const newTile = {
                TileImage,
                TileHoverImage,
                isActive,
                sortOrder: maxSortOrder + 1,
                BrandUrl
            };

            brandPopUp.BrandProductTilesAASATI.push(newTile);
            await brandPopUp.save();

            return res.success(brandPopUp, req.__('PRODUCT_TILE_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while adding product tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async aasatiBrandEditProductTile(req, res) {
        try {
            const { id } = req.params;
            const { TileImage, TileHoverImage, isActive, sortOrder, BrandUrl } = req.body;

            const brandPopUp = await BrandPopUpAASATI.findOne({});
            if (!brandPopUp) {
                return res.notFound({ success: false, message: 'Document not found' });
            }

            const tileIndex = brandPopUp.BrandProductTilesAASATI.findIndex(tile => tile._id.toString() === id);
            if (tileIndex === -1) {
                return res.notFound({ success: false, message: 'Product tile not found' });
            }

            brandPopUp.BrandProductTilesAASATI[tileIndex] = {
                ...brandPopUp.BrandProductTilesAASATI[tileIndex],
                TileImage,
                TileHoverImage,
                isActive,
                sortOrder,
                BrandUrl
            };

            await brandPopUp.save();

            return res.success(brandPopUp.BrandProductTilesAASATI, req.__('PRODUCT_TILE_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while editing product tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // View all Brand Product Tiles
    async aasatiBrandViewProductTiles(req, res) {
        const { isActive = 'true' } = req.query; // Default to 'true' if not provided
        try {
            const brandPopUp = await BrandPopUpAASATI.findOne({}, 'BrandProductTilesAASATI');

            if (!brandPopUp) {
                return res.warn({}, req.__('TILES_NOT_FOUND'));
            }
            if (isActive !== 'all') {
                // Filter the banners according to the isActive condition
                const filteredTiles = brandPopUp.BrandProductTilesAASATI.filter(
                    tile => tile.isActive === (isActive === 'true')
                );
                return res.success(filteredTiles, req.__('DATA_FETCHED_SUCCESSFULLY'));
            }

            return res.success(brandPopUp.BrandProductTilesAASATI, req.__('DATA_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while fetching product tiles:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete a Brand Product Tile
    async aasatiBrandDeleteProductTile(req, res) {
        try {
            const { id } = req.params;

            const brandPopUp = await BrandPopUpAASATI.findOne();
            if (!brandPopUp) {
                return res.warn({}, req.__('TILES_NOT_FOUND'));
            }

            const tileIndex = brandPopUp.BrandProductTilesAASATI.findIndex(t => t._id.toString() === id);
            if (tileIndex > -1) {
                brandPopUp.BrandProductTilesAASATI.splice(tileIndex, 1);
                await brandPopUp.save();
                return res.success({}, req.__('TILE_DELETED_SUCCESSFULLY'));
            } else {
                return res.warn({}, req.__('TILE_NOT_FOUND'));
            }
        } catch (error) {
            console.error('Error while deleting product tile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /*************************** AASATI BRAND APIs End *********************************/
}
module.exports = new BrandController();
