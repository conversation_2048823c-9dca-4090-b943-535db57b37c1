const mongoose = require('mongoose');

const ItemConfigurationMasterSchema = new mongoose.Schema(
    {
        brand: { type: String, default: '' },
        series: { type: String, default: '' },
        category: { type: String, default: '' },
        itemCategory: { type: String, default: '' }, // "ITEM_CATEGORY (FAMILY-MODEL)"
        itemCategoryCode: { type: String, default: '' }, // "ITEM_CATEGORY_CODE (INA0081)"
        family: { type: String, default: '' },
        model: { type: String, default: '' },
        modelSKU: { type: String, default: '' },
        application: { type: [String], default: [] },
        modelSKUDescription: { type: String, default: '' },
        cutsheetTitle: { type: String, default: '' },
        websiteModelAccordionTitle: { type: String, default: '' },
        antiGlare: { type: [String], default: [] }, // "Anti Glare (ag)"
        beamAngle: { type: [String], default: [] }, // "Beam <PERSON>le (ba)"
        bend: { type: [String], default: [] }, // "Bend (bnd)"
        connectionCable: { type: [String], default: [] }, // "ConnectionCable (concab)"
        driver: { type: [String], default: [] }, // "Driver (drv)"
        finish: { type: [String], default: [] }, // "Finish (fin)"
        length: { type: [String], default: [] }, // "Length (len)"
        lens: { type: [String], default: [] }, // "Lens (lens)"
        lightColour: { type: [String], default: [] }, // "Light Colour (lc)"
        operatingVoltage: { type: [String], default: [] }, // "Operating Voltage (ov)"
        suspension: { type: [String], default: [] }, // "Suspension (sus)"
        trim: { type: [String], default: [] }, // "Trim (trm)"
        watts: { type: [String], default: [] }, // "Watts (watts)"
        // dealerCost: { type: Number, default: 0 }, // "Dealer Cost"
        // dealerMSRP: { type: Number, default: 0 }, // "Dealer MSRP"
        // repCost: { type: Number, default: 0 }, // "Rep Cost"
        // distributorCost: { type: Number, default: 0 }, // "Distributor Cost"
        // contractorCost: { type: Number, default: 0 }, // "Contractor Cost"
        // endUserCost: { type: Number, default: 0 }, // "End User Cost"
        cutPoints: { type: String, default: '' }, // "End User Cost"
        uom: { type: String, default: '' }, // "End User Cost"
        type: { type: [String], default: [] }, // "End User Cost"
        connection: { type: [String], default: [] }, // "End User Cost"
        acctyp: { type: [String], default: [] },
        cabType: { type: [String], default: [] },
        chan: { type: [String], default: [] },
        reflector: { type: [String], default: [] },
        collections: { type: [String], default: [] },
        material: { type: [String], default: [] },
        wattsPerUOM: { type: String, default: '' },
        lumensPerUOM: { type: String, default: '' },
        wattsPerFixtureRange: { type: String, default: '' },
        lumensPerFixtureRange: { type: String, default: '' },
        fileName: { type: String, default: '' },

        isActive: {
            type: Boolean,
            default: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    {
        timestamps: {
            createdAt: 'created',
            updatedAt: 'updated',
            toJSON: {
                getters: true,
            },
            toObject: {
                getters: true,
            },
        },
        toJSON: {
            getters: true,
        },
        toObject: {
            getters: true,
        },
    }
);
ItemConfigurationMasterSchema.index({ itemCategoryCode: 1 }, { unique: true });
module.exports = mongoose.model('ItemConfigurationMaster', ItemConfigurationMasterSchema);
