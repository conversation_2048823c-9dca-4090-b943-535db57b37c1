const mongoose = require('mongoose');
const {
    models: { Inspiration, InspirationLanding },
} = require('../../../../lib/models');

class InspirationController {
    // Get all inspirations
    async getAllInspirations(req, res) {
        try {
            const {type} = req.query
            const filter = {};
    if (type) {
        filter['landing.type'] = type; // Filter by type if provided
    }
    const inspirations = await Inspiration.find(filter).populate('landing.type');
            return res.success(inspirations, req.__('INSPIRATIONS_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error fetching inspirations:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Get a single inspiration by ID
    async getInspiration(req, res) {
        try {
            const inspiration = await Inspiration.findById(req.params.id);
            if (!inspiration) {
                return res.notFound({}, req.__('INSPIRATION_NOT_FOUND'));
            }
            return res.success(inspiration, req.__('INSPIRATION_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error fetching inspiration:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add or update an inspiration
    async addOrUpdateInspiration(req, res) {
        try {
            const {
                videoFileName,
                isActive,
                sortOrder,
                scenes,
                circadianLighting,
                colour,
                builtWithAASATTI,
                landing
                
            } = req.body;

            // If ID exists in params, perform update
            if (req.params.id) {
                const inspiration = await Inspiration.findOneAndUpdate(
                    { _id: req.params.id }, // Update by ID if exists
                    {
                        $set: {
                            videoFileName,
                            isActive,
                            sortOrder,
                            scenes,
                            circadianLighting,
                            colour,
                            builtWithAASATTI,
                            landing
                        },
                    },
                    { new: true, upsert: false } // No upsert when updating an existing one
                );

                if (!inspiration) {
                    return res.notFound({}, req.__('INSPIRATION_NOT_FOUND'));
                }

                return res.success(inspiration, req.__('INSPIRATION_UPDATED_SUCCESSFULLY'));
            }
            // If no ID in params, perform create
            else {
                const newInspiration = new Inspiration({
                    videoFileName,
                    isActive,
                    sortOrder,
                    scenes,
                    circadianLighting,
                    colour,
                    builtWithAASATTI,
                    landing
                });

                await newInspiration.save();

                return res.success(newInspiration, req.__('INSPIRATION_CREATED_SUCCESSFULLY'));
            }
        } catch (error) {
            console.error('Error adding/updating inspiration:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete an inspiration by ID
    async deleteInspiration(req, res) {
        try {
            const inspiration = await Inspiration.findByIdAndDelete(req.params.id);
            if (!inspiration) {
                return res.notFound({}, req.__('INSPIRATION_NOT_FOUND'));
            }
            return res.success({}, req.__('INSPIRATION_DELETED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error deleting inspiration:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add a scene to an inspiration
    async addScene(req, res) {
        try {
            const { mediaType, buttonLabel, fileName } = req.body;
            const inspiration = await Inspiration.findById(req.params.id);

            if (!inspiration) {
                return res.notFound({}, req.__('INSPIRATION_NOT_FOUND'));
            }

            const newScene = { mediaType, buttonLabel, fileName };
            inspiration.scenes.push(newScene);
            await inspiration.save();

            return res.success(inspiration, req.__('SCENE_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error adding scene:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Update a scene in an inspiration
    async updateScene(req, res) {
        try {
            const { sceneId } = req.params;
            const { mediaType, buttonLabel, fileName } = req.body;

            const inspiration = await Inspiration.findOneAndUpdate(
                { 'scenes._id': sceneId },
                {
                    $set: {
                        'scenes.$[elem].mediaType': mediaType,
                        'scenes.$[elem].buttonLabel': buttonLabel,
                        'scenes.$[elem].fileName': fileName,
                    },
                },
                {
                    arrayFilters: [{ 'elem._id': sceneId }],
                    new: true,
                }
            );

            if (!inspiration) {
                return res.warn({}, req.__('SCENE_NOT_FOUND'));
            }

            return res.success(inspiration, req.__('SCENE_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error updating scene:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete a scene from an inspiration
    async deleteScene(req, res) {
        try {
            const { sceneId } = req.params;

            const inspiration = await Inspiration.findByIdAndUpdate(
                req.params.id,
                { $pull: { scenes: { _id: sceneId } } },
                { new: true }
            );

            if (!inspiration) {
                return res.warn({}, req.__('SCENE_NOT_FOUND'));
            }

            return res.success(inspiration, req.__('SCENE_DELETED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error deleting scene:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add or update circadian lighting
    async addOrUpdateCircadianLighting(req, res) {
        try {
            const { images } = req.body;

            const inspiration = await Inspiration.findOneAndUpdate(
                { _id: req.params.id },
                { $set: { circadianLighting: { images } } },
                { new: true, upsert: true }
            );

            return res.success(inspiration, req.__('CIRCADIAN_LIGHTING_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error updating circadian lighting:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add or update colour blocks
    async addOrUpdateColour(req, res) {
        try {
            const { totalBlocks, blocks } = req.body;

            const inspiration = await Inspiration.findOneAndUpdate(
                { _id: req.params.id },
                { $set: { colour: { totalBlocks, blocks } } },
                { new: true, upsert: true }
            );

            return res.success(inspiration, req.__('COLOUR_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error updating colour blocks:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

     // Add or update inspiration banner section
     async addOrUpdatebanner(req, res) {
        try {
           
            const { id, mediaType,fileName,fileTitle, mediaTypeMobi,fileNameMobi,fileTitleMobi } = req.body;

            const updatedInspiration = await InspirationLanding.findOneAndUpdate(
                {},
                {
                    $set: {
                        mediaType,
                        fileName,
                        fileTitle,
                        mediaTypeMobi,
                        fileNameMobi,
                        fileTitleMobi
                    }
                },
                {
                    new: true, // Return the updated document
                    upsert: true, // Insert if not found
                    projection: {
                        mediaType: 1,
                        fileName: 1,
                        fileTitle: 1,
                        mediaTypeMobi: 1,
                        fileNameMobi: 1,
                        fileTitleMobi: 1
                    }
                }
            );

            return res.success(updatedInspiration, req.__('BANNER_UPDATE_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error updating banner:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
// Get inspiration landing banner section
    async getBanner(req, res) {
        try {
            const banner = await InspirationLanding.findOne({}, {
                mediaType: 1,
                fileName: 1,
                fileTitle: 1,
                mediaTypeMobi: 1,
                fileNameMobi: 1,
                fileTitleMobi: 1
            });
    
            if (!banner) {
                return res.notFound({}, req.__('BANNER_NOT_FOUND'));
            }
    
            return res.success(banner, req.__('BANNER_FETCH_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error fetching banner:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
}

module.exports = new InspirationController();