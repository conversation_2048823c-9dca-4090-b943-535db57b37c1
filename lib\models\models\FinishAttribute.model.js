const mongoose = require('mongoose');

// Beam <PERSON>le Attribute Schema
const FinishAttributeSchema = new mongoose.Schema({
    name: {
        type: String,
        default: '',
    },
    modelSlug: {
        type: String,
        default: 'fin',
    },
    isActive: {
        type: Boolean,
        default: true,
    },
    isDelete: {
        type: Boolean,
        default: false,
    },
});

module.exports = mongoose.model('FinishAttribute', FinishAttributeSchema);
