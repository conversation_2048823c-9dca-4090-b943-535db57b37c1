const mongoose = require('mongoose');

const mediaSchema = new mongoose.Schema({
    mediaType: {
        type: String,
        enum: ['image', 'video'],
        default: 'image',
    },
    mediaUrl: {
        type: String,
        default: '',
    },
    mediaTypeMobi: {
        type: String,
        enum: ['image', 'video'],
        default: 'image',
    },
    mediaUrlMobi: {
        type: String,
        default: '',
    },
    isDelete: {
        type: Boolean,
        default: false,
    },
    isActive: {
        type: Boolean,
        default: true,
    },
    sortOrder: {
        type: Number,
        default: 0,
    },
});

const downloadItemSchema = new mongoose.Schema({
    fileName: {
        type: String,
        default: '',
    },
    isAvailable: {
        type: Boolean,
        default: false,
    },
});

const downloadsSchema = new mongoose.Schema({
    specificationDownload: downloadItemSchema,
    installationDownload: downloadItemSchema,
    iesDownload: downloadItemSchema,
    cadDownload: downloadItemSchema,
    lookbookDownload: downloadItemSchema,
    catalogueDownload: downloadItemSchema,
    videoDownload: downloadItemSchema,
});

const productModelsSchema = new mongoose.Schema({
    title: {
        type: String,
        default: '',
    },
    
    description: {
        type: String,
        default: '',
    },
    modelSKU: {
        type: String,
        default: '',
    },
    productModelImages: [mediaSchema],
    downloads: downloadsSchema,
    itemCategory: { type: String, default: '' },
    isDeleted: {
        type: Boolean,
        default: false,
    },
    isActive: {
        type: Boolean,
        default: true,
    },
    sortOrder: {
        type: Number,
        default: 0,
    },
});

const bannerItemSchema = new mongoose.Schema({
    mediaType: {
        type: String,
        enum: ['image', 'video'],
        default: 'image',
    },
    mediaUrl: {
        type: String,
        default: '',
    },
});

const BannerSectionSchema = new mongoose.Schema({
    banner: [bannerItemSchema],
    title: {
        type: String,
        default: '',
    },
    isTitleVisible: { type: Boolean, default: true },
    familyName: {
        label: { type: String, default: 'Product Family' },
        isVisible: { type: Boolean, default: true },
    },
    modelsAndSpecs: {
        label: { type: String, default: 'Models & Specs' },
        isVisible: { type: Boolean, default: true },
    },
    downloads: {
        label: { type: String, default: 'Downloads' },
        isVisible: { type: Boolean, default: true },
    },
    itemConfigurator: {
        label: { type: String, default: 'Item Configurator' },
        isVisible: { type: Boolean, default: true },
    },
    viewCatalogue: {
        label: { type: String, default: 'View Catalogue' },
        isVisible: { type: Boolean, default: true },
    },
});

const ProductDetailFamilySchema = new mongoose.Schema({
    family: {
        id: { type: mongoose.Schema.Types.ObjectId, ref: 'FamilyFilter', default: null },
        name: { type: String, default: '' },
    },
    familyCode: {
        type: String,
        default: '',
        trim: true,
        uppercase: true
    },
    brand: {
        id: { type: mongoose.Schema.Types.ObjectId, ref: 'BrandFilter', default: null },
        name: { type: String, default: '' },
    },
    series: {
        id: { type: mongoose.Schema.Types.ObjectId, ref: 'SeriesFilter', default: null },
        name: { type: String, default: '' },
    },
    category: {
        id: { type: mongoose.Schema.Types.ObjectId, ref: 'Category', default: null },
        name: { type: String, default: '' },
    },
    collections: {
        id: { type: mongoose.Schema.Types.ObjectId, ref: 'CollectionFilter', default: null },
        name: { type: String, default: '' },
    },
    material: {
        id: { type: mongoose.Schema.Types.ObjectId, ref: 'MaterialFilter', default: null },
        name: { type: String, default: '' },
    },
    itemCategory: {
        type: String,
        default: '',
        trim: true
    },
    efficacyType: {
        type: String,
        default: null
    },
    resourceImage: bannerItemSchema,
    productTileImage: bannerItemSchema,
    productHoverImage: bannerItemSchema,
    categoryTileImage: bannerItemSchema,
    categoryHoverImage: bannerItemSchema,
    collectionImage: bannerItemSchema,
    collectionHoverImage: bannerItemSchema,
    finishTileImage: bannerItemSchema,
    finishHoverImage: bannerItemSchema,
    associatedProducts: {
        type: [String],
        default: [],
        validate: {
            validator: function(arr) {
                return arr.every(product => typeof product === 'string' && product.trim().length > 0);
            },
            message: 'All associated products must be non-empty strings'
        }
    },
    associatedProductsVisible: {
        type: Boolean,
        default: true
    },
});

const ProductSchema = new mongoose.Schema(
    {
        sortOrder: {
            type: String,
            default: '',
            trim: true
        },
        bannerSection: BannerSectionSchema,
        productDetailFamilyRecordInternal: ProductDetailFamilySchema,
        BoardContainer: [mediaSchema],
        productModels: [productModelsSchema],
        downloads: downloadsSchema,
        similarProducts: {
            type: [String],
            default: [],
            validate: {
                validator: function(arr) {
                    return arr.every(product => typeof product === 'string' && product.trim().length > 0);
                },
                message: 'All similar products must be non-empty strings'
            }
        },
        similarProductsVisible: {
            type: Boolean,
            default: false
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        sortOrder: {
            type: Number,
            default: 0,
        },
    },
    { timestamps: true }
);

module.exports = mongoose.model('Product', ProductSchema);
