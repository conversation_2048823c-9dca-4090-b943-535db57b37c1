const mongoose = require('mongoose'),
    Schema = mongoose.Schema;

const FooterSchema = new Schema(
    {
        FooterSocialMediaInstaURL: {
            type: String,
            default: '',
        },
        FooterSocialMediaXURL: {
            type: String,
            default: '',
        },
        FooterSocialMediaFBURL: {
            type: String,
            default: '',
        },
        FooterSocialMediaLinkedINURL: {
            type: String,
            default: '',
        },
        FooterPrivacyURL: {
            type: String,
            default: '',
        },
        FooterWarrantyURL: {
            type: String,
            default: '',
        },
        FooterTermsURL: {
            type: String,
            default: '',
        },
        FooterCopyrightURL: {
            type: String,
            default: '',
        },
    },
    {
        timestamps: {
            createdAt: 'created',
            updatedAt: 'updated',
        },
        id: false,
        toJSON: {
            getters: true,
        },
        toObject: {
            getters: true,
        },
    }
);


module.exports = mongoose.model('Footer', FooterSchema);
