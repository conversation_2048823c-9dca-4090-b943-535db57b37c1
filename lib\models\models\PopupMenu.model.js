const mongoose = require('mongoose'),
    Schema = mongoose.Schema;

const PopupMenuSchema = new Schema(
    {
        HeroPopUpMenuProductsImage: {
            type: String,
            default: '',
        },
        HeroPopUpMenuAasattiImage: {
            type: String,
            default: '',
        },
        HeroPopUpMenuInspirationImage: {
            type: String,
            default: '',
        },
        HeroPopUpMenuProjectsImage: {
            type: String,
            default: '',
        },
        HeroPopUpMenuResourcesImage: {
            type: String,
            default: '',
        },
        HeroPopUpMenuCareersImage: {
            type: String,
            default: '',
        },
        HeroPopUpMenuLegalImage: {
            type: String,
            default: '',
        },
        HeroPopUpMenuCompanyImage: {
            type: String,
            default: '',
        },
        HeroPopUpMenuContactImage: {
            type: String,
            default: '',
        },
    },
    {
        timestamps: {
            createdAt: 'created',
            updatedAt: 'updated',
        },
        id: false,
        toJSON: {
            getters: true,
        },
        toObject: {
            getters: true,
        },
    }
);


module.exports = mongoose.model('PopupMenu', PopupMenuSchema);
