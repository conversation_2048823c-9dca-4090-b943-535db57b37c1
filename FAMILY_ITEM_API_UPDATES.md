# Family Item Configuration API Updates

## Overview
Updated the `bulkFamilyItemConfigurationImport` API to handle the new JSON format and automatically manage Brand, Series, Category, and Family filter records.

## Changes Made

### 1. Updated JSON Input Format
The API now accepts data in this format:
```json
{
    "data": [
        {
            "productFamilyCode": "SGI-001-006",
            "productFamilyDescription": "SGI.  Specification Series.  FLEX.  CLASSIC",
            "brand": "SGI",
            "series": "SPECIFICATION", 
            "category": "FLEX",
            "family": "CLASSIC",
            "associatedProducts": ["GEN-003-041", "GEN-003-042"],
            "similarProducts": ["GEN-001-030", "SGI-001-024"]
        }
    ],
    "filename": "revXXXXXXX"
}
```

### 2. Automatic Filter Record Management
- **Finds existing records** in BrandFilter, SeriesFilter, Category, and FamilyFilter collections
- **Creates missing records** automatically if they don't exist
- **Maps IDs and names** according to FamilyItemConfigurationSchema requirements

### 3. Performance Optimizations
- **Pre-fetching**: Loads all existing filter records once at the start
- **Batch processing**: Processes data in batches of 500 records
- **Bulk operations**: Uses MongoDB bulk write operations
- **Memory efficient**: Processes large datasets without memory issues

### 4. Schema Compliance
The processed data follows the FamilyItemConfigurationSchema structure:
```javascript
{
    productFamilyCode: String,
    productFamilyDescription: String,
    brand: {
        id: ObjectId (ref: 'BrandFilter'),
        name: String
    },
    series: {
        id: ObjectId (ref: 'SeriesFilter'), 
        name: String
    },
    category: {
        id: ObjectId (ref: 'Category'),
        name: String
    },
    family: {
        id: ObjectId (ref: 'FamilyFilter'),
        name: String
    },
    associatedProducts: [String],
    similarProducts: [String],
    fileName: String
}
```

## API Endpoints

### Import Family Items
**POST** `/api/products/bulk-family-item-configuration`

**Request Body:**
```json
{
    "data": [...], // Array of family item objects
    "filename": "revXXXXXXX" // Optional filename
}
```

**Response:**
```json
{
    "success": true,
    "message": "Family items imported successfully",
    "data": {
        "processedCount": 1000,
        "filename": "revXXXXXXX"
    }
}
```

### List Family Items  
**GET** `/api/products/bulk-item-family-configuration`

**Query Parameters:**
- `search`: Search term
- `brand`: Filter by brand name
- `series`: Filter by series name  
- `category`: Filter by category name
- `family`: Filter by family name
- `page`: Page number
- `limit`: Items per page
- `getAllRecords`: Get all records (true/false)

## Key Benefits

### 1. Scalability
- Handles thousands of records efficiently
- Batch processing prevents memory overflow
- Optimized database queries

### 2. Data Integrity
- Automatic creation of missing filter records
- Proper ID referencing maintains relationships
- Validation ensures data consistency

### 3. Performance
- Single database query to fetch existing filters
- Bulk write operations for fast inserts/updates
- Minimal database round trips

### 4. Maintainability
- Clean separation of concerns
- Reusable helper methods
- Clear error handling

## Usage Example

```javascript
const familyData = {
    "data": [
        {
            "productFamilyCode": "SGI-001-006",
            "productFamilyDescription": "SGI.  Specification Series.  FLEX.  CLASSIC",
            "brand": "SGI",
            "series": "SPECIFICATION",
            "category": "FLEX", 
            "family": "CLASSIC",
            "associatedProducts": ["GEN-003-041"],
            "similarProducts": ["GEN-001-030"]
        }
    ],
    "filename": "import_batch_001"
};

// Import the data
const response = await fetch('/api/products/bulk-family-item-configuration', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(familyData)
});
```

## Testing
Use the provided `test_family_item_api.js` file to test the implementation:

1. Update the API URL and authentication token
2. Run: `node test_family_item_api.js`
3. Verify the import and list functionality

## Notes
- The API maintains backward compatibility
- All filter records are created with default active status
- The filename parameter is optional but recommended for tracking
- Error handling provides detailed feedback for debugging
