const mongoose = require('mongoose');

// Schema for Watts Output
const outputWattsSchema = new mongoose.Schema({
  wattsPerFoot: {
    type: String,
    enum: ['Less than 2 W/FT', '2.1 W/FT to 4.0 W/FT', '4.1 W/FT to 6.0 W/FT', '6.1 W/FT to 10.0 W/FT', 'Greater than 10 W/FT']
  },
  wattsPerFixture: {
    type: String,
    enum: ['Less than 2 Watts', '2.1 Watts to 6 Watts', '6.1 Watts to 10 Watts', '10.1 Watts to 20 Watts', '20.1 Watts to 30 Watts', '30.1 Watts to 50 Watts', 'Greater than 50 Watts']
  }
});

// Schema for Lumens Output
const outputLumensSchema = new mongoose.Schema({
  lumensPerFoot: {
    type: String,
    enum: ['Less than 150 Lm/FT', '151 Lm/FT to 300 Lm/FT', '301 Lm/FT to 600 Lm/FT', '601 Lm/FT to 1000 Lm/FT', 'Greater than 1000 Lm/FT']
  },
  lumensPerFixture: {
    type: String,
    enum: ['Less than 150 Lm/FT', '151 Lm/FT to 300 Lm/FT', '301 Lm/FT to 600 Lm/FT', '601 Lm/FT to 1000 Lm/FT', 'Greater than 1000 Lm/FT']
  }
});

// Main Product Filter Schema
const productFilterSchema = new mongoose.Schema({
  brand: {
    type: String,
    enum: ['SGi', 'efficacy', 'GENIE', 'AASATTI'],
    required: true
  },
  series: {
    type: String,
    enum: ['SPECIFICATION', 'DECORATIVE', 'VALUE', 'INTERNATIONAL'],
    required: true
  },
  application: {
    type: String,
    enum: ['ACCENT', 'COVE', 'DECORATIVE', 'FAÇADE', 'GENERAL', 'HANDRAIL', 'INGROUND', 'MIRROR', 'PATHWAY', 'SAUNA-STEAM', 'SOFFIT', 'STEP', 'TASK', 'UNDERWATER', 'WALL GRAZING', 'WALL WASHING', 'WELLNESS'],
    required: true
  },
  category: {
    type: String,
    enum: ['ACCENT', 'BAR', 'DECORATIVE', 'DOWNLIGHT', 'FLEX', 'INGROUND', 'LINEAR', 'MAGNETIC', 'NEON', 'POWER-CONTROL', 'SPOT', 'TRACK', 'WASHER'],
    required: true
  },
  family: {
    type: String,
    enum: [
      '360', 'ACCENT', 'ACOUSTIC', 'BAR', 'BRICK', 'COLOUR LINE', 'DECORATIVE-ALL', 'DOWNLIGHT', 'FLEX', 'FLEX GRAZER', 
      'GENIE ACCENT', 'GENIE FLEX', 'GENIUS', 'LINE', 'MAGNETIC', 'NEON', 'RAIL', 'SPOT', 'WASHER',
      'COLLECTIONS-ACOUSTIC', 'GENIE_NEON', 'GENIE_PROFILE', 'CONTROL4', 'BLE', 'ZIGBEE', 'WIFI', 'CASAMBI', 'DIMMABLE 0-10V'
      // Add the combinations of family per brand
    ],
    required: true
  },
  lightColor: {
    type: String,
    enum: [
      '2300K', '2700K', '3000K', '3500K', '4000K', '5000K', '6500K', 'CSTCCT', 'RED', 'GREEN', 'BLUE', 'AMBER', 'TUNABLE WHITE', 
      'DIM2WARM', 'DMXPIXEL RGBW2700K', 'DMXPIXEL RGBW3000K', 'DMXPIXEL RGBW4000K', 'DMXPIXEL RGBW5000K', 'RGB', 'RGBW2700K', 
      'RGBW3000K', 'RGBW4000K', 'RGBTW', 'RGBFS'
    ],
    required: true
  },
  outputWatts: outputWattsSchema,
  outputLumens: outputLumensSchema
});

module.exports = mongoose.model('ProductFilter', productFilterSchema);
