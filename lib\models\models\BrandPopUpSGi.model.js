const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Schema for carousel items in BrandPopUpSGiBanners

const CarouselImageSchema = new Schema({
  banner: {
      type: String,
      required: true,
  },
  mediaType: {
      type: String,
      enum: ['image', 'video'],
      required: true,
  },
  bannerMobi: {
      type: String,
      
  },
  mediaTypeMobi: {
      type: String,
      enum: ['image', 'video'],
     
  },
  textOne: String,
  textTwo: String,
  link: String,
  isTextVisible: {
      type: Boolean,
      default: false,
  },
  isActive: { type: Boolean, default: true },
  sortOrder: { type: Number, required: true }
});

// Schema for BrandProductTilesSGi
const BrandProductTileSchema = new Schema({
  TileImage: { type: String, default:"" },
  TileHoverImage: { type: String, default:"" },
  isActive: { type: Boolean, default: true },
  sortOrder: { type: Number, required: true },
  BrandUrl: { type: String, default: '' },
});

// Main SGibrandPopUp Schema
const BrandPopUpSGiSchema = new Schema({
  
  BrandPopUpSGiBanners: [CarouselImageSchema],
  BrandValueSGi: { type: String, default:"" }, 
  BrandValueSGiMobi: { type: String, default:"" }, 
  BrandProductTilesSGi: [BrandProductTileSchema],
  
});

module.exports = mongoose.model('BrandPopUpSGi', BrandPopUpSGiSchema);
