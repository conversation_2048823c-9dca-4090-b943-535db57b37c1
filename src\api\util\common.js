const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');
const ObjectId = mongoose.Types.ObjectId;

const getPlatform = req => req.headers['X-sgi-platform'];

const getLanguage = req => req.headers['Accept-Language'];

const generateToken = payload => jwt.sign(payload, process.env.JWT_SECRET);

const getObjectId = id => ObjectId(id);

module.exports = {
    getPlatform,
    getLanguage,
    generateToken,
    getObjectId,
};
