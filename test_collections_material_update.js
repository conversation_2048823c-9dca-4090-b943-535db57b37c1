/**
 * Test script for Collections and Material fields in Family Item Configuration API
 */

const axios = require('axios');

// Test data with collections and material fields
const testDataWithCollectionsMaterial = {
    "data": [
        {
            "productFamilyCode": "TEST-COL-001",
            "productFamilyDescription": "Test Product with Collections and Material",
            "brand": "SGI",
            "series": "SPECIFICATION",
            "category": "FLEX",
            "family": "CLASSIC",
            "collections": "PREMIUM",
            "material": "ALUMINUM",
            "associatedProducts": ["TEST-001", "TEST-002"],
            "similarProducts": ["TEST-003", "TEST-004"]
        },
        {
            "productFamilyCode": "TEST-COL-002",
            "productFamilyDescription": "Test Product without Collections and Material",
            "brand": "GENIE",
            "series": "DECORATIVE",
            "category": "LINEAR",
            "family": "MODERN",
            "associatedProducts": ["TEST-005"],
            "similarProducts": ["TEST-006"]
        },
        {
            "productFamilyCode": "TEST-COL-003",
            "productFamilyDescription": "Test Product with only Collections",
            "brand": "SGI",
            "series": "SPECIFICATION",
            "category": "FLEX",
            "family": "GRAZER",
            "collections": "STANDARD",
            "associatedProducts": ["TEST-007"],
            "similarProducts": ["TEST-008"]
        },
        {
            "productFamilyCode": "TEST-COL-004",
            "productFamilyDescription": "Test Product with only Material",
            "brand": "GENIE",
            "series": "DECORATIVE",
            "category": "LINEAR",
            "family": "LINE",
            "material": "STEEL",
            "associatedProducts": ["TEST-009"],
            "similarProducts": ["TEST-010"]
        }
    ],
    "filename": "test_collections_material"
};

async function testCollectionsMaterialAPI() {
    try {
        console.log('🧪 Testing Collections and Material Fields...');
        
        // Replace with your actual API endpoint
        const API_BASE_URL = 'http://localhost:3000/api'; // Adjust port as needed
        
        console.log('📤 Testing bulk import with collections and material...');
        console.log('Data structure:');
        testDataWithCollectionsMaterial.data.forEach((item, index) => {
            console.log(`  ${index + 1}. ${item.productFamilyCode}:`);
            console.log(`     - Collections: ${item.collections || 'Not provided'}`);
            console.log(`     - Material: ${item.material || 'Not provided'}`);
        });
        
        const importResponse = await axios.post(
            `${API_BASE_URL}/products/bulk-family-item-configuration`,
            testDataWithCollectionsMaterial,
            {
                headers: {
                    'Content-Type': 'application/json',
                    // Add authentication header if needed
                    // 'Authorization': 'Bearer YOUR_TOKEN'
                },
                timeout: 30000
            }
        );

        console.log('\n✅ Import Response:');
        console.log(JSON.stringify(importResponse.data, null, 2));
        
        // Test listing with collections filter
        console.log('\n📋 Testing list with collections filter...');
        const collectionsFilterResponse = await axios.get(
            `${API_BASE_URL}/products/bulk-item-family-configuration?collections=PREMIUM,STANDARD&limit=10`,
            {
                headers: { 'Content-Type': 'application/json' },
                timeout: 30000
            }
        );
        
        console.log('✅ Collections Filter Response:');
        console.log(`Found ${collectionsFilterResponse.data.data?.count || 0} records with PREMIUM or STANDARD collections`);
        
        // Test listing with material filter
        console.log('\n🔧 Testing list with material filter...');
        const materialFilterResponse = await axios.get(
            `${API_BASE_URL}/products/bulk-item-family-configuration?material=ALUMINUM,STEEL&limit=10`,
            {
                headers: { 'Content-Type': 'application/json' },
                timeout: 30000
            }
        );
        
        console.log('✅ Material Filter Response:');
        console.log(`Found ${materialFilterResponse.data.data?.count || 0} records with ALUMINUM or STEEL material`);
        
        // Test combined filters
        console.log('\n🔍 Testing combined filters...');
        const combinedFilterResponse = await axios.get(
            `${API_BASE_URL}/products/bulk-item-family-configuration?brand=SGI&collections=PREMIUM&material=ALUMINUM&limit=5`,
            {
                headers: { 'Content-Type': 'application/json' },
                timeout: 30000
            }
        );
        
        console.log('✅ Combined Filter Response:');
        console.log(`Found ${combinedFilterResponse.data.data?.count || 0} SGI records with PREMIUM collections and ALUMINUM material`);
        
        // Show sample record structure
        if (combinedFilterResponse.data.data?.items?.length > 0) {
            console.log('\n📄 Sample record structure:');
            const sampleRecord = combinedFilterResponse.data.data.items[0];
            console.log('Collections field:', JSON.stringify(sampleRecord.collections, null, 2));
            console.log('Material field:', JSON.stringify(sampleRecord.material, null, 2));
        }
        
        console.log('\n🎉 All Collections and Material tests passed!');
        
        console.log('\n📊 Test Summary:');
        console.log('✅ Import with optional collections and material fields');
        console.log('✅ Filter by collections parameter');
        console.log('✅ Filter by material parameter');
        console.log('✅ Combined filtering with multiple parameters');
        console.log('✅ Proper handling of records without collections/material');
        
    } catch (error) {
        console.error('❌ Test failed:');
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', error.response.data);
        } else if (error.request) {
            console.error('No response received:', error.message);
        } else {
            console.error('Error:', error.message);
        }
    }
}

// Run the test
testCollectionsMaterialAPI();

console.log(`
📋 Collections and Material Test Script

This script tests the new optional fields in Family Item Configuration API:

🆕 New Features:
1. Collections field (optional) - references CollectionFilter
2. Material field (optional) - references MaterialFilter
3. Automatic creation of missing Collections/Material records
4. Filtering by collections and material parameters
5. Proper handling when fields are not provided

📝 Test Cases:
1. Record with both collections and material
2. Record with neither collections nor material
3. Record with only collections
4. Record with only material
5. Filtering by collections parameter
6. Filtering by material parameter
7. Combined filtering with multiple parameters

🔧 API Endpoints Tested:
- POST /products/bulk-family-item-configuration (with new fields)
- GET /products/bulk-item-family-configuration (with new filter parameters)

Make sure:
1. Your API server is running
2. Update API_BASE_URL if needed
3. Add authentication headers if required
4. CollectionFilter and MaterialFilter models exist
`);
