const mongoose = require('mongoose'),
    Schema = mongoose.Schema;

const ContactSchema = new Schema({
    ContactBannerType: {
        type: String,
        default:"image",
        enum: ['image', 'video'],
    },
    ContactBannerTitle: {
        type: String,
        trim: true,
    },
    ContactBannerFileName: {
        type: String,
        default:""
    },
    ContactCaption: {
        type: String,
         default:""
    },
    ContactAddressLabel: {
        type: String,
        default:""
    },
    ContactAddress: {
        type: String,
       default:""
    },
    ContactServiceLabel: {
        type: String,
       default:""
    },
    ContactServiceHours: {
        type: String,
       default:""
    },
    isDeleted: {
        type: Boolean,
        default: false,
    },
}, {
    timestamps: {
        createdAt: 'created',
        updatedAt: 'updated',
    },
    id: false,
    toJSON: {
        getters: true,
    },
    toObject: {
        getters: true,
    },
});

module.exports = mongoose.model('Contact', ContactSchema);