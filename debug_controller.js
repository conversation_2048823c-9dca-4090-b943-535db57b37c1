/**
 * Debug script to test ProductController method binding
 */

const ProductControllerClass = require('./src/api/routes/products/ProductController');

console.log('🔍 Debugging ProductController...');

// Test 1: Check if class can be instantiated
try {
    const controller = new ProductControllerClass();
    console.log('✅ ProductController instantiated successfully');
    
    // Test 2: Check if method exists
    if (typeof controller.processAndCreateFilterRecords === 'function') {
        console.log('✅ processAndCreateFilterRecords method exists');
        console.log('📋 Method signature:', controller.processAndCreateFilterRecords.toString().substring(0, 100) + '...');
    } else {
        console.log('❌ processAndCreateFilterRecords method NOT found');
        console.log('📋 Available methods:');
        Object.getOwnPropertyNames(Object.getPrototypeOf(controller))
            .filter(name => typeof controller[name] === 'function' && name !== 'constructor')
            .forEach(method => console.log(`   - ${method}`));
    }
    
    // Test 3: Check if bulkFamilyItemConfigurationImport exists
    if (typeof controller.bulkFamilyItemConfigurationImport === 'function') {
        console.log('✅ bulkFamilyItemConfigurationImport method exists');
    } else {
        console.log('❌ bulkFamilyItemConfigurationImport method NOT found');
    }
    
    // Test 4: Check method binding
    const testMethod = controller.processAndCreateFilterRecords;
    if (testMethod) {
        console.log('✅ Method can be accessed');
        console.log('🔗 Method bound to:', testMethod.name);
    }
    
} catch (error) {
    console.error('❌ Error instantiating ProductController:', error.message);
}

console.log('\n🧪 Testing method call simulation...');

// Test 5: Simulate the problematic call
try {
    const controller = new ProductControllerClass();
    
    // Create mock data
    const mockData = [
        {
            productFamilyCode: "TEST-001",
            brand: "SGI",
            series: "SPECIFICATION",
            category: "FLEX",
            family: "CLASSIC"
        }
    ];
    
    const mockBrandMap = new Map();
    const mockSeriesMap = new Map();
    const mockCategoryMap = new Map();
    const mockFamilyMap = new Map();
    const mockFilename = "test";
    
    // Check if we can call the method (without actually executing it)
    if (typeof controller.processAndCreateFilterRecords === 'function') {
        console.log('✅ Method call would work - function exists and is callable');
        
        // Test the actual method binding by checking 'this' context
        const boundMethod = controller.processAndCreateFilterRecords.bind(controller);
        console.log('✅ Method can be bound correctly');
        
    } else {
        console.log('❌ Method call would fail - function does not exist');
    }
    
} catch (error) {
    console.error('❌ Error in method call simulation:', error.message);
}

console.log('\n📊 Summary:');
console.log('- If all tests pass, the method should work correctly');
console.log('- If any test fails, that indicates the source of the problem');
console.log('- Run this script with: node debug_controller.js');
