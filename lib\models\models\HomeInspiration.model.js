const mongoose = require('mongoose'),
    Schema = mongoose.Schema,
    ObjectId = mongoose.Types.ObjectId;

const HomeInspirationCarouselSchema = new Schema({
    InspirationTitle: {
        type: String,
        default: '',
    },
    Scene1Image: {
        type: String,
        default: '',
    },
    Scene2Image: {
        type: String,
        default: '',
    },
    Scene3Image: {
        type: String,
        default: '',
    },
    Scene4Image: {
        type: String,
        default: '',
    },
    Scene5Image: {
        type: String,
        default: '',
    },
    
    isActive: {
        type: Boolean,
        default: false,
    },
    sortOrder: {
        type: Number,
        default: 0,
    },
    isDeleted: {
        type: Boolean,
        default: false,
    },
});

const HomeInspirationSchema = new Schema(
    {
        InspirationMainTitle: {
            type: String,
            default: '',
        },
        InspriationMainCopy: {
            type: String,
            default: '',
        },
        carousel: [HomeInspirationCarouselSchema],
    },
    {
        timestamps: {
            createdAt: 'created',
            updatedAt: 'updated',
        },
        id: false,
        toJSON: {
            getters: true,
        },
        toObject: {
            getters: true,
        },
    }
);

// Pre-save middleware to auto-increment sortOrder for new carousel items
HomeInspirationSchema.pre('save', function(next) {
    const doc = this;

    if (doc.isNew) {
        if (doc.carousel && doc.carousel.length > 0) {
            doc.carousel.forEach((carouselItem, index) => {
                if (carouselItem.isNew) {
                    const maxSortOrder = Math.max(...doc.carousel.map(item => item.sortOrder), 0);
                    carouselItem.sortOrder = maxSortOrder + 1;
                }
            });
        }
    }
    next();
});

module.exports = mongoose.model('HomeInspiration', HomeInspirationSchema);
