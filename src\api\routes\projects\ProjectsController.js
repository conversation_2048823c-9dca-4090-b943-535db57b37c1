const {
    models: { Project, ProjectLanding },
} = require('../../../../lib/models');

class ProjectsController {
    async listProjects(req, res) {
        const { 
            id,
            search, 
            isActive, 
            isFeatured, 
            minSortOrder, 
            maxSortOrder, 
            page = 1, 
            limit = 10, 
            FilterProjectBrand, 
            FilterProjectType, 
            FilterProjectApplication, 
            FilterProjectSeries, 
            FilterProjectProductFamily 
        } = req.query;
    
        const finalResponse = {
            items: [],
            count: 0,
            page,
            limit,
        };
    
        // Convert page and limit to numbers
        const pageNumber = parseInt(page);
        const limitNumber = parseInt(limit);
    
        // Calculate the number of items to skip
        let skip = (pageNumber - 1) * limitNumber;
    
        try {
            let qry = { isDeleted: false }; // Default filter for non-deleted projects
    
            // Add search filter for project title or description
            if (search) {
                qry.$or = [
                    { ProjectTitle: { $regex: new RegExp(search, 'i') } },
                    { 'ProjectTabs.description': { $regex: new RegExp(search, 'i') } },
                ];
            }
    
            // Add filter for isActive flag
            if (isActive !== undefined) {
                qry.isActive = isActive === 'true'; // Convert string 'true'/'false' to boolean
            }
    
            // Add filter for isFeatured flag
            if (isFeatured !== undefined) {
                qry.isFeatured = isFeatured === 'true';
            }
    
            // Apply sortOrder range query if provided
            if (minSortOrder && maxSortOrder) {
                qry.sortOrder = { $gte: parseInt(minSortOrder), $lte: parseInt(maxSortOrder) };
            } else if (minSortOrder) {
                qry.sortOrder = { $gte: parseInt(minSortOrder) };
            } else if (maxSortOrder) {
                qry.sortOrder = { $lte: parseInt(maxSortOrder) };
            }

            if (id) {
                qry._id = { $in: id.split(',') }
            }
    
            // Filter by brand, type, application, series, and product family filters
            const filterConditions = [];
            
            if (FilterProjectBrand) {
                filterConditions.push({ FilterProjectBrand: { $in: FilterProjectBrand.split(',') } });
            }
            if (FilterProjectType) {
                filterConditions.push({ FilterProjectType: { $in: FilterProjectType.split(',') } });
            }
            if (FilterProjectApplication) {
                filterConditions.push({ FilterProjectApplication: { $in: FilterProjectApplication.split(',') } });
            }
            if (FilterProjectSeries) {
                filterConditions.push({ FilterProjectSeries: { $in: FilterProjectSeries.split(',') } });
            }
            if (FilterProjectProductFamily) {
                filterConditions.push({ FilterProjectProductFamily: { $in: FilterProjectProductFamily.split(',') } });
            }
    
            if (filterConditions.length > 0) {
                qry.$and = filterConditions;
            }
    
            // Get the count of projects that match the filters
            const projectCount = await Project.countDocuments(qry);
    
            // Fetch the projects based on the filters, sorting by created date, and paginating results
            const projects = await Project.find(qry)
                .sort({ created: -1 })
                .skip(skip)
                .limit(limitNumber)
                .exec();
    
            // If no projects are found, return a warning
            if (projects.length === 0) {
                return res.warn({}, req.__('NO_PROJECTS_FOUND'));
            }
    
            // Set the results in the finalResponse object
            finalResponse.items = projects;
            finalResponse.count = projectCount;
    
            // Respond with the projects and the total count
            return res.success(finalResponse, req.__('PROJECTS_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            // Log the error and return a server error response
            console.error('Error fetching projects:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    
    async createProject(req, res) {
        const {
            featuredImage,
            isFeatured,
            projectDetailBanner,
            FilterProjectBrand,
            FilterProjectType,
            FilterProjectApplication,
            FilterProjectSeries,
            FilterProjectProductFamily,
            ProjectTitle,
            ProjectDescriptionShort,
            ProjectDescriptionDetail,
            SocialMediaLinks,
            ProjectPictures,
            ProjectTabs,
            similarProjects,
            isActive,
        } = req.body;
    
        try {
            // Ensure projectDetailBanner is properly formatted
            const formattedProjectDetailBanner = projectDetailBanner || {
                mediaType: 'image',  // default value
                fileName: '',
                fileTitle: ''
            };
    
            // Creating a new project object
            const newProject = new Project({
                featuredImage,
                isFeatured,
                projectDetailBanner: formattedProjectDetailBanner,
                FilterProjectBrand: FilterProjectBrand || [],
                FilterProjectType: FilterProjectType || [],
                FilterProjectApplication: FilterProjectApplication || [],
                FilterProjectSeries: FilterProjectSeries || [],
                FilterProjectProductFamily: FilterProjectProductFamily || [],
                ProjectTitle,
                ProjectDescriptionShort,
                ProjectDescriptionDetail,
                SocialMediaLinks: SocialMediaLinks || {
                    instagram: '',
                    x: '',
                    facebook: '',
                    linkedin: ''
                },
                ProjectPictures: ProjectPictures || [],
                ProjectTabs: ProjectTabs || [],
                similarProjects: similarProjects || [],
                isActive: isActive || false,
            });
    
            // Saving the new project to the database
            const savedProject = await newProject.save();
    
            return res.success(savedProject, req.__('PROJECT_CREATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error creating project:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    
    // Controller method to edit a project
    async editProject(req, res) {
        const { id } = req.params;
        const projectData = req.body; // The data to update the project with

        try {
            // Check if the project exists
            const project = await Project.findOne({ _id: id, isDeleted: false });

            if (!project) {
                return res.warn('Project not found', req.__('PROJECT_NOT_FOUND'));
            }

            // Update the project with the new data
            const updatedProject = await Project.findByIdAndUpdate(id, projectData, {
                new: true, // Return the updated document
                runValidators: true, // Ensure the data meets schema validation
            });

            if (!updatedProject) {
                return res.warn('Project could not be updated', req.__('PROJECT_UPDATE_FAILED'));
            }

            // Return success response
            res.success({ item: updatedProject }, req.__('PROJECT_UPDATED_SUCCESS'));
        } catch (error) {
            console.error('Error updating project:', error);
            res.warn(error, req.__('PROJECT_UPDATE_ERROR'));
        }
    }

    // Controller method to delete a project
    async deleteProject(req, res) {
        const { id } = req.params; // Get project ID from request parameters

        try {
            // Find the project by ID and ensure it's not already marked as deleted
            const project = await Project.findOne({ _id: id, isDeleted: false });

            if (!project) {
                return res.warn('Project not found', req.__('PROJECT_NOT_FOUND'));
            }

            // Mark the project as deleted
            project.isDeleted = true;
            await project.save();

            // Return success response
            res.success({ message: req.__('PROJECT_DELETED_SUCCESS') });
        } catch (error) {
            console.error('Error deleting project:', error);
            res.warn(error, req.__('PROJECT_DELETE_ERROR'));
        }
    }

     // Add or update inspiration banner section
     async addOrUpdatebanner(req, res) {
        try {
            const { id, mediaType, fileName, fileTitle, mediaTypeMobi, fileNameMobi, fileTitleMobi } = req.body;

            const updatedInspiration = await ProjectLanding.findOneAndUpdate(
                {},
                {
                    $set: {
                        mediaType,
                        fileName,
                        fileTitle,
                        mediaTypeMobi,
                        fileNameMobi,
                        fileTitleMobi,
                    },
                },
                {
                    new: true, // Return the updated document
                    upsert: true, // Insert if not found
                    projection: {
                        mediaType: 1,
                        fileName: 1,
                        fileTitle: 1,
                        mediaTypeMobi: 1,
                        fileNameMobi: 1,
                        fileTitleMobi: 1,
                    },
                }
            );

            return res.success(updatedInspiration, req.__('BANNER_UPDATE_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error updating banner:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    // Get inspiration landing banner section
    async getBanner(req, res) {
        try {
            const banner = await ProjectLanding.findOne(
                {},
                {
                    mediaType: 1,
                    fileName: 1,
                    fileTitle: 1,
                    mediaTypeMobi: 1,
                    fileNameMobi: 1,
                    fileTitleMobi: 1,
                }
            );

            if (!banner) {
                return res.notFound({}, req.__('BANNER_NOT_FOUND'));
            }

            return res.success(banner, req.__('BANNER_FETCH_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error fetching banner:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
}
module.exports = new ProjectsController();
