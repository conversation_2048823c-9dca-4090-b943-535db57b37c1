const mongoose = require('mongoose');

// Beam Angle Attribute Schema
const BeamAngleAttributeSchema = new mongoose.Schema({
    name: {
        type: String,
        default: '',
    },
    modelSlug: {
        type: String,
        default: 'ba',
    },
    isActive: {
        type: Boolean,
        default: true,
    },
    isDelete: {
        type: Boolean,
        default: false,
    },
});

module.exports = mongoose.model('BeamAngleAttribute', BeamAngleAttributeSchema);
