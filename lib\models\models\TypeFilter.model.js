const mongoose = require('mongoose');

// Main Product Filter Schema
const TypeFilterSchema = new mongoose.Schema({
    name: {
        type: String,
        default: '',
    },
    displayName: {
        type: String,
        default: '',
    },
    isActive: {
        type: Boolean,
        default: true,
    },
    isDelete: {
        type: Boolean,
        default: false,
    },
    sortOrder: {
        type: Number,
        default: 0,
    },
     brands: [
            {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'BrandFilter'
            }
        ],
});

module.exports = mongoose.model('TypeFilter', TypeFilterSchema);
