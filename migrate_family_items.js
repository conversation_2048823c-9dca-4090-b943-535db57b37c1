/**
 * Migration script for FamilyItemConfigurationMaster
 * This script helps normalize existing data and create indexes
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import the updated model
const FamilyItemConfigurationMaster = require('./lib/models/models/FamilyItemConfigurationMaster');

async function migrateFamilyItems() {
    try {
        console.log('🚀 Starting FamilyItemConfiguration migration...');
        
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/your-database', {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });
        console.log('✅ Connected to MongoDB');

        // Get all existing records
        const existingRecords = await FamilyItemConfigurationMaster.find({}).lean();
        console.log(`📊 Found ${existingRecords.length} existing records`);

        if (existingRecords.length === 0) {
            console.log('ℹ️  No existing records to migrate');
            return;
        }

        // Normalize existing data
        let updatedCount = 0;
        const batchSize = 100;
        
        for (let i = 0; i < existingRecords.length; i += batchSize) {
            const batch = existingRecords.slice(i, i + batchSize);
            const bulkOps = [];

            for (const record of batch) {
                const updateData = {};
                let needsUpdate = false;

                // Normalize productFamilyCode
                if (record.productFamilyCode) {
                    const normalizedCode = record.productFamilyCode.trim().toUpperCase();
                    if (normalizedCode !== record.productFamilyCode) {
                        updateData.productFamilyCode = normalizedCode;
                        needsUpdate = true;
                    }
                }

                // Normalize productFamilyDescription
                if (record.productFamilyDescription) {
                    const normalizedDesc = record.productFamilyDescription.trim();
                    if (normalizedDesc !== record.productFamilyDescription) {
                        updateData.productFamilyDescription = normalizedDesc;
                        needsUpdate = true;
                    }
                }

                // Normalize brand name
                if (record.brand?.name) {
                    const normalizedBrand = record.brand.name.trim().toUpperCase();
                    if (normalizedBrand !== record.brand.name) {
                        updateData['brand.name'] = normalizedBrand;
                        needsUpdate = true;
                    }
                }

                // Normalize series name
                if (record.series?.name) {
                    const normalizedSeries = record.series.name.trim().toUpperCase();
                    if (normalizedSeries !== record.series.name) {
                        updateData['series.name'] = normalizedSeries;
                        needsUpdate = true;
                    }
                }

                // Normalize category name
                if (record.category?.name) {
                    const normalizedCategory = record.category.name.trim().toUpperCase();
                    if (normalizedCategory !== record.category.name) {
                        updateData['category.name'] = normalizedCategory;
                        needsUpdate = true;
                    }
                }

                // Normalize family name
                if (record.family?.name) {
                    const normalizedFamily = record.family.name.trim().toUpperCase();
                    if (normalizedFamily !== record.family.name) {
                        updateData['family.name'] = normalizedFamily;
                        needsUpdate = true;
                    }
                }

                // Normalize fileName
                if (record.fileName) {
                    const normalizedFileName = record.fileName.trim();
                    if (normalizedFileName !== record.fileName) {
                        updateData.fileName = normalizedFileName;
                        needsUpdate = true;
                    }
                }

                // Clean up product arrays
                if (record.associatedProducts?.length > 0) {
                    const cleanedProducts = record.associatedProducts
                        .filter(p => p && typeof p === 'string' && p.trim().length > 0)
                        .map(p => p.trim());
                    if (JSON.stringify(cleanedProducts) !== JSON.stringify(record.associatedProducts)) {
                        updateData.associatedProducts = cleanedProducts;
                        needsUpdate = true;
                    }
                }

                if (record.similarProducts?.length > 0) {
                    const cleanedProducts = record.similarProducts
                        .filter(p => p && typeof p === 'string' && p.trim().length > 0)
                        .map(p => p.trim());
                    if (JSON.stringify(cleanedProducts) !== JSON.stringify(record.similarProducts)) {
                        updateData.similarProducts = cleanedProducts;
                        needsUpdate = true;
                    }
                }

                if (needsUpdate) {
                    bulkOps.push({
                        updateOne: {
                            filter: { _id: record._id },
                            update: { $set: updateData }
                        }
                    });
                }
            }

            if (bulkOps.length > 0) {
                const result = await FamilyItemConfigurationMaster.bulkWrite(bulkOps);
                updatedCount += result.modifiedCount;
                console.log(`📝 Updated batch ${Math.floor(i/batchSize) + 1}: ${result.modifiedCount} records`);
            }
        }

        console.log(`✅ Migration completed! Updated ${updatedCount} records`);

        // Create indexes (they should be created automatically, but let's ensure)
        console.log('🔍 Ensuring indexes are created...');
        await FamilyItemConfigurationMaster.createIndexes();
        console.log('✅ Indexes created successfully');

        // Show some statistics
        const stats = await FamilyItemConfigurationMaster.aggregate([
            {
                $group: {
                    _id: null,
                    totalRecords: { $sum: 1 },
                    activeRecords: { $sum: { $cond: [{ $eq: ['$isActive', true] }, 1, 0] } },
                    deletedRecords: { $sum: { $cond: [{ $eq: ['$isDeleted', true] }, 1, 0] } },
                    uniqueBrands: { $addToSet: '$brand.name' },
                    uniqueSeries: { $addToSet: '$series.name' },
                    uniqueCategories: { $addToSet: '$category.name' },
                    uniqueFamilies: { $addToSet: '$family.name' }
                }
            },
            {
                $project: {
                    totalRecords: 1,
                    activeRecords: 1,
                    deletedRecords: 1,
                    uniqueBrandsCount: { $size: '$uniqueBrands' },
                    uniqueSeriesCount: { $size: '$uniqueSeries' },
                    uniqueCategoriesCount: { $size: '$uniqueCategories' },
                    uniqueFamiliesCount: { $size: '$uniqueFamilies' }
                }
            }
        ]);

        if (stats.length > 0) {
            const stat = stats[0];
            console.log('\n📊 Collection Statistics:');
            console.log(`   Total Records: ${stat.totalRecords}`);
            console.log(`   Active Records: ${stat.activeRecords}`);
            console.log(`   Deleted Records: ${stat.deletedRecords}`);
            console.log(`   Unique Brands: ${stat.uniqueBrandsCount}`);
            console.log(`   Unique Series: ${stat.uniqueSeriesCount}`);
            console.log(`   Unique Categories: ${stat.uniqueCategoriesCount}`);
            console.log(`   Unique Families: ${stat.uniqueFamiliesCount}`);
        }

    } catch (error) {
        console.error('❌ Migration failed:', error);
        throw error;
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from MongoDB');
    }
}

// Test the new static methods
async function testNewMethods() {
    try {
        console.log('\n🧪 Testing new static methods...');
        
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/your-database');

        // Test findActive method
        const activeItems = await FamilyItemConfigurationMaster.findActive().limit(5);
        console.log(`✅ findActive(): Found ${activeItems.length} active items`);

        // Test findByFilters method
        const filteredItems = await FamilyItemConfigurationMaster.findByFilters({
            brand: ['SGI'],
            isActive: true
        }).limit(3);
        console.log(`✅ findByFilters(): Found ${filteredItems.length} SGI items`);

        // Test text search
        const searchResults = await FamilyItemConfigurationMaster.find({
            $text: { $search: 'SGI FLEX' }
        }).limit(3);
        console.log(`✅ Text search: Found ${searchResults.length} items matching 'SGI FLEX'`);

    } catch (error) {
        console.error('❌ Testing failed:', error);
    } finally {
        await mongoose.disconnect();
    }
}

// Run migration if called directly
if (require.main === module) {
    migrateFamilyItems()
        .then(() => testNewMethods())
        .then(() => {
            console.log('\n🎉 Migration and testing completed successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 Process failed:', error);
            process.exit(1);
        });
}

module.exports = { migrateFamilyItems, testNewMethods };
