const mongoose = require('mongoose');

// <PERSON>am <PERSON>le Attribute Schema
const WattsAttributeSchema = new mongoose.Schema({
    name: {
        type: String,
        default: '',
    },
    modelSlug: {
        type: String,
        default: 'watts',
    },
    isActive: {
        type: Boolean,
        default: true,
    },
    isDelete: {
        type: Boolean,
        default: false,
    },
});

module.exports = mongoose.model('WattsAttribute', WattsAttributeSchema);
