const mongoose = require('mongoose');

// Beam <PERSON>le Attribute Schema
const BendAttributeSchema = new mongoose.Schema({
    name: {
        type: String,
        default: '',
    },
    modelSlug: {
        type: String,
        default: 'bnd',
    },
    isActive: {
        type: Boolean,
        default: true,
    },
    isDelete: {
        type: Boolean,
        default: false,
    },
});

module.exports = mongoose.model('BendAttribute', BendAttributeSchema);
