const mongoose = require('mongoose');

// Beam <PERSON>le Attribute Schema
const SuspensionAttributeSchema = new mongoose.Schema({
    name: {
        type: String,
        default: '',
    },
    modelSlug: {
        type: String,
        default: 'sus',
    },
    isActive: {
        type: Boolean,
        default: true,
    },
    isDelete: {
        type: Boolean,
        default: false,
    },
});

module.exports = mongoose.model('SuspensionAttribute', SuspensionAttributeSchema);
