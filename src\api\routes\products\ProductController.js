const {
    models: { Product, ProductLanding, ItemConfigurationMaster, FamilyItemConfigurationMaster, BrandFilter, SeriesFilter, Category, FamilyFilter },
} = require('../../../../lib/models');
const { utcDateTime } = require('../../../../lib/util');

class ProductController {
    async createProductFamily(req, res) {
        const {
            bannerSection,
            productDetailFamilyRecordInternal,
            BoardContainer,
            productModels,
            downloads,
            similarProducts,
        } = req.body;

        try {
            // Ensure bannerSection is properly formatted
            const formattedBannerSection = bannerSection || {
                banner: [],
                title: '',
                isTitleVisible: true,
                familyName: { label: 'Product Family', isVisible: true },
                modelsAndSpecs: { label: 'Models & Specs', isVisible: true },
                downloads: { label: 'Downloads', isVisible: true },
                itemConfigurator: { label: 'Item Configurator', isVisible: true },
            };

            // Ensure product detail family record is properly formatted
            const formattedProductDetailFamilyRecordInternal = productDetailFamilyRecordInternal || {
                family: { id: null, name: '' },
                brand: { id: null, name: '' },
                series: { id: null, name: '' },
                category: { id: null, name: '' },
                collections: { id: null, name: '' },
                material: { id: null, name: '' },
                itemCategory: '',
                resourceImage: { mediaType: 'image', mediaUrl: '' },
                productTileImage: { mediaType: 'image', mediaUrl: '' },
                productHoverImage: { mediaType: 'image', mediaUrl: '' },
                categoryTileImage: { mediaType: 'image', mediaUrl: '' },
                categoryHoverImage: { mediaType: 'image', mediaUrl: '' },
                collectionImage: { mediaType: 'image', mediaUrl: '' },
                collectionHoverImage: { mediaType: 'image', mediaUrl: '' },
                finishTileImage: { mediaType: 'image', mediaUrl: '' },
                finishHoverImage: { mediaType: 'image', mediaUrl: '' },
            };

            // Ensure BoardContainer is properly formatted
            const formattedBoardContainer = BoardContainer || [];

            // Ensure product models are properly formatted
            const formattedProductModels = productModels || [];

            // Ensure downloads are properly formatted
            const formattedDownloads = downloads || {
                specificationDownload: { fileName: '', isAvailable: false },
                installationDownload: { fileName: '', isAvailable: false },
                iesDownload: { fileName: '', isAvailable: false },
                cadDownload: { fileName: '', isAvailable: false },
                videoDownload: { fileName: '', isAvailable: false },
                lookbookDownload: { fileName: '', isAvailable: false },
                catalogueDownload: { fileName: '', isAvailable: false },
            };

            const productCount = await Product.countDocuments({ isDeleted: false });

            // Creating a new product object
            const newProduct = new Product({
                bannerSection: formattedBannerSection,
                productDetailFamilyRecordInternal: formattedProductDetailFamilyRecordInternal,
                BoardContainer: formattedBoardContainer,
                productModels: formattedProductModels,
                downloads: formattedDownloads,
                similarProducts: similarProducts || [],
                sortOrder: productCount ? productCount + 1 : 1,
            });

            // Saving the new product to the database
            const savedProduct = await newProduct.save();

            return res.success(savedProduct, req.__('PRODUCT_CREATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error creating product:', error);
            return res.serverError(error.message, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async editProductFamily(req, res) {
        const {
            bannerSection,
            productDetailFamilyRecordInternal,
            BoardContainer,
            productModels,
            downloads,
            similarProducts,
            sortOrder,
        } = req.body;
        const productId = req.params.productId; // assuming the product ID is passed as a parameter in the URL

        try {
            // Find the product by ID
            const product = await Product.findById(productId);
            if (!product) {
                return res.notFound({}, req.__('PRODUCT_NOT_FOUND'));
            }

            // Ensure bannerSection is properly formatted or use existing one
            const formattedBannerSection = bannerSection || product.bannerSection;

            // Ensure product detail family record is properly formatted or use existing one
            const formattedProductDetailFamilyRecordInternal =
                productDetailFamilyRecordInternal || product.productDetailFamilyRecordInternal;

            // Ensure BoardContainer is properly formatted or use existing one
            const formattedBoardContainer = BoardContainer || product.BoardContainer;

            // Ensure product models are properly formatted or use existing one
            const formattedProductModels = productModels || product.productModels;

            // Ensure downloads are properly formatted or use existing one
            const formattedDownloads = downloads || product.downloads;

            // Update the product with the new data
            product.bannerSection = formattedBannerSection;
            product.productDetailFamilyRecordInternal = formattedProductDetailFamilyRecordInternal;
            product.BoardContainer = formattedBoardContainer;
            product.productModels = formattedProductModels;
            product.downloads = formattedDownloads;
            product.sortOrder = sortOrder;
            product.similarProducts = similarProducts || product.similarProducts;
            // Save the updated product
            const updatedProduct = await product.save();

            return res.success(updatedProduct, req.__('PRODUCT_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error editing product:', error);
            return res.serverError(error.message, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async listProductsFamily(req, res) {
        const {
            id,
            search,
            isActive,
            minSortOrder,
            maxSortOrder,
            page = 1,
            limit = 10,
            FilterProductFamily,
            FilterBrand,
            FilterCategory,
            FilterMaterial,
            FilterCollection,
            FilterType, // Filter for TYPE
            FilterSeries, // Filter for SERIES
            FilterApplication, // Filter for APPLICATION
            getAllRecords, // Query parameter to determine if all records should be fetched
            sortBy = 'createdAt', // Field to sort by, default is 'createdAt'
            sortOrder = 'desc', // Sorting order, default is 'desc'
        } = req.query;

        const finalResponse = {
            items: [],
            count: 0,
            page: getAllRecords ? null : page, // Set to null if fetching all records
            limit: getAllRecords ? null : limit, // Set to null if fetching all records
        };

        // Convert page and limit to numbers
        const pageNumber = parseInt(page);
        const limitNumber = parseInt(limit);

        // Calculate the number of items to skip for pagination
        let skip = (pageNumber - 1) * limitNumber;

        try {
            let qry = { isDeleted: false }; // Default filter for non-deleted products

            // Add search filter for product name or description
            if (search) {
                qry.$or = [
                    { 'productModels.title': { $regex: new RegExp(search, 'i') } },
                    { 'productModels.description': { $regex: new RegExp(search, 'i') } },
                ];
            }

            // Add filter for isActive flag
            if (isActive !== undefined) {
                qry.isActive = isActive === 'true'; // Convert string 'true'/'false' to boolean
            }

            // Apply sortOrder range query if provided
            if (minSortOrder && maxSortOrder) {
                qry['productModels.sortOrder'] = { $gte: parseInt(minSortOrder), $lte: parseInt(maxSortOrder) };
            } else if (minSortOrder) {
                qry['productModels.sortOrder'] = { $gte: parseInt(minSortOrder) };
            } else if (maxSortOrder) {
                qry['productModels.sortOrder'] = { $lte: parseInt(maxSortOrder) };
            }

            // Filter by specific IDs
            if (id) {
                qry._id = { $in: id.split(',') };
            }

            // Filter by product family, brand, category, type, series, and application names
            const filterConditions = [];

            if (FilterProductFamily) {
                filterConditions.push({
                    'productDetailFamilyRecordInternal.family.name': { $in: FilterProductFamily.split(',') },
                });
            }
            if (FilterBrand) {
                filterConditions.push({
                    'productDetailFamilyRecordInternal.brand.name': { $in: FilterBrand.split(',') },
                });
            }
            if (FilterCategory) {
                filterConditions.push({
                    'productDetailFamilyRecordInternal.category.name': { $in: FilterCategory.split(',') },
                });
            }
            if (FilterMaterial) {
                filterConditions.push({
                    'productDetailFamilyRecordInternal.material.name': { $in: FilterMaterial.split(',') },
                });
            }
            if (FilterCollection) {
                filterConditions.push({
                    'productDetailFamilyRecordInternal.collection.name': { $in: FilterCollection.split(',') },
                });
            }
            if (FilterType) {
                filterConditions.push({
                    'productDetailFamilyRecordInternal.type.name': { $in: FilterType.split(',') },
                });
            }
            if (FilterSeries) {
                filterConditions.push({
                    'productDetailFamilyRecordInternal.series.name': { $in: FilterSeries.split(',') },
                });
            }
            if (FilterApplication) {
                filterConditions.push({
                    'productDetailFamilyRecordInternal.application.name': { $in: FilterApplication.split(',') },
                });
            }

            if (filterConditions.length > 0) {
                qry.$and = filterConditions;
            }

            // Get the count of products that match the filters
            const productCount = await Product.countDocuments(qry);

            // Determine the sorting options based on client input
            const sortOptions = {};
            sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1; // 1 for ascending, -1 for descending

            // Fetch the products based on the filters and sorting
            let productsQuery = Product.find(qry).sort(sortOptions);

            // If getAllRecords is not 'true', apply pagination
            if (getAllRecords !== 'true') {
                productsQuery = productsQuery.skip(skip).limit(limitNumber);
            }

            const products = await productsQuery.exec();

            // If no products are found, return a warning
            if (products.length === 0) {
                return res.warn({}, req.__('NO_PRODUCTS_FOUND'));
            }

            // Set the results in the finalResponse object
            finalResponse.items = products;
            finalResponse.count = productCount;

            // Respond with the products and the total count
            return res.success(finalResponse, req.__('PRODUCTS_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            // Log the error and return a server error response
            console.error('Error fetching products:', error);
            return res.serverError(error.message, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async getProductDetail(req, res) {
        const { productId } = req.params; // Get product ID from the route parameter

        try {
            // Fetch the product details using the provided product ID
            const product = await Product.findById(productId).exec();

            // If the product is not found, return a not found response
            if (!product) {
                return res.warn({}, req.__('PRODUCT_NOT_FOUND'));
            }

            // Return the product details in the response
            return res.success(product, req.__('PRODUCT_DETAIL_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            // Log the error and return a server error response
            console.error('Error fetching product details:', error);
            return res.serverError(error.message, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    //ADD new Booard containe or Board image
    async addBoardContainer(req, res) {
        try {
            const { productId } = req.params;
            const { mediaType, mediaUrl, mediaTypeMobi, mediaUrlMobi, sortOrder, isActive, isDelete } = req.body;

            const newBoardContainer = {
                mediaType,
                mediaUrl,
                mediaTypeMobi,
                mediaUrlMobi,
                sortOrder,
                isActive,
                isDelete,
            };

            const product = await Product.findById(productId);
            if (!product) return res.notFound({}, req.__('PRODUCT_NOT_FOUND'));

            product.BoardContainer.push(newBoardContainer);
            await product.save();
            return res.success(newBoardContainer, req.__('BOARD_CONTAINER_ADDED_SUCCESSFULLY'));
        } catch (error) {
            return res.serverError(error.message, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    //Edit existing  Booard containe or Board image
    async updateBoardContainer(req, res) {
        try {
            const { productId, boardId } = req.params;
            const { mediaType, mediaUrl, mediaTypeMobi, mediaUrlMobi, sortOrder, isActive, isDelete } = req.body;

            const product = await Product.findById(productId);
            if (!product) return res.notFound({}, req.__('PRODUCT_NOT_FOUND'));

            const boardContainer = product.BoardContainer.id(boardId);
            if (!boardContainer) return res.notFound({}, req.__('BOARD_CONTAINER_NOT_FOUND'));

            // Update fields if they exist in the request body
            if (mediaType) boardContainer.mediaType = mediaType;
            if (mediaUrl) boardContainer.mediaUrl = mediaUrl;
            if (mediaTypeMobi) boardContainer.mediaTypeMobi = mediaTypeMobi;
            if (mediaUrlMobi) boardContainer.mediaUrlMobi = mediaUrlMobi;
            if (sortOrder) boardContainer.sortOrder = sortOrder;
            if (isActive !== undefined) boardContainer.isActive = isActive;
            if (isDelete !== undefined) boardContainer.isDelete = isDelete;

            await product.save();
            return res.success(boardContainer, req.__('BOARD_CONTAINER_UPDATE_SUCCESSFULLY'));
        } catch (error) {
            console.error(error);
            return res.serverError(error.message, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    //Delete existing  Booard containe or Board image
    async deleteBoardContainer(req, res) {
        const { productId, containerId } = req.params;

        try {
            // Find the product by productId
            const product = await Product.findById(productId);

            if (!product) {
                return res.notFound({}, req.__('PRODUCT_NOT_FOUND'));
            }

            // Find the board container to remove
            const containerIndex = product.BoardContainer.findIndex(
                container => container._id.toString() === containerId
            );

            if (containerIndex === -1) {
                return res.notFound({}, req.__('BOARD_CONTAINER_NOT_FOUND'));
            }

            // Remove the board container from the array
            product.BoardContainer.splice(containerIndex, 1);

            // Save the updated product document
            await product.save();
            return res.success({}, req.__('BOARD_CONTAINER_DELETE_SUCCESSFULLY'));
        } catch (error) {
            console.error(error);
            return res.serverError(error.message, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add new Product Model
    async addProductModel(req, res) {
        try {
            const { productId } = req.params;
            const {
                title,
                description,
                modelSKU,
                productModelImages,
                downloads,
                sortOrder,
                isActive,
                isDelete,
            } = req.body;

            const newProductModel = {
                title,
                description,
                modelSKU,
                productModelImages,
                downloads,
                sortOrder,
                isActive,
                isDelete,
            };

            const product = await Product.findById(productId);
            if (!product) return res.notFound({}, req.__('PRODUCT_NOT_FOUND'));

            product.productModels.push(newProductModel);
            await product.save();

            return res.success(newProductModel, req.__('PRODUCT_MODEL_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error(error);
            return res.serverError(error.message, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit existing Product Model
    async updateProductModel(req, res) {
        try {
            const { productId, modelId } = req.params;
            const {
                title,
                description,
                modelSKU,
                productModelImages,
                downloads,
                sortOrder,
                isActive,
                isDelete,
            } = req.body;

            const product = await Product.findById(productId);
            if (!product) return res.notFound({}, req.__('PRODUCT_NOT_FOUND'));

            const productModel = product.productModels.id(modelId);
            if (!productModel) return res.notFound({}, req.__('PRODUCT_MODEL_NOT_FOUND'));

            // Update fields if they exist in the request body
            if (title) productModel.title = title;
            if (description) productModel.description = description;
            if (modelSKU) productModel.modelSKU = modelSKU;
            if (productModelImages) productModel.productModelImages = productModelImages;
            if (downloads) productModel.downloads = downloads;
            if (sortOrder) productModel.sortOrder = sortOrder;
            if (isActive !== undefined) productModel.isActive = isActive;
            if (isDelete !== undefined) productModel.isDelete = isDelete;

            await product.save();
            return res.success(productModel, req.__('PRODUCT_MODEL_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error(error);
            return res.serverError(error.message, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete existing Product Model
    async deleteProductModel(req, res) {
        const { productId, modelId } = req.params;

        try {
            // Find the product by productId
            const product = await Product.findById(productId);
            if (!product) return res.notFound({}, req.__('PRODUCT_NOT_FOUND'));

            // Find the product model to remove
            const modelIndex = product.productModels.findIndex(model => model._id.toString() === modelId);

            if (modelIndex === -1) return res.notFound({}, req.__('PRODUCT_MODEL_NOT_FOUND'));

            // Remove the product model from the array
            product.productModels.splice(modelIndex, 1);

            // Save the updated product document
            await product.save();
            return res.success({}, req.__('PRODUCT_MODEL_DELETED_SUCCESSFULLY'));
        } catch (error) {
            console.error(error);
            return res.serverError(error.message, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // async bulkItemConfigurationImport(req, res) {
    //     try {
    //         // Ensure data is provided
    //         const { data } = req.body;
    //         if (!data || !Array.isArray(data)) {
    //             return res.warn("Invalid data format. 'data' should be an array of items.");
    //         }

    //         // Extract all itemCategory values from the input data
    //         const itemCategories = data.map(item => item.itemCategory);

    //         // Fetch all existing items in one query
    //         const existingItems = await ItemConfigurationMaster.find({
    //             itemCategory: { $in: itemCategories },
    //             isDeleted: false,
    //         });
    //         // Create a map of existing items for quick lookup
    //         const existingItemsMap = new Map();
    //         existingItems.forEach(item => {
    //             existingItemsMap.set(item.itemCategory, item);
    //         });

    //         // Prepare arrays for bulk updates and inserts
    //         const updates = [];
    //         const inserts = [];

    //         data.forEach(item => {
    //             const existingItem = existingItemsMap.get(item.itemCategory);

    //             if (existingItem) {
    //                 // Prepare update operation
    //                 updates.push({
    //                     updateOne: {
    //                         filter: { itemCategory: item.itemCategory },
    //                         update: {
    //                             $set: {
    //                                 brand: item.brand || existingItem.brand,
    //                                 series: item.series || existingItem.series,
    //                                 category: item.category || existingItem.category,
    //                                 family: item.family || existingItem.family,
    //                                 model: item.model || existingItem.model,
    //                                 modelSKU: item.modelSKU || existingItem.modelSKU,
    //                                 application: item.application || existingItem.application,
    //                                 modelSKUDescription: item.modelSKUDescription || existingItem.modelSKUDescription,
    //                                 cutsheetTitle: item.cutsheetTitle || existingItem.cutsheetTitle,
    //                                 websiteModelAccordionTitle:
    //                                     item.websiteModelAccordionTitle || existingItem.websiteModelAccordionTitle,
    //                                 antiGlare: item.antiGlare || existingItem.antiGlare,
    //                                 beamAngle: item.beamAngle || existingItem.beamAngle,
    //                                 bend: item.bend || existingItem.bend,
    //                                 connectionCable: item.connectionCable || existingItem.connectionCable,
    //                                 driver: item.driver || existingItem.driver,
    //                                 finish: item.finish || existingItem.finish,
    //                                 length: item.length || existingItem.length,
    //                                 lens: item.lens || existingItem.lens,
    //                                 lightColour: item.lightColour || existingItem.lightColour,
    //                                 operatingVoltage: item.operatingVoltage || existingItem.operatingVoltage,
    //                                 suspension: item.suspension || existingItem.suspension,
    //                                 trim: item.trim || existingItem.trim,
    //                                 watts: item.watts || existingItem.watts,
    //                                 // dealerCost: parseFloat(item.dealerCost) || existingItem.dealerCost,
    //                                 // dealerMSRP: parseFloat(item.dealerMSRP) || existingItem.dealerMSRP,
    //                                 // repCost: parseFloat(item.repCost) || existingItem.repCost,
    //                                 // distributorCost: parseFloat(item.distributorCost) || existingItem.distributorCost,
    //                                 // contractorCost: parseFloat(item.contractorCost) || existingItem.contractorCost,
    //                                 // endUserCost: parseFloat(item.endUserCost) || existingItem.endUserCost,
    //                                 isActive: item.isActive !== undefined ? item.isActive : existingItem.isActive,
    //                                 isDeleted: item.isDeleted !== undefined ? item.isDeleted : existingItem.isDeleted,
    //                                 cutPoints: item.cutPoints !== undefined ? item.cutPoints : existingItem.cutPoints,
    //                                 uom: item.uom !== undefined ? item.uom : existingItem.uom,
    //                                 type: item.type !== undefined ? item.type : existingItem.type,
    //                                 reflector: item.reflector !== undefined ? item.reflector : existingItem.reflector,
    //                                 cabType: item.cabType !== undefined ? item.cabType : existingItem.cabType,
    //                                 connection:
    //                                     item.connection !== undefined ? item.connection : existingItem.connection,
    //                                 acctyp: item.acctyp !== undefined ? item.acctyp : existingItem.acctyp,
    //                                 chan: item.chan !== undefined ? item.chan : existingItem.chan,
    //                                 collection: item.collection !== undefined ? item.collection : existingItem.collection,
    //                                 material: item.material !== undefined ? item.material : existingItem.material,
    //                                 wattsPerUOM: item.wattsPerUOM !== undefined ? item.wattsPerUOM : existingItem.wattsPerUOM,
    //                                 lumensPerUOM: item.lumensPerUOM !== undefined ? item.lumensPerUOM : existingItem.lumensPerUOM,
    //                                 wattsPerFixtureRange: item.wattsPerFixtureRange !== undefined ? item.wattsPerFixtureRange : existingItem.wattsPerFixtureRange,
    //                                 lumensPerFixtureRange: item.lumensPerFixtureRange !== undefined ? item.lumensPerFixtureRange : existingItem.lumensPerFixtureRange,
    //                                 itemCategoryCode:
    //                                     item.itemCategoryCode !== undefined
    //                                         ? item.itemCategoryCode
    //                                         : existingItem.itemCategoryCode,
    //                             },
    //                         },
    //                     },
    //                 });
    //             } else {
    //                 // Prepare insert operation
    //                 inserts.push({
    //                     brand: item.brand || '',
    //                     series: item.series || '',
    //                     category: item.category || '',
    //                     itemCategory: item.itemCategory || '',
    //                     family: item.family || '',
    //                     model: item.model || '',
    //                     modelSKU: item.modelSKU || '',
    //                     application: item.application || [],
    //                     modelSKUDescription: item.modelSKUDescription || '',
    //                     cutsheetTitle: item.cutsheetTitle || '',
    //                     websiteModelAccordionTitle: item.websiteModelAccordionTitle || '',
    //                     cutPoints: item.cutPoints || '',
    //                     uom: item.uom || '',
    //                     type: item.type || [],
    //                     reflector: item.reflector || [],
    //                     cabType: item.cabType || [],
    //                     connection: item.connection || '',
    //                     acctyp: item.acctyp || [],
    //                     chan: item.chan || [],
    //                     itemCategoryCode: item.itemCategoryCode || '',
    //                     antiGlare: item.antiGlare || [],
    //                     beamAngle: item.beamAngle || [],
    //                     bend: item.bend || [],
    //                     connectionCable: item.connectionCable || [],
    //                     driver: item.driver || [],
    //                     finish: item.finish || [],
    //                     length: item.length || [],
    //                     lens: item.lens || [],
    //                     lightColour: item.lightColour || [],
    //                     operatingVoltage: item.operatingVoltage || [],
    //                     suspension: item.suspension || [],
    //                     trim: item.trim || [],
    //                     watts: item.watts || [],
    //                     collections: item.collections || [],
    //                     material: item.material || [],
    //                     wattsPerUOM: item.wattsPerUOM || '',
    //                     lumensPerUOM: item.lumensPerUOM || '',
    //                     wattsPerFixtureRange: item.wattsPerFixtureRange || '',
    //                     lumensPerFixtureRange: item.lumensPerFixtureRange || '',
    //                     // dealerCost: parseFloat(item.dealerCost) || 0,
    //                     // dealerMSRP: parseFloat(item.dealerMSRP) || 0,
    //                     // repCost: parseFloat(item.repCost) || 0,
    //                     // distributorCost: parseFloat(item.distributorCost) || 0,
    //                     // contractorCost: parseFloat(item.contractorCost) || 0,
    //                     // endUserCost: parseFloat(item.endUserCost) || 0,
    //                     isActive: item.isActive !== undefined ? item.isActive : true,
    //                     isDeleted: item.isDeleted !== undefined ? item.isDeleted : false,
    //                 });
    //             }
    //         });

    //         // Perform bulk operations
    //         if (updates.length > 0) {
    //             await ItemConfigurationMaster.bulkWrite(updates);
    //         }
    //         if (inserts.length > 0) {
    //             await ItemConfigurationMaster.insertMany(inserts);
    //         }

    //         return res.success({ message: req.__('ITEMS_IMPORT_SUCCESS') });
    //     } catch (error) {
    //         console.error('Error importing items:', error);
    //         return res.serverError(error.message, req.__('INTERNAL_SERVER_ERROR'));
    //     }
    // }

    async bulkItemConfigurationImport(req, res) {
        try {
            const { data } = req.body;
            if (!data || !Array.isArray(data)) {
                return res.warn("Invalid data format. 'data' should be an array of items.");
            }

            const BATCH_SIZE = 500;
            const totalBatches = Math.ceil(data.length / BATCH_SIZE);

            const bulkOpsPromises = [];

            for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                const batchData = data.slice(batchIndex * BATCH_SIZE, (batchIndex + 1) * BATCH_SIZE);
                const itemCategoryCodes = batchData.map(item => item.itemCategoryCode);

                // Fetch existing records using itemCategoryCode
                const existingItems = await ItemConfigurationMaster.find(
                    { itemCategoryCode: { $in: itemCategoryCodes }, isDeleted: false },
                    { _id: 1, itemCategoryCode: 1 }
                );

                // Create a map of existing items for quick lookup
                const existingItemsMap = new Map();
                existingItems.forEach(item => existingItemsMap.set(item.itemCategoryCode, item._id)); // Store `_id`

                const bulkOperations = batchData.map(item => {
                    const existingItemId = existingItemsMap.get(item.itemCategoryCode);

                    const updateData = { ...item };
                    delete updateData._id; // Ensure `_id` is not in the update data

                    return existingItemId
                        ? {
                              updateOne: {
                                  filter: { _id: existingItemId }, // Use `_id` for updating
                                  update: { $set: updateData },
                              },
                          }
                        : {
                              insertOne: { document: updateData }, // Insert new record
                          };
                });

                if (bulkOperations.length > 0) {
                    bulkOpsPromises.push(ItemConfigurationMaster.bulkWrite(bulkOperations));
                }
            }

            // Execute all batch operations concurrently
            await Promise.all(bulkOpsPromises);

            return res.success({ message: req.__('ITEMS_IMPORT_SUCCESS') });
        } catch (error) {
            console.error('Error importing items:', error);
            return res.serverError(error.message, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async listItemConfigurations(req, res) {
        const {
            search,
            brand,
            series,
            category,
            itemCategory,
            family,
            model,
            modelSKU,
            application,
            modelSKUDescription,
            cutsheetTitle,
            antiGlare,
            beamAngle,
            bend,
            connectionCable,
            driver,
            finish,
            length,
            lens,
            lightColour,
            operatingVoltage,
            suspension,
            trim,
            watts,
            dealerCost,
            distributorCost,
            contractorCost,
            endUserCost,
            page,
            limit,
            getAllRecords,
            acctyp,
            collections,
            material,
            wattsPerUOM,
            lumensPerUOM,
            wattsPerFixtureRange,
            lumensPerFixtureRange,
        } = req.query;

        const finalResponse = {
            items: [],
            count: 0,
            page: getAllRecords ? null : page,
            limit: getAllRecords ? null : limit,
        };

        const pageNumber = parseInt(page);
        const limitNumber = parseInt(limit);
        let skip = (pageNumber - 1) * limitNumber;

        try {
            let qry = { isDeleted: false };

            // Apply search filters
            if (search) {
                qry.$or = [
                    { brand: { $regex: new RegExp(search, 'i') } },
                    { series: { $regex: new RegExp(search, 'i') } },
                    { category: { $regex: new RegExp(search, 'i') } },
                    { itemCategory: { $regex: new RegExp(search, 'i') } },
                    { family: { $regex: new RegExp(search, 'i') } },
                    { model: { $regex: new RegExp(search, 'i') } },
                    { modelSKU: { $regex: new RegExp(search, 'i') } },
                ];
            }

            // Convert brand, series, category, and family values to uppercase before filtering
            if (brand) qry.brand = { $in: brand.split(',').map(b => b.toUpperCase()) };
            if (series) qry.series = { $in: series.split(',').map(s => s.toUpperCase()) };
            if (category) qry.category = { $in: category.split(',').map(c => c.toUpperCase()) };
            if (family) qry.family = { $in: family.split(',').map(f => f.toUpperCase()) };

            if (model) qry.model = { $in: model.split(',') };
            if (modelSKU) qry.modelSKU = { $in: modelSKU.split(',') };
            if (itemCategory) qry.itemCategory = { $in: itemCategory.split(',') };
            if (material) qry.material = { $in: material.split(',') };
            if (collections) qry.collections = { $in: collections.split(',') };

            if (application) qry.application = { $in: application.split(',') };
            if (modelSKUDescription) qry.modelSKUDescription = modelSKUDescription;
            if (cutsheetTitle) qry.cutsheetTitle = cutsheetTitle;
            if (antiGlare) qry.antiGlare = { $in: antiGlare.split(',') };
            if (beamAngle) qry.beamAngle = { $in: beamAngle.split(',') };
            if (bend) qry.bend = { $in: bend.split(',') };
            if (connectionCable) qry.connectionCable = { $in: connectionCable.split(',') };
            if (driver) qry.driver = { $in: driver.split(',') };
            if (finish) qry.finish = { $in: finish.split(',') };
            if (length) qry.length = { $in: length.split(',') };
            if (lens) qry.lens = { $in: lens.split(',') };
            if (lightColour) qry.lightColour = { $in: lightColour.split(',') };
            if (operatingVoltage) qry.operatingVoltage = { $in: operatingVoltage.split(',') };
            if (suspension) qry.suspension = { $in: suspension.split(',') };
            if (trim) qry.trim = { $in: trim.split(',') };
            if (watts) qry.watts = { $in: watts.split(',') };
            if (dealerCost) qry.dealerCost = { $gte: dealerCost };
            if (distributorCost) qry.distributorCost = { $gte: distributorCost };
            if (contractorCost) qry.contractorCost = { $gte: contractorCost };
            if (endUserCost) qry.endUserCost = { $gte: endUserCost };

            const itemCount = await ItemConfigurationMaster.countDocuments(qry);

            let itemsQuery = ItemConfigurationMaster.find(qry).sort({ createdAt: -1 });
            if (getAllRecords !== 'true') {
                itemsQuery = itemsQuery.skip(skip).limit(limitNumber);
            }

            const items = await itemsQuery.exec();

            if (items.length === 0) {
                return res.warn('', req.__('NO_ITEM_CONFIG_FOUND'));
            }

            finalResponse.items = items;
            finalResponse.count = itemCount;

            res.success(finalResponse);
        } catch (error) {
            console.log('Error fetching item configurations:', error);
            res.warn(error);
        }
    }

    async deleteAllItemConfigurations(req, res) {
        try {
            // Delete all records from the ItemConfigurationMaster collection
            const result = await ItemConfigurationMaster.deleteMany({});

            // Return success response
            return res.success(
                { deletedCount: result.deletedCount },
                req.__('ALL_ITEM_CONFIGURATIONS_DELETED_SUCCESSFULLY')
            );
        } catch (error) {
            console.error('Error deleting item configurations:', error);
            return res.serverError(error.message, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add or update inspiration banner section
    async addOrUpdatebanner(req, res) {
        try {
            const { id, mediaType, fileName, fileTitle, mediaTypeMobi, fileNameMobi, fileTitleMobi } = req.body;

            const updatedInspiration = await ProductLanding.findOneAndUpdate(
                {},
                {
                    $set: {
                        mediaType,
                        fileName,
                        fileTitle,
                        mediaTypeMobi,
                        fileNameMobi,
                        fileTitleMobi,
                    },
                },
                {
                    new: true, // Return the updated document
                    upsert: true, // Insert if not found
                    projection: {
                        mediaType: 1,
                        fileName: 1,
                        fileTitle: 1,
                        mediaTypeMobi: 1,
                        fileNameMobi: 1,
                        fileTitleMobi: 1,
                    },
                }
            );

            return res.success(updatedInspiration, req.__('BANNER_UPDATE_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error updating banner:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    // Get inspiration landing banner section
    async getBanner(req, res) {
        try {
            const banner = await ProductLanding.findOne(
                {},
                {
                    mediaType: 1,
                    fileName: 1,
                    fileTitle: 1,
                    mediaTypeMobi: 1,
                    fileNameMobi: 1,
                    fileTitleMobi: 1,
                }
            );

            if (!banner) {
                return res.notFound({}, req.__('BANNER_NOT_FOUND'));
            }

            return res.success(banner, req.__('BANNER_FETCH_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error fetching banner:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    //Family Item intregration
    async bulkFamilyItemConfigurationImport(req, res) {
        try {
            const { data, filename } = req.body;
            if (!data || !Array.isArray(data)) {
                return res.warn("Invalid data format. 'data' should be an array.");
            }

            const BATCH_SIZE = 500;
            const totalBatches = Math.ceil(data.length / BATCH_SIZE);

            // Pre-fetch all existing filter records to minimize database queries
            const [existingBrands, existingSeries, existingCategories, existingFamilies] = await Promise.all([
                BrandFilter.find({ isDelete: false }, { _id: 1, name: 1 }),
                SeriesFilter.find({ isDelete: false }, { _id: 1, name: 1 }),
                Category.find({ isDeleted: false }, { _id: 1, name: 1 }),
                FamilyFilter.find({ isDelete: false }, { _id: 1, name: 1 })
            ]);

            // Create lookup maps for existing records
            const brandMap = new Map(existingBrands.map(b => [b.name.toUpperCase(), b]));
            const seriesMap = new Map(existingSeries.map(s => [s.name.toUpperCase(), s]));
            const categoryMap = new Map(existingCategories.map(c => [c.name.toUpperCase(), c]));
            const familyMap = new Map(existingFamilies.map(f => [f.name.toUpperCase(), f]));

            // Process data and create missing filter records
            const processedData = await this.processAndCreateFilterRecords(
                data, brandMap, seriesMap, categoryMap, familyMap, filename
            );

            // Bulk insert/update family item configurations
            const bulkOpsPromises = [];
            for (let i = 0; i < totalBatches; i++) {
                const batch = processedData.slice(i * BATCH_SIZE, (i + 1) * BATCH_SIZE);
                const familyCodes = batch.map(f => f.productFamilyCode);

                const existing = await FamilyItemConfigurationMaster.find(
                    { productFamilyCode: { $in: familyCodes }, isDeleted: false },
                    { _id: 1, productFamilyCode: 1 }
                );

                const existingMap = new Map();
                existing.forEach(f => existingMap.set(f.productFamilyCode, f._id));

                const bulkOps = batch.map(item => {
                    const existingId = existingMap.get(item.productFamilyCode);
                    const updateData = { ...item };
                    delete updateData._id;

                    return existingId
                        ? {
                              updateOne: {
                                  filter: { _id: existingId },
                                  update: { $set: updateData },
                              },
                          }
                        : {
                              insertOne: { document: updateData },
                          };
                });

                if (bulkOps.length) {
                    bulkOpsPromises.push(FamilyItemConfigurationMaster.bulkWrite(bulkOps));
                }
            }

            await Promise.all(bulkOpsPromises);
            return res.success({
                message: req.__('FAMILY_ITEMS_IMPORT_SUCCESS'),
                processedCount: data.length,
                filename: filename || 'Unknown'
            });
        } catch (error) {
            console.error('Import error:', error);
            return res.serverError(error.message, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async listFamilyItemConfigurations(req, res) {
        const { search, brand, series, category, family, page, limit, getAllRecords } = req.query;

        const finalResponse = {
            items: [],
            count: 0,
            page: getAllRecords ? null : page,
            limit: getAllRecords ? null : limit,
        };

        const pageNumber = parseInt(page);
        const limitNumber = parseInt(limit);
        const skip = (pageNumber - 1) * limitNumber;

        try {
            let qry = { isDeleted: false };

            if (search) {
                qry.$or = [
                    { productFamilyCode: { $regex: new RegExp(search, 'i') } },
                    { productFamilyDescription: { $regex: new RegExp(search, 'i') } },
                    { brand: { $regex: new RegExp(search, 'i') } },
                    { series: { $regex: new RegExp(search, 'i') } },
                    { category: { $regex: new RegExp(search, 'i') } },
                    { family: { $regex: new RegExp(search, 'i') } },
                ];
            }

            if (brand) qry['brand.name'] = { $in: brand.split(',').map(s => s.toUpperCase()) };
            if (series) qry['series.name'] = { $in: series.split(',').map(s => s.toUpperCase()) };
            if (category) qry['category.name'] = { $in: category.split(',').map(s => s.toUpperCase()) };
            if (family) qry['family.name'] = { $in: family.split(',').map(s => s.toUpperCase()) };

            const itemCount = await FamilyItemConfigurationMaster.countDocuments(qry);
            let query = FamilyItemConfigurationMaster.find(qry).sort({ createdAt: -1 });

            if (getAllRecords !== 'true') {
                query = query.skip(skip).limit(limitNumber);
            }

            const items = await query.exec();
            if (!items.length) return res.warn('', req.__('NO_FAMILY_CONFIG_FOUND'));

            finalResponse.items = items;
            finalResponse.count = itemCount;
            return res.success(finalResponse);
        } catch (error) {
            console.error('List error:', error);
            return res.serverError(error.message);
        }
    }

    async deleteAllFamilyItemConfigurations(req, res) {
        try {
            const result = await FamilyItemConfigurationMaster.deleteMany({});
            return res.success(
                { deletedCount: result.deletedCount },
                req.__('ALL_FAMILY_ITEM_CONFIGURATIONS_DELETED_SUCCESSFULLY')
            );
        } catch (error) {
            console.error('Delete error:', error);
            return res.serverError(error.message, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Helper method to process data and create missing filter records
    async processAndCreateFilterRecords(data, brandMap, seriesMap, categoryMap, familyMap, filename) {
        const newBrands = [];
        const newSeries = [];
        const newCategories = [];
        const newFamilies = [];
        const processedData = [];

        // Collect all unique filter values that need to be created
        const uniqueBrands = new Set();
        const uniqueSeries = new Set();
        const uniqueCategories = new Set();
        const uniqueFamilies = new Set();

        for (const item of data) {
            const brandUpper = item.brand?.toUpperCase();
            const seriesUpper = item.series?.toUpperCase();
            const categoryUpper = item.category?.toUpperCase();
            const familyUpper = item.family?.toUpperCase();

            if (brandUpper && !brandMap.has(brandUpper)) uniqueBrands.add(brandUpper);
            if (seriesUpper && !seriesMap.has(seriesUpper)) uniqueSeries.add(seriesUpper);
            if (categoryUpper && !categoryMap.has(categoryUpper)) uniqueCategories.add(categoryUpper);
            if (familyUpper && !familyMap.has(familyUpper)) uniqueFamilies.add(familyUpper);
        }

        // Create missing brand records
        if (uniqueBrands.size > 0) {
            for (const brandName of uniqueBrands) {
                const newBrand = {
                    name: brandName,
                    displayName: brandName,
                    isActive: true,
                    isDelete: false
                };
                newBrands.push(newBrand);
            }

            if (newBrands.length > 0) {
                const createdBrands = await BrandFilter.insertMany(newBrands);
                createdBrands.forEach(brand => {
                    brandMap.set(brand.name.toUpperCase(), brand);
                });
            }
        }

        // Create missing series records
        if (uniqueSeries.size > 0) {
            for (const seriesName of uniqueSeries) {
                const newSeriesRecord = {
                    name: seriesName,
                    displayName: seriesName,
                    brands: [],
                    isActive: true,
                    isDelete: false,
                    sortOrder: 0
                };
                newSeries.push(newSeriesRecord);
            }

            if (newSeries.length > 0) {
                const createdSeries = await SeriesFilter.insertMany(newSeries);
                createdSeries.forEach(series => {
                    seriesMap.set(series.name.toUpperCase(), series);
                });
            }
        }

        // Create missing category records
        if (uniqueCategories.size > 0) {
            for (const categoryName of uniqueCategories) {
                const newCategory = {
                    name: categoryName,
                    slug: categoryName.toLowerCase().replace(/\s+/g, '-'),
                    isDeleted: false,
                    isSuspended: false
                };
                newCategories.push(newCategory);
            }

            if (newCategories.length > 0) {
                const createdCategories = await Category.insertMany(newCategories);
                createdCategories.forEach(category => {
                    categoryMap.set(category.name.toUpperCase(), category);
                });
            }
        }

        // Create missing family records
        if (uniqueFamilies.size > 0) {
            for (const familyName of uniqueFamilies) {
                const newFamily = {
                    name: familyName,
                    displayName: familyName,
                    brands: [],
                    isActive: true,
                    isDelete: false,
                    sortOrder: 0
                };
                newFamilies.push(newFamily);
            }

            if (newFamilies.length > 0) {
                const createdFamilies = await FamilyFilter.insertMany(newFamilies);
                createdFamilies.forEach(family => {
                    familyMap.set(family.name.toUpperCase(), family);
                });
            }
        }

        // Process each item and create the final data structure
        for (const item of data) {
            const brandUpper = item.brand?.toUpperCase();
            const seriesUpper = item.series?.toUpperCase();
            const categoryUpper = item.category?.toUpperCase();
            const familyUpper = item.family?.toUpperCase();

            const brandRecord = brandMap.get(brandUpper);
            const seriesRecord = seriesMap.get(seriesUpper);
            const categoryRecord = categoryMap.get(categoryUpper);
            const familyRecord = familyMap.get(familyUpper);

            const processedItem = {
                productFamilyCode: item.productFamilyCode,
                productFamilyDescription: item.productFamilyDescription || '',
                brand: {
                    id: brandRecord?._id || null,
                    name: brandRecord?.name || item.brand || ''
                },
                series: {
                    id: seriesRecord?._id || null,
                    name: seriesRecord?.name || item.series || ''
                },
                category: {
                    id: categoryRecord?._id || null,
                    name: categoryRecord?.name || item.category || ''
                },
                family: {
                    id: familyRecord?._id || null,
                    name: familyRecord?.name || item.family || ''
                },
                associatedProducts: item.associatedProducts || [],
                similarProducts: item.similarProducts || [],
                fileName: filename || '',
                isActive: true,
                isDeleted: false
            };

            processedData.push(processedItem);
        }

        return processedData;
    }
}
module.exports = new ProductController();
