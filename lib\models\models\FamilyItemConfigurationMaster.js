const mongoose = require('mongoose');

const FamilyItemConfigurationSchema = new mongoose.Schema(
    {
        productFamilyCode: {
            type: String,
            required: true,
            unique: true,
            trim: true,
            uppercase: true
        },
        productFamilyDescription: {
            type: String,
            default: '',
            trim: true
        },
        brand: {
            id: { type: mongoose.Schema.Types.ObjectId, ref: 'BrandFilter', default: null },
            name: {
                type: String,
                default: '',
                trim: true,
                uppercase: true
            },
        },
        series: {
            id: { type: mongoose.Schema.Types.ObjectId, ref: 'SeriesFilter', default: null },
            name: {
                type: String,
                default: '',
                trim: true,
                uppercase: true
            },
        },
        category: {
            id: { type: mongoose.Schema.Types.ObjectId, ref: 'Category', default: null },
            name: {
                type: String,
                default: '',
                trim: true,
                uppercase: true
            },
        },
        family: {
            id: { type: mongoose.Schema.Types.ObjectId, ref: 'FamilyFilter', default: null },
            name: {
                type: String,
                default: '',
                trim: true,
                uppercase: true
            },
        },
        associatedProducts: {
            type: [String],
            default: [],
            validate: {
                validator: function(arr) {
                    return arr.every(product => typeof product === 'string' && product.trim().length > 0);
                },
                message: 'All associated products must be non-empty strings'
            }
        },
        similarProducts: {
            type: [String],
            default: [],
            validate: {
                validator: function(arr) {
                    return arr.every(product => typeof product === 'string' && product.trim().length > 0);
                },
                message: 'All similar products must be non-empty strings'
            }
        },
        fileName: {
            type: String,
            default: '',
            trim: true
        },
        isActive: { type: Boolean, default: true },
        isDeleted: { type: Boolean, default: false },
    },
    {
        timestamps: true,
        // Add collection options for better performance
        collection: 'familyitemconfigurations'
    }
);

// Primary index for unique constraint
FamilyItemConfigurationSchema.index({ productFamilyCode: 1 }, { unique: true });

// Compound indexes for common query patterns
FamilyItemConfigurationSchema.index({
    'brand.name': 1,
    'series.name': 1,
    'category.name': 1,
    'family.name': 1,
    isDeleted: 1,
    isActive: 1
});

// Individual indexes for filter queries
FamilyItemConfigurationSchema.index({ 'brand.name': 1, isDeleted: 1, isActive: 1 });
FamilyItemConfigurationSchema.index({ 'series.name': 1, isDeleted: 1, isActive: 1 });
FamilyItemConfigurationSchema.index({ 'category.name': 1, isDeleted: 1, isActive: 1 });
FamilyItemConfigurationSchema.index({ 'family.name': 1, isDeleted: 1, isActive: 1 });

// Index for soft delete and active status queries
FamilyItemConfigurationSchema.index({ isDeleted: 1, isActive: 1, createdAt: -1 });

// Index for filename queries (for tracking imports)
FamilyItemConfigurationSchema.index({ fileName: 1, createdAt: -1 });

// Text index for search functionality
FamilyItemConfigurationSchema.index({
    productFamilyCode: 'text',
    productFamilyDescription: 'text',
    'brand.name': 'text',
    'series.name': 'text',
    'category.name': 'text',
    'family.name': 'text'
});

// Add pre-save middleware to ensure data consistency
FamilyItemConfigurationSchema.pre('save', function(next) {
    // Ensure productFamilyCode is uppercase and trimmed
    if (this.productFamilyCode) {
        this.productFamilyCode = this.productFamilyCode.trim().toUpperCase();
    }

    // Ensure filter names are uppercase and trimmed
    if (this.brand && this.brand.name) {
        this.brand.name = this.brand.name.trim().toUpperCase();
    }
    if (this.series && this.series.name) {
        this.series.name = this.series.name.trim().toUpperCase();
    }
    if (this.category && this.category.name) {
        this.category.name = this.category.name.trim().toUpperCase();
    }
    if (this.family && this.family.name) {
        this.family.name = this.family.name.trim().toUpperCase();
    }

    next();
});

// Add instance methods for common operations
FamilyItemConfigurationSchema.methods.softDelete = function() {
    this.isDeleted = true;
    this.isActive = false;
    return this.save();
};

FamilyItemConfigurationSchema.methods.restore = function() {
    this.isDeleted = false;
    this.isActive = true;
    return this.save();
};

// Add static methods for common queries
FamilyItemConfigurationSchema.statics.findActive = function() {
    return this.find({ isDeleted: false, isActive: true });
};

FamilyItemConfigurationSchema.statics.findByFilters = function(filters) {
    const query = { isDeleted: false };

    if (filters.brand) query['brand.name'] = { $in: Array.isArray(filters.brand) ? filters.brand : [filters.brand] };
    if (filters.series) query['series.name'] = { $in: Array.isArray(filters.series) ? filters.series : [filters.series] };
    if (filters.category) query['category.name'] = { $in: Array.isArray(filters.category) ? filters.category : [filters.category] };
    if (filters.family) query['family.name'] = { $in: Array.isArray(filters.family) ? filters.family : [filters.family] };
    if (filters.isActive !== undefined) query.isActive = filters.isActive;

    return this.find(query);
};

module.exports = mongoose.model('FamilyItemConfigurationMaster', FamilyItemConfigurationSchema);
