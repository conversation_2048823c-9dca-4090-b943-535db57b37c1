const mongoose = require('mongoose');

// Main Product Filter Schema
const SeriesFilterSchema = new mongoose.Schema({
    name: {
        type: String,
        default: '',
    },
    imageUrl: [{
        type: String,
        default: '',
    }],
    displayName: {
        type: String,
        default: '',
    },
    brands: [
        {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'BrandFilter'
        }
    ],
    isActive: {
        type: Boolean,
        default: true,
    },
    isDelete: {
        type: Boolean,
        default: false,
    },
    sortOrder: {
        type: Number,
        default: 0,
    },
});

module.exports = mongoose.model('SeriesFilter', SeriesFilterSchema);
