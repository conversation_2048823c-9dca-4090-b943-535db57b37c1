const {
    models: {
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON>F<PERSON>er,
        <PERSON><PERSON><PERSON>er,
        FamilyFilter,
        LightColorsFilter,
        OutputLumensFixtureFilter,
        OutputLumensFootFilter,
        OutputWattsFixtureFilter,
        OutputWattsFootFilter,
        MaterialFilter,
        CollectionFilter,
    },
} = require('../../../../lib/models');

class FiltersController {
    /*************************************** BRAND FILTER APIs START*************************************/
    async listBrandFilters(req, res) {
        const { id, isDelete, isActive } = req.query; // Extracting query params

        try {
            let query = {};

            // If a specific brandId is provided, fetch that brand
            if (id) {
                const brand = await BrandFilter.findById(id);
                if (!brand) {
                    return res.warn({}, req.__('Brand not found'));
                }
                return res.success(brand, req.__('Brand filter fetched successfully'));
            }

            // Apply filters for isDelete and isActive if provided
            if (isDelete !== undefined) {
                if (isDelete === 'true') query.isDelete = true;
                else if (isDelete === 'false') query.isDelete = false;
            }

            if (isActive !== undefined) {
                if (isActive === 'true') query.isActive = true;
                else if (isActive === 'false') query.isActive = false;
            }

            // Fetch all brand filters based on the query
            const brands = await BrandFilter.find(query);

            // Send the list of brand filters
            return res.success(brands, req.__('Brand filters fetched successfully'));
        } catch (error) {
            console.error('Error listing brand filters:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async addBrandFilter(req, res) {
        const { brandName,displayName, logo } = req.body; // Extracting the brand name from request body

        try {
            // Validate if the brand name exists in the request
            if (!brandName || brandName.trim() === '') {
                return res.warn({}, req.__('Brand name is required'));
            }

            // Check if the brand already exists in the database
            const existingBrand = await BrandFilter.findOne({ name: brandName, logo });
            if (existingBrand) {
                return res.warn({}, req.__('Brand already exists'));
            }

            // Create a new brand filter and save it to the database
            const newBrand = new BrandFilter({
                name: brandName,
                displayName,
                logo,
            });

            await newBrand.save();

            // Send success response
            return res.success(newBrand, req.__('Brand filter added successfully'));
        } catch (error) {
            console.error('Error adding brand filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async editBrandFilter(req, res) {
        const { id } = req.params;
        const { brandName, logo, isActive, displayName } = req.body;

        try {
            // Validate if the brand ID and name are provided
            if (!id || !brandName || brandName.trim() === '') {
                return res.warn({}, req.__('Brand ID and name are required'));
            }

            // Check if the brand exists in the database
            const existingBrand = await BrandFilter.findById(id);
            if (!existingBrand) {
                return res.warn({}, req.__('Brand not found'));
            }

            // Update the brand name
            existingBrand.name = brandName;
            existingBrand.logo = logo;
            existingBrand.displayName = displayName;
            existingBrand.isActive = isActive;

            // Save the updated brand
            await existingBrand.save();

            // Send success response
            return res.success(existingBrand, req.__('Brand filter updated successfully'));
        } catch (error) {
            console.error('Error editing brand filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async deleteBrandFilter(req, res) {
        const { id } = req.params; // Extracting brand ID from the request body

        try {
            // Validate if the brand ID is provided
            if (!id) {
                return res.warn({}, req.__('Brand ID is required'));
            }

            // Check if the brand exists in the database
            const existingBrand = await BrandFilter.findById(id);
            if (!existingBrand) {
                return res.warn({}, req.__('Brand not found'));
            }

            // Mark the brand as deleted (soft delete)
            existingBrand.isDelete = true;

            // Save the updated brand
            await existingBrand.save();

            // Send success response
            return res.success(existingBrand, req.__('Brand filter deleted successfully'));
        } catch (error) {
            console.error('Error deleting brand filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    /*************************************** BRAND FILTER APIs END*************************************/

    /*************************************** TYPE FILTER APIs START*************************************/
    async listTypeFilters(req, res) {
        const {
            id,
            isDelete,
            isActive,
            sortBy = 'createdAt', // Field to sort by, default is 'createdAt'
            sortOrder = 'desc', // Sorting order, default is 'desc'
            brandId
        } = req.query; // Extracting query params

        try {
            let query = {};

            // If a specific brandId is provided, fetch that brand
            if (id) {
                const type = await TypeFilter.findById(id);
                if (!type) {
                    return res.warn({}, req.__('Type not found'));
                }
                return res.success(type, req.__('Type filter fetched successfully'));
            }

            // Apply filters for isDelete and isActive if provided
            if (isDelete !== undefined) {
                if (isDelete === 'true') query.isDelete = true;
                else if (isDelete === 'false') query.isDelete = false;
            }

            if (isActive !== undefined) {
                if (isActive === 'true') query.isActive = true;
                else if (isActive === 'false') query.isActive = false;
            }

            if (brandId) {
                query.brands = { $in: brandId.split(',') }; // Check if brandId exists in the brands array
            }

            const sortOptions = {};
            sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1; // 1 for ascending, -1 for descending

            // Fetch all brand filters based on the query
            const brands = await TypeFilter.find(query).sort(sortOptions);

            // Send the list of brand filters
            return res.success(brands, req.__('Type filters fetched successfully'));
        } catch (error) {
            console.error('Error listing types filters:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async addTypeFilter(req, res) {
        const { typename, displayName, sortOrder, brands } = req.body; // Extracting the brand name from request body

        try {
            // Validate if the brand name exists in the request
            if (!typename || typename.trim() === '') {
                return res.warn({}, req.__('Type name is required'));
            }

            // Check if the brand already exists in the database
            const existingBrand = await TypeFilter.findOne({ name: typename });
            if (existingBrand) {
                return res.warn({}, req.__('Type already exists'));
            }
            const orderCount = await TypeFilter.countDocuments({ isDelete: false });
            // Create a new brand filter and save it to the database
            const newBrand = new TypeFilter({
                name: typename,
                displayName,
                brands,
                sortOrder: sortOrder ? sortOrder : orderCount + 1,
            });

            await newBrand.save();

            // Send success response
            return res.success(newBrand, req.__('Type filter added successfully'));
        } catch (error) {
            console.error('Error adding Type filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async editTypeFilter(req, res) {
        const { id } = req.params;
        const { typename, isActive, displayName, sortOrder,brands } = req.body;

        try {
            // Validate if the brand ID and name are provided
            if (!id || !typename || typename.trim() === '') {
                return res.warn({}, req.__('Type ID and name are required'));
            }

            // Check if the brand exists in the database
            const existingBrand = await TypeFilter.findById(id);
            if (!existingBrand) {
                return res.warn({}, req.__('Type not found'));
            }

            // Update the brand name
            existingBrand.name = typename;
            existingBrand.displayName = displayName;
            existingBrand.sortOrder = sortOrder;
            existingBrand.brands = brands;
            existingBrand.isActive = isActive;

            // Save the updated brand
            await existingBrand.save();

            // Send success response
            return res.success(existingBrand, req.__('Type filter updated successfully'));
        } catch (error) {
            console.error('Error editing Type filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async deleteTypeFilter(req, res) {
        const { id } = req.params; // Extracting brand ID from the request body

        try {
            // Validate if the brand ID is provided
            if (!id) {
                return res.warn({}, req.__('Type ID is required'));
            }

            // Check if the brand exists in the database
            const existingBrand = await TypeFilter.findById(id);
            if (!existingBrand) {
                return res.warn({}, req.__('Type not found'));
            }

            // Mark the brand as deleted (soft delete)
            existingBrand.isDelete = true;

            // Save the updated brand
            await existingBrand.save();

            // Send success response
            return res.success(existingBrand, req.__('Type filter deleted successfully'));
        } catch (error) {
            console.error('Error deleting Type filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    /*************************************** TYPE FILTER APIs END*************************************/

    /*************************************** SERIES FILTER APIs START ***********************************/
    async listSeriesFilters(req, res) {
        const {
            id,
            brandId,
            isDelete,
            isActive,
            sortBy = 'createdAt', // Field to sort by, default is 'createdAt'
            sortOrder = 'desc', // Sorting order, default is 'desc'
            imageUrl
        } = req.query; // Extracting query params

        try {
            let query = {};

            // If a specific series ID is provided, fetch that series
            if (id) {
                const series = await SeriesFilter.findById(id);
                if (!series) {
                    return res.warn({}, req.__('Series not found'));
                }
                return res.success(series, req.__('Series filter fetched successfully'));
            }

            // Apply filters for isDelete and isActive if provided
            if (isDelete !== undefined) {
                if (isDelete === 'true') query.isDelete = true;
                else if (isDelete === 'false') query.isDelete = false;
            }

            if (isActive !== undefined) {
                if (isActive === 'true') query.isActive = true;
                else if (isActive === 'false') query.isActive = false;
            }
            if (brandId) {
                query.brands = { $in: brandId.split(',') }; // Check if brandId exists in the brands array
            }
           
            const sortOptions = {};
            sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1; // 1 for ascending, -1 for descending
            // Fetch all series filters based on the query
            const seriesList = await SeriesFilter.find(query).sort(sortOptions);

            // Send the list of series filters
            return res.success(seriesList, req.__('Series filters fetched successfully'));
        } catch (error) {
            console.error('Error listing series filters:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // API to add a new series filter
    async addSeriesFilter(req, res) {
        const { seriesName, brands, displayName, sortOrder, imageUrl } = req.body;
    
        try {
            if (!seriesName || seriesName.trim() === '') {
                return res.warn({}, req.__('Series name is required'));
            }
    
            // Check if series already exists
            let existingSeries = await SeriesFilter.findOne({ name: seriesName });
    
            if (existingSeries) {
                // If series exists, update its isDelete and isActive fields
                existingSeries.isDelete = false;
                existingSeries.isActive = true;
                await existingSeries.save();
    
                return res.success(existingSeries, req.__('Series filter already existed and reactivated successfully'));
            }
    
            // If new series, create it
            const orderCount = await SeriesFilter.countDocuments({ isDelete: false });
    
            const newSeries = new SeriesFilter({
                name: seriesName,
                brands,
                displayName,
                sortOrder: sortOrder ? sortOrder : orderCount + 1,
                imageUrl: Array.isArray(imageUrl) ? imageUrl : (imageUrl ? [imageUrl] : []), // Proper imageUrl handling
                isActive: true,
                isDelete: false
            });
    
            await newSeries.save();
    
            return res.success(newSeries, req.__('Series filter added successfully'));
        } catch (error) {
            console.error('Error adding series filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    

    // API to edit an existing series filter
    async editSeriesFilter(req, res) {
        const { id } = req.params; 
        const { seriesName, isActive, brands, displayName, sortOrder, imageUrl } = req.body;
    
        try {
            if (!id || !seriesName || seriesName.trim() === '') {
                return res.warn({}, req.__('Series ID and name are required'));
            }
    
            const existingSeries = await SeriesFilter.findById(id);
            if (!existingSeries) {
                return res.warn({}, req.__('Series not found'));
            }
    
            existingSeries.name = seriesName;
            existingSeries.isActive = isActive;
            existingSeries.brands = brands;
            existingSeries.displayName = displayName;
            existingSeries.sortOrder = sortOrder;
    
            // Handle imageUrl update properly
            if (imageUrl) {
                if (Array.isArray(imageUrl)) {
                    existingSeries.imageUrl = imageUrl; // if it's already an array
                } else {
                    existingSeries.imageUrl = [imageUrl]; // if it's a single string, wrap it in an array
                }
            }
    
            await existingSeries.save();
    
            return res.success(existingSeries, req.__('Series filter updated successfully'));
        } catch (error) {
            console.error('Error editing series filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    

    // API to delete a series filter (soft delete)
    async deleteSeriesFilter(req, res) {
        const { id } = req.params; // Extracting series ID from the request body

        try {
            // Validate if the series ID is provided
            if (!id) {
                return res.warn({}, req.__('Series ID is required'));
            }

            // Check if the series exists in the database
            const existingSeries = await SeriesFilter.findById(id);
            if (!existingSeries) {
                return res.warn({}, req.__('Series not found'));
            }

            // Mark the series as deleted (soft delete)
            existingSeries.isDelete = true;

            // Save the updated series
            await existingSeries.save();

            // Send success response
            return res.success(existingSeries, req.__('Series filter deleted successfully'));
        } catch (error) {
            console.error('Error deleting series filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /*************************************** SERIES FILTER APIs END *************************************/

    /*************************************** APPLICATION FILTER APIs START ***********************************/
    // List Application Filters
    async listApplicationFilters(req, res) {
        const {
            id,
            isDelete,
            isActive,
            sortBy = 'createdAt', // Field to sort by, default is 'createdAt'
            sortOrder = 'desc', // Sorting order, default is 'desc'
        } = req.query;

        try {
            let query = {};

            if (id) {
                const application = await ApplicationFilter.findById(id);
                if (!application) {
                    return res.warn({}, req.__('Application not found'));
                }
                return res.success(application, req.__('Application filter fetched successfully'));
            }

            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }
            const sortOptions = {};
            sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1; // 1 for ascending, -1 for descending
            const applications = await ApplicationFilter.find(query).sort(sortOptions);
            return res.success(applications, req.__('Application filters fetched successfully'));
        } catch (error) {
            console.error('Error listing application filters:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add Application Filter
    async addApplicationFilter(req, res) {
        const { name, brands, displayName, sortOrder } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Application name is required'));
            }

            // Check if application already exists
            const existingApplication = await ApplicationFilter.findOne({ name });

            if (existingApplication) {
                if (!existingApplication.isDelete) {
                    return res.warn({}, req.__('Application already exists'));
                } else {
                    // Update the existing deleted application
                    existingApplication.isDelete = false;
                    existingApplication.isActive = true;
                    existingApplication.brands = brands || existingApplication.brands;
                    existingApplication.displayName = displayName || existingApplication.displayName;
                    existingApplication.sortOrder = sortOrder || existingApplication.sortOrder;

                    await existingApplication.save();
                    return res.success(existingApplication, req.__('Application filter updated successfully'));
                }
            }

            // Count total non-deleted applications for sorting
            const orderCount = await ApplicationFilter.countDocuments({ isDelete: false });

            // Create a new application filter
            const newApplication = new ApplicationFilter({
                name,
                brands,
                displayName,
                sortOrder: sortOrder ? sortOrder : orderCount + 1,
                isDelete: false,
                isActive: true,
            });

            await newApplication.save();
            return res.success(newApplication, req.__('Application filter added successfully'));
        } catch (error) {
            console.error('Error adding application filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Application Filter
    async editApplicationFilter(req, res) {
        const { id } = req.params;
        const { name, isActive, displayName, sortOrder } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Application name is required'));
            }

            const application = await ApplicationFilter.findById(id);
            if (!application) {
                return res.warn({}, req.__('Application not found'));
            }

            application.name = name;
            application.isActive = isActive;
            application.displayName = displayName;
            application.sortOrder = sortOrder;
            await application.save();

            return res.success(application, req.__('Application filter updated successfully'));
        } catch (error) {
            console.error('Error editing application filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Application Filter (Soft Delete)
    async deleteApplicationFilter(req, res) {
        const { id } = req.params;

        try {
            const application = await ApplicationFilter.findById(id);
            if (!application) {
                return res.warn({}, req.__('Application not found'));
            }

            application.isDelete = true;
            await application.save();

            return res.success(application, req.__('Application filter deleted successfully'));
        } catch (error) {
            console.error('Error deleting application filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /*************************************** APPLICATION FILTER APIs END *************************************/

    /*************************************** CATEGORY FILTER APIs START ***********************************/

    // List Category Filters
    async listCategoryFilters(req, res) {
        const {
            id,
            brandId,
            isDelete,
            isActive,
            sortBy = 'createdAt', // Field to sort by, default is 'createdAt'
            sortOrder = 'desc', // Sorting order, default is 'desc'
        } = req.query;
        try {
            let query = {};

            if (id) {
                const filterData = await CategoryFilter.findById(id);
                if (!filterData) {
                    return res.warn({}, req.__('Category not found'));
                }
                return res.success(filterData, req.__('Category filter fetched successfully'));
            }

            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }
            if (brandId) {
                query.brands = { $in: brandId.split(',') }; // Check if brandId exists in the brands array
            }
            const sortOptions = {};
            sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1; // 1 for ascending, -1 for descending
            const filterDataList = await CategoryFilter.find(query).sort(sortOptions);
            return res.success(filterDataList, req.__('filters fetched successfully'));
        } catch (error) {
            console.error('Error listing filters:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add Category Filter
    async addCategoryFilter(req, res) {
        const { name, brands, displayName, sortOrder } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            // Check if category filter already exists
            const existingFilter = await CategoryFilter.findOne({ name });

            if (existingFilter) {
                if (!existingFilter.isDelete) {
                    return res.warn({}, req.__('Filter already exists'));
                } else {
                    // Update the existing deleted filter
                    existingFilter.isDelete = false;
                    existingFilter.isActive = true;
                    existingFilter.brands = brands || existingFilter.brands;
                    existingFilter.displayName = displayName || existingFilter.displayName;
                    existingFilter.sortOrder = sortOrder || existingFilter.sortOrder;

                    await existingFilter.save();
                    return res.success(existingFilter, req.__('Filter updated successfully'));
                }
            }

            // Count total non-deleted filters for sorting
            const orderCount = await CategoryFilter.countDocuments({ isDelete: false });

            // Create a new category filter
            const newFilter = new CategoryFilter({
                name,
                brands,
                displayName,
                sortOrder: sortOrder ? sortOrder : orderCount + 1,
                isDelete: false,
                isActive: true,
            });

            await newFilter.save();
            return res.success(newFilter, req.__('Filter added successfully'));
        } catch (error) {
            console.error('Error adding category filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Category Filter
    async editCategoryFilter(req, res) {
        const { id } = req.params;
        const { name, isActive, brands, displayName, sortOrder } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            const filterData = await CategoryFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.name = name;
            filterData.isActive = isActive;
            filterData.brands = brands;
            filterData.displayName = displayName;
            filterData.sortOrder = sortOrder;
            await filterData.save();

            return res.success(filterData, req.__('filter updated successfully'));
        } catch (error) {
            console.error('Error editing filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Category Filter (Soft Delete)
    async deleteCategoryFilter(req, res) {
        const { id } = req.params;

        try {
            const filterData = await CategoryFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.isDelete = true;
            await filterData.save();

            return res.success(filterData, req.__('filter deleted successfully'));
        } catch (error) {
            console.error('Error deleting filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /*************************************** CATEGORY FILTER APIs END *************************************/

    /*************************************** FAMILY FILTER APIs START ***********************************/
    // List Family Filters
    async listFamilyFilters(req, res) {
        const {
            id,
            isDelete,
            isActive,
            brandId,
            sortBy = 'createdAt', // Field to sort by, default is 'createdAt'
            sortOrder = 'desc', // Sorting order, default is 'desc'
        } = req.query;
        try {
            let query = {};

            // Filter by Family Filter ID
            if (id) {
                const filterData = await FamilyFilter.findById(id);
                if (!filterData) {
                    return res.warn({}, req.__('Family Filter not found'));
                }
                return res.success(filterData, req.__('Family filter fetched successfully'));
            }

            // Filter by isDelete flag
            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            // Filter by isActive flag
            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            // Filter by Brand ID (if provided)
            if (brandId) {
                query.brands = { $in: brandId.split(',') }; // Check if brandId exists in the brands array
            }
            const sortOptions = {};
            sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1; // 1 for ascending, -1 for descending
            const filterDataList = await FamilyFilter.find(query).sort(sortOptions);
            return res.success(filterDataList, req.__('Filters fetched successfully'));
        } catch (error) {
            console.error('Error listing family filters:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add Family Filter
    async addFamilyFilter(req, res) {
        const { name, brands, displayName, sortOrder } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            // Check if the family filter already exists
            const existingFilter = await FamilyFilter.findOne({ name });

            if (existingFilter) {
                if (!existingFilter.isDelete) {
                    return res.warn({}, req.__('Filter already exists'));
                } else {
                    // Reactivate the deleted filter
                    existingFilter.isDelete = false;
                    existingFilter.isActive = true;
                    existingFilter.brands = brands || existingFilter.brands;
                    existingFilter.displayName = displayName || existingFilter.displayName;
                    existingFilter.sortOrder = sortOrder || existingFilter.sortOrder;

                    await existingFilter.save();
                    return res.success(existingFilter, req.__('Filter updated successfully'));
                }
            }

            // Count total non-deleted filters for sorting
            const orderCount = await FamilyFilter.countDocuments({ isDelete: false });

            // Create a new family filter
            const newFilter = new FamilyFilter({
                name,
                brands,
                displayName,
                sortOrder: sortOrder ? sortOrder : orderCount + 1,
                isDelete: false,
                isActive: true,
            });

            await newFilter.save();
            return res.success(newFilter, req.__('Filter added successfully'));
        } catch (error) {
            console.error('Error adding family filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Family Filter
    async editFamilyFilter(req, res) {
        const { id } = req.params;
        const { name, brands, isActive, displayName, sortOrder } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            const filterData = await FamilyFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.name = name;
            filterData.brands = brands;
            filterData.isActive = isActive;
            filterData.displayName = displayName;
            filterData.sortOrder = sortOrder;
            await filterData.save();

            return res.success(filterData, req.__('Filter updated successfully'));
        } catch (error) {
            console.error('Error editing family filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Family Filter (Soft Delete)
    async deleteFamilyFilter(req, res) {
        const { id } = req.params;

        try {
            const filterData = await FamilyFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.isDelete = true;
            await filterData.save();

            return res.success(filterData, req.__('Filter deleted successfully'));
        } catch (error) {
            console.error('Error deleting family filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    /*************************************** FAMILY FILTER APIs END *************************************/

    /*************************************** LIGHT FILTER APIs START ***********************************/
    // List Light Colors Filters
    async listLightColorsFilters(req, res) {
        const { id, isDelete, isActive } = req.query;
        try {
            let query = {};

            if (id) {
                const filterData = await LightColorsFilter.findById(id);
                if (!filterData) {
                    return res.warn({}, req.__('Light Colors Filter not found'));
                }
                return res.success(filterData, req.__('Light colors filter fetched successfully'));
            }

            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            const filterDataList = await LightColorsFilter.find(query);
            return res.success(filterDataList, req.__('Filters fetched successfully'));
        } catch (error) {
            console.error('Error listing light colors filters:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add Light Colors Filter
    async addLightColorsFilter(req, res) {
        const { name, displayName, sortOrder } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            // Check if filter exists (both active and deleted)
            const existingFilter = await LightColorsFilter.findOne({ name });

            if (existingFilter) {
                if (!existingFilter.isDelete) {
                    return res.warn({}, req.__('Filter already exists'));
                } else {
                    // Reactivate the deleted filter
                    existingFilter.displayName = displayName;
                    existingFilter.sortOrder = sortOrder;
                    existingFilter.isDelete = false;
                    existingFilter.isActive = true;
                    await existingFilter.save();

                    return res.success(existingFilter, req.__('Filter reactivated successfully'));
                }
            }

            // Create new filter if it doesn't exist
            const newFilter = new LightColorsFilter({
                name,
                displayName,
                sortOrder,
                isDelete: false,
                isActive: true,
            });

            await newFilter.save();
            return res.success(newFilter, req.__('Filter added successfully'));
        } catch (error) {
            console.error('Error adding light colors filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Light Colors Filter
    async editLightColorsFilter(req, res) {
        const { id } = req.params;
        const { name, isActive } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            const filterData = await LightColorsFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.name = name;
            filterData.isActive = isActive;
            await filterData.save();

            return res.success(filterData, req.__('Filter updated successfully'));
        } catch (error) {
            console.error('Error editing light colors filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Light Colors Filter (Soft Delete)
    async deleteLightColorsFilter(req, res) {
        const { id } = req.params;

        try {
            const filterData = await LightColorsFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.isDelete = true;
            await filterData.save();

            return res.success(filterData, req.__('Filter deleted successfully'));
        } catch (error) {
            console.error('Error deleting light colors filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /*************************************** LIGHT FILTER APIs END *************************************/

    /*************************************** OUTPUT LUMENS FIXTURE FILTER APIs START ***********************************/

    // List Output Lumens Fixture Filters
    async listOutputLumensFixtureFilters(req, res) {
        const { id, isDelete, isActive } = req.query;
        try {
            let query = {};

            if (id) {
                const filterData = await OutputLumensFixtureFilter.findById(id);
                if (!filterData) {
                    return res.warn({}, req.__('Output Lumens Fixture Filter not found'));
                }
                return res.success(filterData, req.__('Output lumens fixture filter fetched successfully'));
            }

            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            const filterDataList = await OutputLumensFixtureFilter.find(query);
            return res.success(filterDataList, req.__('Filters fetched successfully'));
        } catch (error) {
            console.error('Error listing output lumens fixture filters:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add Output Lumens Fixture Filter
    async addOutputLumensFixtureFilter(req, res) {
        const { name, displayName, sortOrder } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            // Check if filter exists (both active and deleted)
            const existingFilter = await OutputLumensFixtureFilter.findOne({ name });

            if (existingFilter) {
                if (!existingFilter.isDelete) {
                    return res.warn({}, req.__('Filter already exists'));
                } else {
                    // Reactivate the deleted filter
                    existingFilter.isDelete = false;
                    existingFilter.isActive = true;
                    existingFilter.displayName = displayName || existingFilter.displayName;
                    existingFilter.sortOrder = sortOrder || existingFilter.sortOrder;
                    await existingFilter.save();

                    return res.success(existingFilter, req.__('Filter reactivated successfully'));
                }
            }

            // Get the current count of non-deleted filters to determine default sortOrder
            const orderCount = await OutputLumensFixtureFilter.countDocuments({ isDelete: false });

            // Create new filter if it doesn't exist
            const newFilter = new OutputLumensFixtureFilter({
                name,
                displayName,
                sortOrder: sortOrder ? sortOrder : orderCount + 1,
                isDelete: false,
                isActive: true,
            });

            await newFilter.save();
            return res.success(newFilter, req.__('Filter added successfully'));
        } catch (error) {
            console.error('Error adding output lumens fixture filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Output Lumens Fixture Filter
    async editOutputLumensFixtureFilter(req, res) {
        const { id } = req.params;
        const { name, isActive } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            const filterData = await OutputLumensFixtureFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.name = name;
            filterData.isActive = isActive;
            await filterData.save();

            return res.success(filterData, req.__('Filter updated successfully'));
        } catch (error) {
            console.error('Error editing output lumens fixture filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Output Lumens Fixture Filter (Soft Delete)
    async deleteOutputLumensFixtureFilter(req, res) {
        const { id } = req.params;

        try {
            const filterData = await OutputLumensFixtureFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.isDelete = true;
            await filterData.save();

            return res.success(filterData, req.__('Filter deleted successfully'));
        } catch (error) {
            console.error('Error deleting output lumens fixture filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /*************************************** OUTPUT LUMENS FIXTURE FILTER APIs END *************************************/

    /*************************************** OUTPUT LUMENS FOOT FILTER APIs START ***********************************/

    // List Output Lumens Foot Filters
    async listOutputLumensFootFilters(req, res) {
        const { id, isDelete, isActive } = req.query;
        try {
            let query = {};

            if (id) {
                const filterData = await OutputLumensFootFilter.findById(id);
                if (!filterData) {
                    return res.warn({}, req.__('Output Lumens Foot Filter not found'));
                }
                return res.success(filterData, req.__('Output lumens foot filter fetched successfully'));
            }

            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            const filterDataList = await OutputLumensFootFilter.find(query);
            return res.success(filterDataList, req.__('Filters fetched successfully'));
        } catch (error) {
            console.error('Error listing output lumens foot filters:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add Output Lumens Foot Filter
    async addOutputLumensFootFilter(req, res) {
        const { name, displayName, sortOrder } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            // Check if filter exists (both active and deleted)
            const existingFilter = await OutputLumensFootFilter.findOne({ name });

            if (existingFilter) {
                if (!existingFilter.isDelete) {
                    return res.warn({}, req.__('Filter already exists'));
                } else {
                    // Reactivate the deleted filter
                    existingFilter.isDelete = false;
                    existingFilter.isActive = true;
                    existingFilter.displayName = displayName || existingFilter.displayName;
                    existingFilter.sortOrder = sortOrder || existingFilter.sortOrder;
                    await existingFilter.save();

                    return res.success(existingFilter, req.__('Filter reactivated successfully'));
                }
            }

            // Get the current count of non-deleted filters to determine default sortOrder
            const orderCount = await OutputLumensFootFilter.countDocuments({ isDelete: false });

            // Create new filter if it doesn't exist
            const newFilter = new OutputLumensFootFilter({
                name,
                displayName,
                sortOrder: sortOrder ? sortOrder : orderCount + 1,
                isDelete: false,
                isActive: true,
            });

            await newFilter.save();
            return res.success(newFilter, req.__('Filter added successfully'));
        } catch (error) {
            console.error('Error adding output lumens foot filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Output Lumens Foot Filter
    async editOutputLumensFootFilter(req, res) {
        const { id } = req.params;
        const { name, isActive } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            const filterData = await OutputLumensFootFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.name = name;
            filterData.isActive = isActive;
            await filterData.save();

            return res.success(filterData, req.__('Filter updated successfully'));
        } catch (error) {
            console.error('Error editing output lumens foot filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Output Lumens Foot Filter (Soft Delete)
    async deleteOutputLumensFootFilter(req, res) {
        const { id } = req.params;

        try {
            const filterData = await OutputLumensFootFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.isDelete = true;
            await filterData.save();

            return res.success(filterData, req.__('Filter deleted successfully'));
        } catch (error) {
            console.error('Error deleting output lumens foot filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /*************************************** OUTPUT LUMENS FOOT FILTER APIs END *************************************/

    /*************************************** OUTPUT WATTS FIXTURE FILTER APIs START ***********************************/

    // List Output Watts Fixture Filters
    async listOutputWattsFixtureFilters(req, res) {
        const { id, isDelete, isActive } = req.query;
        try {
            let query = {};

            if (id) {
                const filterData = await OutputWattsFixtureFilter.findById(id);
                if (!filterData) {
                    return res.warn({}, req.__('Output Watts Fixture Filter not found'));
                }
                return res.success(filterData, req.__('Output watts fixture filter fetched successfully'));
            }

            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            const filterDataList = await OutputWattsFixtureFilter.find(query);
            return res.success(filterDataList, req.__('Filters fetched successfully'));
        } catch (error) {
            console.error('Error listing output watts fixture filters:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add Output Watts Fixture Filter
    async addOutputWattsFixtureFilter(req, res) {
        const { name, displayName, sortOrder } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            // Check if the filter already exists
            const existingFilter = await OutputWattsFixtureFilter.findOne({ name });

            if (existingFilter) {
                if (existingFilter.isDelete) {
                    // Reactivate the existing filter
                    existingFilter.isDelete = false;
                    existingFilter.isActive = true;
                    existingFilter.displayName = displayName || existingFilter.displayName;
                    existingFilter.sortOrder = sortOrder || existingFilter.sortOrder;
                    await existingFilter.save();

                    return res.success(existingFilter, req.__('Filter reactivated successfully'));
                }
                return res.warn({}, req.__('Filter already exists'));
            }

            // Create a new filter
            const newFilter = new OutputWattsFixtureFilter({
                name,
                displayName,
                sortOrder,
            });
            await newFilter.save();

            return res.success(newFilter, req.__('Filter added successfully'));
        } catch (error) {
            console.error('Error adding output watts fixture filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Output Watts Fixture Filter
    async editOutputWattsFixtureFilter(req, res) {
        const { id } = req.params;
        const { name, isActive } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            const filterData = await OutputWattsFixtureFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.name = name;
            filterData.isActive = isActive;
            await filterData.save();

            return res.success(filterData, req.__('Filter updated successfully'));
        } catch (error) {
            console.error('Error editing output watts fixture filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Output Watts Fixture Filter (Soft Delete)
    async deleteOutputWattsFixtureFilter(req, res) {
        const { id } = req.params;

        try {
            const filterData = await OutputWattsFixtureFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.isDelete = true;
            await filterData.save();

            return res.success(filterData, req.__('Filter deleted successfully'));
        } catch (error) {
            console.error('Error deleting output watts fixture filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /*************************************** OUTPUT WATTS FIXTURE FILTER APIs END *************************************/

    /*************************************** OUTPUT WATTS FOOT FILTER APIs START ***********************************/

    // List Output Watts Foot Filters
    async listOutputWattsFootFilters(req, res) {
        const { id, isDelete, isActive } = req.query;
        try {
            let query = {};

            if (id) {
                const filterData = await OutputWattsFootFilter.findById(id);
                if (!filterData) {
                    return res.warn({}, req.__('Filter not found'));
                }
                return res.success(filterData, req.__('Filter fetched successfully'));
            }

            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            const filterDataList = await OutputWattsFootFilter.find(query);
            return res.success(filterDataList, req.__('Filters fetched successfully'));
        } catch (error) {
            console.error('Error listing filters:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add Output Watts Foot Filter
    async addOutputWattsFootFilter(req, res) {
        const { name, displayName, sortOrder } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            // Check if the filter already exists
            const existingFilter = await OutputWattsFootFilter.findOne({ name });

            if (existingFilter) {
                if (existingFilter.isDelete) {
                    // Reactivate the existing filter
                    existingFilter.isDelete = false;
                    existingFilter.isActive = true;
                    existingFilter.displayName = displayName || existingFilter.displayName;
                    existingFilter.sortOrder = sortOrder || existingFilter.sortOrder;
                    await existingFilter.save();

                    return res.success(existingFilter, req.__('Filter reactivated successfully'));
                }
                return res.warn({}, req.__('Filter already exists'));
            }

            // Create a new filter
            const newFilter = new OutputWattsFootFilter({
                name,
                displayName,
                sortOrder,
            });
            await newFilter.save();

            return res.success(newFilter, req.__('Filter added successfully'));
        } catch (error) {
            console.error('Error adding output watts foot filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Output Watts Foot Filter
    async editOutputWattsFootFilter(req, res) {
        const { id } = req.params;
        const { name, isActive } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            const filterData = await OutputWattsFootFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.name = name;
            filterData.isActive = isActive;
            await filterData.save();

            return res.success(filterData, req.__('Filter updated successfully'));
        } catch (error) {
            console.error('Error editing filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Output Watts Foot Filter (Soft Delete)
    async deleteOutputWattsFootFilter(req, res) {
        const { id } = req.params;

        try {
            const filterData = await OutputWattsFootFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.isDelete = true;
            await filterData.save();

            return res.success(filterData, req.__('Filter deleted successfully'));
        } catch (error) {
            console.error('Error deleting filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /*************************************** OUTPUT WATTS FOOT FILTER APIs END *************************************/

    /*************************************** MATERIAL FILTER APIs START ***********************************/

    // List Output Watts Foot Filters
    async listMaterialFilters(req, res) {
        const { id, isDelete, isActive } = req.query;
        try {
            let query = {};

            if (id) {
                const filterData = await MaterialFilter.findById(id);
                if (!filterData) {
                    return res.warn({}, req.__('Filter not found'));
                }
                return res.success(filterData, req.__('Filter fetched successfully'));
            }

            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            const filterDataList = await MaterialFilter.find(query);
            return res.success(filterDataList, req.__('Filters fetched successfully'));
        } catch (error) {
            console.error('Error listing filters:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async addMaterialFilter(req, res) {
        const { name, displayName, sortOrder } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            // Check if a filter with the given name exists
            const existingFilter = await MaterialFilter.findOne({ name });

            if (existingFilter) {
                if (!existingFilter.isDelete) {
                    // If the filter exists and is not deleted, return an error
                    return res.warn({}, req.__('Filter already exists'));
                } else {
                    // If the filter exists and is deleted, update it
                    existingFilter.isDelete = false;
                    existingFilter.isActive = true;
                    existingFilter.displayName = displayName || existingFilter.displayName;
                    existingFilter.sortOrder = sortOrder || existingFilter.sortOrder;

                    await existingFilter.save();

                    return res.success(existingFilter, req.__('Filter restored successfully'));
                }
            }

            // If the filter does not exist, create a new one
            const orderCount = await MaterialFilter.countDocuments({ isDelete: false });

            const newFilter = new MaterialFilter({
                name,
                displayName,
                sortOrder: sortOrder ? sortOrder : orderCount + 1,
                isActive: true,
                isDelete: false,
            });

            await newFilter.save();

            return res.success(newFilter, req.__('Filter added successfully'));
        } catch (error) {
            console.error('Error adding filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Output Watts Foot Filter
    async editMaterialFilter(req, res) {
        const { id } = req.params;
        const { name, displayName, sortOrder, isActive } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            const filterData = await MaterialFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.name = name;
            filterData.displayName = displayName;
            filterData.sortOrder = sortOrder;
            filterData.isActive = isActive ? isActive : filterData.isActive;
            await filterData.save();

            return res.success(filterData, req.__('Filter updated successfully'));
        } catch (error) {
            console.error('Error editing filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Output Watts Foot Filter (Soft Delete)
    async deleteMaterialFilter(req, res) {
        const { id } = req.params;

        try {
            const filterData = await MaterialFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.isDelete = true;
            await filterData.save();

            return res.success(filterData, req.__('Filter deleted successfully'));
        } catch (error) {
            console.error('Error deleting filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /*************************************** MATERIAL FILTER APIs END *************************************/

    /*************************************** COLLECTION FILTER APIs START ***********************************/

    // List Output Watts Foot Filters
    async listCollectionFilters(req, res) {
        const { id, isDelete, isActive } = req.query;
        try {
            let query = {};

            if (id) {
                const filterData = await CollectionFilter.findById(id);
                if (!filterData) {
                    return res.warn({}, req.__('Filter not found'));
                }
                return res.success(filterData, req.__('Filter fetched successfully'));
            }

            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            const filterDataList = await CollectionFilter.find(query);
            return res.success(filterDataList, req.__('Filters fetched successfully'));
        } catch (error) {
            console.error('Error listing filters:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async addCollectionFilter(req, res) {
        const { name, displayName, sortOrder } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            // Check if a filter with the given name exists
            const existingFilter = await CollectionFilter.findOne({ name });

            if (existingFilter) {
                if (!existingFilter.isDelete) {
                    // If the filter exists and is not deleted, return an error
                    return res.warn({}, req.__('Filter already exists'));
                } else {
                    // If the filter exists and is deleted, update it
                    existingFilter.isDelete = false;
                    existingFilter.isActive = true;
                    existingFilter.displayName = displayName || existingFilter.displayName;
                    existingFilter.sortOrder = sortOrder || existingFilter.sortOrder;

                    await existingFilter.save();

                    return res.success(existingFilter, req.__('Filter restored successfully'));
                }
            }

            // If the filter does not exist, create a new one
            const newFilter = new CollectionFilter({
                name,
                displayName,
                sortOrder,
                isActive: true,
                isDelete: false,
            });

            await newFilter.save();

            return res.success(newFilter, req.__('Filter added successfully'));
        } catch (error) {
            console.error('Error adding filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Output Watts Foot Filter
    async editCollectionFilter(req, res) {
        const { id } = req.params;
        const { name, displayName, sortOrder, isActive } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            const filterData = await CollectionFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.name = name;
            filterData.sortOrder = sortOrder;
            filterData.displayName = displayName;
            filterData.isActive = isActive ? isActive : filterData.isActive;
            await filterData.save();

            return res.success(filterData, req.__('Filter updated successfully'));
        } catch (error) {
            console.error('Error editing filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Output Watts Foot Filter (Soft Delete)
    async deleteCollectionFilter(req, res) {
        const { id } = req.params;

        try {
            const filterData = await CollectionFilter.findById(id);
            if (!filterData) {
                return res.warn({}, req.__('Filter not found'));
            }

            filterData.isDelete = true;
            await filterData.save();

            return res.success(filterData, req.__('Filter deleted successfully'));
        } catch (error) {
            console.error('Error deleting filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /*************************************** COLLECTION FILTER APIs END *************************************/
}
module.exports = new FiltersController();
