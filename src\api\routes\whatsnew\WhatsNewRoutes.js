const express = require('express');
const router = express.Router();
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenAdmin } = require('../../util/auth');
const WhatsNewController = require('./WhatsNewController');

router.post('/', verifyTokenAdmin, WhatsNewController.addWhatsNew);
router.post('/carousel', verifyTokenAdmin, WhatsNewController.addWhatsNewCarousel);
router.put('/carousel/:carouselId', verifyTokenAdmin, WhatsNewController.updateWhatsNewCarousel);
router.get('/', WhatsNewController.viewWhatsNew);
router.delete('/carousel/:carouselId', WhatsNewController.deleteCarousel);

module.exports = router;
