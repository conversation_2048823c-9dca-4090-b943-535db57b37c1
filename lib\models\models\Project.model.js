const mongoose = require('mongoose'),
    Schema = mongoose.Schema;

// Schema for project tabs
const ProjectTabsSchema = new Schema({
    banner: [
        {
            mediaType: {
                type: String,
                enum: ['image', 'video'],
                required: true,
            },
            mediaUrl: {
                type: String,
                default: '',
            },
        },
    ],
    description: {
        type: String,
        default: '',
    },
    isActive: {
        type: Boolean,
        default: false,
    },
    tabTitle: {
        type: String,
        default: '',
    },
    sortOrder: {
        type: Number,
        default: 0,
    },
});

// Schema for project detail banner (image or video)
const ProjectDetailBannerSchema = new Schema({
    mediaType: {
        type: String,
        enum: ['image', 'video'],
        required: true,
    },
    fileName: {
        type: String,
        default: '',
    },
    fileTitle: {
        type: String,
        default: '',
    },
});

// Main Project schema
const ProjectSchema = new Schema(
    {
        featuredImage: {
            type: String,
            default: '',
        },
        isFeatured: {
            type: Boolean,
            default: false,
        },
        projectDetailBanner: [ProjectDetailBannerSchema], // Nested schema for project banner
        // Filters - referring to external collections for normalization
        FilterProjectBrand: [
            {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'BrandFilter',
            },
        ],
        FilterProjectType: [
            {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'TypeFilter',
            },
        ],
        FilterProjectApplication: [
            {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'ApplicationFilter',
            },
        ],
        FilterProjectSeries: [
            {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'SeriesFilter',
            },
        ],
        FilterProjectProductFamily: [
            {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'FamilyFilter',
            },
        ],
        ProjectTitle: {
            type: String,
            default: '',
        },
        ProjectDescriptionShort: {
            type: String,
            default: '',
        },
        ProjectDescriptionDetail: {
            type: String,
            default: '',
        },
        // Consolidated social media links into a single object
        SocialMediaLinks: {
            instagram: { type: String, default: '' },
            x: { type: String, default: '' }, // X is the new name for Twitter
            facebook: { type: String, default: '' },
            linkedin: { type: String, default: '' },
        },
        ProjectPictures: [
            {
                type: String,
                default: '',
            },
        ],
        // Array of Project Tabs
        ProjectTabs: [ProjectTabsSchema],
        similarProjects: [
            {
                type: String,
                validate: {
                    validator: function(v) {
                        return /^(http|https):\/\/[^ "]+$/.test(v);
                    },
                    message: props => `${props.value} is not a valid URL`,
                },
            },
        ],
        isActive: {
            type: Boolean,
            default: false,
        },
        sortOrder: {
            type: Number,
            default: 0,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    {
        timestamps: {
            createdAt: 'created',
            updatedAt: 'updated',
        },
        id: false,
        toJSON: {
            getters: true,
        },
        toObject: {
            getters: true,
        },
    }
);

// Pre-save middleware for auto-incrementing sortOrder
ProjectSchema.pre('save', async function(next) {
    if (this.isNew) {
        try {
            const maxSortOrder = await mongoose
                .model('Project')
                .findOne({})
                .sort('-sortOrder')
                .select('sortOrder')
                .exec();

            this.sortOrder = (maxSortOrder ? maxSortOrder.sortOrder : 0) + 1;
        } catch (err) {
            return next(err);
        }
    }
    next();
});

module.exports = mongoose.model('Project', ProjectSchema);
