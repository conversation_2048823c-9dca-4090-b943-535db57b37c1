const mongoose = require('mongoose');

// Beam <PERSON>le Attribute Schema
const ConnectionCableAttributeSchema = new mongoose.Schema({
    name: {
        type: String,
        default: '',
    },
    modelSlug: {
        type: String,
        default: 'concab',
    },
    isActive: {
        type: Boolean,
        default: true,
    },
    isDelete: {
        type: Boolean,
        default: false,
    },
});

module.exports = mongoose.model('ConnectionCableAttribute', ConnectionCableAttributeSchema);
