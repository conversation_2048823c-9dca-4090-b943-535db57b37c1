const mongoose = require('mongoose'),
    Schema = mongoose.Schema,
    ObjectId = mongoose.Types.ObjectId;



const ResourceSchema = new Schema(
    {
        ResourceBannerType: {
            type: String,
            default:"image",
            enum: ['image', 'video'],
        },
        ResourcesBannerTitle: {
            type: String,
            default:""
        },
        ResourcesBannerFileName: {
            type: String,
            default:""
        },
        isTextVisible: {
            type: Boolean,
            default: true,
        },
        pdfDriveUrl:{
            type: String,
            default:"" 
        }
    },
    {
        timestamps: {
            createdAt: 'created',
            updatedAt: 'updated',
        },
        id: false,
        toJSON: {
            getters: true,
        },
        toObject: {
            getters: true,
        },
    }
);

module.exports = mongoose.model('Resource', ResourceSchema);
