const mongoose = require('mongoose'),
    Schema = mongoose.Schema;

const CareerSchema = new Schema(
    {
        CareersBannerType: {
            type: String,
            default: 'image',
            enum: ['image', 'video'],
        },
        CareersBannerTitle: {
            type: String,
            default: '',
        },
        CareersBannerFileName: {
            type: String,
            default: '',
        },
        LifeatSGiTitle: {
            type: String,
            default: '',
        },
        LifeatSGiSummary: {
            type: String,
            default: '',
        },
        LifeatSGiDetail: {
            type: String,
            default: '',
        },
        CareersImage: {
            type: String,
            default: '',
        },
        WeAreHiringText: {
            type: String,
            default: '',
        },
    },
    {
        timestamps: {
            createdAt: 'created',
            updatedAt: 'updated',
        },
        id: false,
        toJSON: {
            getters: true,
        },
        toObject: {
            getters: true,
        },
    }
);

module.exports = mongoose.model('Career', CareerSchema);
