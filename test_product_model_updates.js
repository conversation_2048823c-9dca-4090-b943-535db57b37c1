/**
 * Test script for Product Model Updates
 * Tests the new fields added to the Product model based on JSON comments
 */

const axios = require('axios');

// Test data with all new fields
const testProductData = {
    "sortOrder": "PROD-001",
    "bannerSection": {
        "banner": [
            {
                "mediaType": "image",
                "mediaUrl": "https://example.com/banner1.jpg"
            }
        ],
        "title": "Test Product Family",
        "isTitleVisible": true,
        "familyName": {
            "label": "Product Family",
            "isVisible": true
        },
        "modelsAndSpecs": {
            "label": "Models & Specs",
            "isVisible": true
        },
        "downloads": {
            "label": "Downloads",
            "isVisible": true
        },
        "itemConfigurator": {
            "label": "Item Configurator",
            "isVisible": true
        },
        "viewCatalogue": {
            "label": "View Catalogue",
            "isVisible": true
        }
    },
    "productDetailFamilyRecordInternal": {
        "family": {
            "id": "507f1f77bcf86cd799439011",
            "name": "CLASS<PERSON>"
        },
        "familyCode": "SGI-001-006",
        "brand": {
            "id": "507f1f77bcf86cd799439012",
            "name": "SGI"
        },
        "series": {
            "id": "507f1f77bcf86cd799439013",
            "name": "SPECIFICATION"
        },
        "category": {
            "id": "507f1f77bcf86cd799439014",
            "name": "FLEX"
        },
        "collections": {
            "id": "507f1f77bcf86cd799439015",
            "name": "PREMIUM"
        },
        "material": {
            "id": "507f1f77bcf86cd799439016",
            "name": "ALUMINUM"
        },
        "itemCategory": "LED_STRIP",
        "efficacyType": "HIGH_EFFICIENCY",
        "associatedProducts": [
            "GEN-003-041",
            "GEN-003-042",
            "GEN-003-043"
        ],
        "associatedProductsVisible": true,
        "resourceImage": {
            "mediaType": "image",
            "mediaUrl": "https://example.com/resource.jpg"
        },
        "productTileImage": {
            "mediaType": "image",
            "mediaUrl": "https://example.com/tile.jpg"
        }
    },
    "BoardContainer": [
        {
            "mediaType": "image",
            "mediaUrl": "https://example.com/board1.jpg",
            "mediaTypeMobi": "image",
            "mediaUrlMobi": "https://example.com/board1-mobile.jpg",
            "isDelete": false,
            "isActive": true,
            "sortOrder": 1
        }
    ],
    "productModels": [
        {
            "title": "Model A",
            "description": "Test model description",
            "modelSKU": "SGI-001-006-A",
            "productModelImages": [
                {
                    "mediaType": "image",
                    "mediaUrl": "https://example.com/model-a.jpg",
                    "isDelete": false,
                    "isActive": true,
                    "sortOrder": 1
                }
            ],
            "downloads": {
                "specificationDownload": {
                    "fileName": "spec-a.pdf",
                    "isAvailable": true
                }
            },
            "itemCategory": "LED_STRIP",
            "isDeleted": false,
            "isActive": true,
            "sortOrder": 1
        }
    ],
    "downloads": {
        "specificationDownload": {
            "fileName": "family-spec.pdf",
            "isAvailable": true
        },
        "installationDownload": {
            "fileName": "installation.pdf",
            "isAvailable": true
        }
    },
    "similarProducts": [
        "GEN-001-030",
        "SGI-001-024",
        "SGI-001-021"
    ],
    "similarProductsVisible": true
};

async function testProductModelUpdates() {
    try {
        console.log('🧪 Testing Product Model Updates...');
        
        // Replace with your actual API endpoint
        const API_BASE_URL = 'http://localhost:3000/api'; // Adjust port as needed
        
        console.log('📤 Testing product creation with new fields...');
        console.log('New fields being tested:');
        console.log('  ✅ sortOrder (string)');
        console.log('  ✅ bannerSection.viewCatalogue');
        console.log('  ✅ productDetailFamilyRecordInternal.familyCode');
        console.log('  ✅ productDetailFamilyRecordInternal.efficacyType');
        console.log('  ✅ productDetailFamilyRecordInternal.associatedProducts');
        console.log('  ✅ productDetailFamilyRecordInternal.associatedProductsVisible');
        console.log('  ✅ similarProducts (array of strings)');
        console.log('  ✅ similarProductsVisible');
        
        const createResponse = await axios.post(
            `${API_BASE_URL}/products`,
            testProductData,
            {
                headers: {
                    'Content-Type': 'application/json',
                    // Add authentication header if needed
                    // 'Authorization': 'Bearer YOUR_TOKEN'
                },
                timeout: 30000
            }
        );

        console.log('\n✅ Product Creation Response:');
        console.log(`Status: ${createResponse.status}`);
        console.log(`Product ID: ${createResponse.data.data?._id}`);
        
        const createdProductId = createResponse.data.data?._id;
        
        if (createdProductId) {
            // Test getting the product details
            console.log('\n📋 Testing product detail retrieval...');
            const detailResponse = await axios.get(
                `${API_BASE_URL}/products/${createdProductId}`,
                {
                    headers: { 'Content-Type': 'application/json' },
                    timeout: 30000
                }
            );
            
            console.log('✅ Product Detail Response:');
            const productData = detailResponse.data.data;
            
            // Verify new fields
            console.log('\n🔍 Verifying new fields:');
            console.log(`  sortOrder: ${productData.sortOrder}`);
            console.log(`  viewCatalogue: ${JSON.stringify(productData.bannerSection?.viewCatalogue)}`);
            console.log(`  familyCode: ${productData.productDetailFamilyRecordInternal?.familyCode}`);
            console.log(`  efficacyType: ${productData.productDetailFamilyRecordInternal?.efficacyType}`);
            console.log(`  associatedProducts: ${JSON.stringify(productData.productDetailFamilyRecordInternal?.associatedProducts)}`);
            console.log(`  associatedProductsVisible: ${productData.productDetailFamilyRecordInternal?.associatedProductsVisible}`);
            console.log(`  similarProducts: ${JSON.stringify(productData.similarProducts)}`);
            console.log(`  similarProductsVisible: ${productData.similarProductsVisible}`);
            
            // Test updating the product
            console.log('\n🔄 Testing product update...');
            const updateData = {
                sortOrder: "PROD-001-UPDATED",
                similarProductsVisible: false,
                productDetailFamilyRecordInternal: {
                    ...productData.productDetailFamilyRecordInternal,
                    familyCode: "SGI-001-006-UPDATED",
                    associatedProductsVisible: false
                }
            };
            
            const updateResponse = await axios.put(
                `${API_BASE_URL}/products/${createdProductId}`,
                updateData,
                {
                    headers: { 'Content-Type': 'application/json' },
                    timeout: 30000
                }
            );
            
            console.log('✅ Product Update Response:');
            console.log(`Status: ${updateResponse.status}`);
            console.log('Updated fields verified successfully');
        }
        
        console.log('\n🎉 All Product Model tests passed!');
        
        console.log('\n📊 Test Summary:');
        console.log('✅ Product creation with new fields');
        console.log('✅ Product detail retrieval');
        console.log('✅ Product update with new fields');
        console.log('✅ Field validation and structure verification');
        
    } catch (error) {
        console.error('❌ Test failed:');
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', JSON.stringify(error.response.data, null, 2));
        } else if (error.request) {
            console.error('No response received:', error.message);
        } else {
            console.error('Error:', error.message);
        }
    }
}

// Run the test
testProductModelUpdates();

console.log(`
📋 Product Model Updates Test Script

This script tests the new fields added to the Product model:

🆕 New Fields Added:
1. sortOrder (string) - Top level field for product ordering
2. bannerSection.viewCatalogue - New section with label and visibility
3. productDetailFamilyRecordInternal.familyCode - Product family code
4. productDetailFamilyRecordInternal.efficacyType - Efficacy type (nullable)
5. productDetailFamilyRecordInternal.associatedProducts - Array of product codes
6. productDetailFamilyRecordInternal.associatedProductsVisible - Visibility flag
7. similarProducts - Changed from URL objects to array of strings
8. similarProductsVisible - Visibility flag for similar products

🔧 API Endpoints Tested:
- POST /products (create product with new fields)
- GET /products/:id (retrieve product details)
- PUT /products/:id (update product with new fields)

📝 Schema Changes:
- Updated Swagger documentation
- Added proper validation for array fields
- Maintained backward compatibility

Make sure:
1. Your API server is running
2. Update API_BASE_URL if needed
3. Add authentication headers if required
4. All new model fields are properly indexed
`);
