const express = require('express');
const router = express.Router();
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenAdmin } = require('../../util/auth');
const HomeController = require('./HomeController');

// router.put('/hero-section', verifyTokenAdmin, HomeController.addHeroSection);

router.post('/hero-section', verifyTokenAdmin, HomeController.addHeroSection);
router.put('/hero-section', verifyTokenAdmin, HomeController.updateHeroSection);
router.get('/hero-section', HomeController.viewHeroSection);

router.put('/hero-section-popup-menu', verifyTokenAdmin, HomeController.updateHeroSectionPopupMenu);
router.get('/hero-section-popup-menu', HomeController.viewHeroSectionPopupMenu);

router.put('/introducing', verifyTokenAdmin, HomeController.updateHomeIntroducting);
router.get('/introducing', HomeController.viewHomeIntroducting);

router.post('/home-inspiration', verifyTokenAdmin, HomeController.addOrUpdateHomeInspiration);
router.post('/home-inspiration-carousel', verifyTokenAdmin, HomeController.addHomeInspirationCarousel);
router.put('/home-inspiration-all-carousel/', verifyTokenAdmin, HomeController.updateHomeInspirationAllCarousel);
router.put('/home-inspiration-carousel/:carouselId', verifyTokenAdmin, HomeController.updateHomeInspirationCarousel);
router.get('/home-inspiration', HomeController.viewHomeInspiration);

router.post('/home-projects', verifyTokenAdmin, HomeController.addOrUpdateHomeProjects);
router.post('/home-projects-tiles', verifyTokenAdmin, HomeController.addHomeProjectsTiles);
router.put('/home-projects-all-tiles/', verifyTokenAdmin, HomeController.updateHomeProjectAllTiles);
router.put('/home-projects-tiles/:id', verifyTokenAdmin, HomeController.updateHomeProjectTiles);
router.get('/home-projects', HomeController.viewHomeProjects);

router.post('/whats-new', verifyTokenAdmin, HomeController.addWhatsNew);
router.put('/whats-new', HomeController.updateWhatsNew);
router.get('/whats-new', verifyTokenAdmin, HomeController.viewWhatsNew);

router.post('/our-commitments', verifyTokenAdmin, HomeController.addOurCommitments);
router.put('/our-commitments', verifyTokenAdmin, HomeController.updateOurCommitments);
router.get('/our-commitments', HomeController.viewOurCommitments);

router.post('/contact-us-banner', verifyTokenAdmin, HomeController.addContactUsBanner);
router.put('/contact-us-banner', verifyTokenAdmin, HomeController.updateContactUsBanner);
router.get('/contact-us-banner', HomeController.viewContactUsBanner);

router.put('/footer-content', verifyTokenAdmin, HomeController.updateFooterData);
router.get('/footer-content', HomeController.getFooterData);

module.exports = router;
