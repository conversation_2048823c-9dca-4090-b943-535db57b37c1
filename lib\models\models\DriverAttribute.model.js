const mongoose = require('mongoose');

// Beam <PERSON>le Attribute Schema
const DriverAttributeSchema = new mongoose.Schema({
    name: {
        type: String,
        default: '',
    },
    modelSlug: {
        type: String,
        default: 'drv',
    },
    isActive: {
        type: Boolean,
        default: true,
    },
    isDelete: {
        type: Boolean,
        default: false,
    },
});

module.exports = mongoose.model('DriverAttribute', DriverAttributeSchema);
