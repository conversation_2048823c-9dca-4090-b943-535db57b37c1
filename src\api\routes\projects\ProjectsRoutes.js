const express = require('express');
const router = express.Router();
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenAdmin } = require('../../util/auth');
const ProjectsController = require('./ProjectsController');

// get  inspiration banner 
router.get('/banner', ProjectsController.getBanner);
router.post('/',verifyTokenAdmin, ProjectsController.createProject);
router.get('/', ProjectsController.listProjects);
router.put('/:id',verifyTokenAdmin, ProjectsController.editProject);
router.delete('/:id', verifyTokenAdmin, ProjectsController.deleteProject);
// Add or Update inspiration banner by ID
router.post('/banner', ProjectsController.addOrUpdatebanner);


module.exports = router;
