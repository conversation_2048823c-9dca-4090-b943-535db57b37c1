const {
    models: {
        AntiGlareAttribute,
        BeamAngleAttribute,
        BendAttribute,
        ConnectionCableAttribute,
        DriverAttribute,
        FinishAttribute,
        LensAttribute,
        LightColourAttribute,
        SuspensionAttribute,
        TrimAttribute,
        WattsAttribute

    },
} = require('../../../../lib/models');

class AttributesController {
    /*************************************** ANTIGLARE ATTRIBUTE APIs START*************************************/

    // List AntiGlare Attribute
    async listAntiGlareAttribute(req, res) {
        const { id, isDelete, isActive } = req.query;

        try {
            let query = {};

            // If a specific filter ID is provided, fetch that filter
            if (id) {
                const filter = await AntiGlareAttribute.findById(id);
                if (!filter) {
                    return res.warn({}, req.__('Filter not found'));
                }
                return res.success(filter, req.__('Anti-glare filter fetched successfully'));
            }

            // Apply filters for isDelete and isActive if provided
            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            // Fetch all filters based on the query
            const filters = await AntiGlareAttribute.find(query);

            return res.success(filters, req.__('Anti-glare filters fetched successfully'));
        } catch (error) {
            console.error('Error listing anti-glare filters:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add AntiGlare Attribute
    async addAntiGlareAttribute(req, res) {
        const { name, modelSlug } = req.body;

        try {
            // Validate if the filter name exists in the request
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            // Check if the filter already exists in the database
            const existingFilter = await AntiGlareAttribute.findOne({ name, modelSlug });
            if (existingFilter) {
                return res.warn({}, req.__('Filter already exists'));
            }

            // Create a new anti-glare filter and save it to the database
            const newFilter = new AntiGlareAttribute({
                name,
                modelSlug
            });

            await newFilter.save();

            return res.success(newFilter, req.__('Anti-glare filter added successfully'));
        } catch (error) {
            console.error('Error adding anti-glare filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit AntiGlare Attribute
    async editAntiGlareAttribute(req, res) {
        const { id } = req.params;
        const { name, modelSlug, isActive } = req.body;

        try {
            // Validate if the filter ID and name are provided
            if (!id || !name || name.trim() === '') {
                return res.warn({}, req.__('Filter ID and name are required'));
            }

            // Check if the filter exists in the database
            const existingFilter = await AntiGlareAttribute.findById(id);
            if (!existingFilter) {
                return res.warn({}, req.__('Filter not found'));
            }

            // Update the filter details
            existingFilter.name = name;
            existingFilter.modelSlug = modelSlug;
            existingFilter.isActive = isActive;

            // Save the updated filter
            await existingFilter.save();

            return res.success(existingFilter, req.__('Anti-glare filter updated successfully'));
        } catch (error) {
            console.error('Error editing anti-glare filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete AntiGlare Attribute (Soft delete)
    async deleteAntiGlareAttribute(req, res) {
        const { id } = req.params;

        try {
            // Validate if the filter ID is provided
            if (!id) {
                return res.warn({}, req.__('Filter ID is required'));
            }

            // Check if the filter exists in the database
            const existingFilter = await AntiGlareAttribute.findById(id);
            if (!existingFilter) {
                return res.warn({}, req.__('Filter not found'));
            }

            // Mark the filter as deleted (soft delete)
            existingFilter.isDelete = true;

            // Save the updated filter
            await existingFilter.save();

            return res.success(existingFilter, req.__('Anti-glare filter deleted successfully'));
        } catch (error) {
            console.error('Error deleting anti-glare filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }




    /*************************************** ANTIGLARE ATTRIBUTE APIs END*************************************/


    /*************************************** BEAM ANGLE ATTRIBUTE APIs START*************************************/

    // List BeamAngle Attribute
    async listBeamAngleAttribute(req, res) {
        const { id, isDelete, isActive } = req.query;

        try {
            let query = {};

            // If a specific filter ID is provided, fetch that filter
            if (id) {
                const filter = await BeamAngleAttribute.findById(id);
                if (!filter) {
                    return res.warn({}, req.__('Filter not found'));
                }
                return res.success(filter, req.__('Beam angle filter fetched successfully'));
            }

            // Apply filters for isDelete and isActive if provided
            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            // Fetch all filters based on the query
            const filters = await BeamAngleAttribute.find(query);

            return res.success(filters, req.__('Beam angle filters fetched successfully'));
        } catch (error) {
            console.error('Error listing beam angle filters:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add BeamAngle Attribute
    async addBeamAngleAttribute(req, res) {
        const { name, modelSlug } = req.body;

        try {
            // Validate if the filter name exists in the request
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Filter name is required'));
            }

            // Check if the filter already exists in the database
            const existingFilter = await BeamAngleAttribute.findOne({ name, modelSlug });
            if (existingFilter) {
                return res.warn({}, req.__('Filter already exists'));
            }

            // Create a new beam angle filter and save it to the database
            const newFilter = new BeamAngleAttribute({
                name,
                modelSlug
            });

            await newFilter.save();

            return res.success(newFilter, req.__('Beam angle filter added successfully'));
        } catch (error) {
            console.error('Error adding beam angle filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit BeamAngle Attribute
    async editBeamAngleAttribute(req, res) {
        const { id } = req.params;
        const { name, modelSlug, isActive } = req.body;

        try {
            // Validate if the filter ID and name are provided
            if (!id || !name || name.trim() === '') {
                return res.warn({}, req.__('Filter ID and name are required'));
            }

            // Check if the filter exists in the database
            const existingFilter = await BeamAngleAttribute.findById(id);
            if (!existingFilter) {
                return res.warn({}, req.__('Filter not found'));
            }

            // Update the filter details
            existingFilter.name = name;
            existingFilter.modelSlug = modelSlug;
            existingFilter.isActive = isActive;

            // Save the updated filter
            await existingFilter.save();

            return res.success(existingFilter, req.__('Beam angle filter updated successfully'));
        } catch (error) {
            console.error('Error editing beam angle filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete BeamAngle Attribute (Soft delete)
    async deleteBeamAngleAttribute(req, res) {
        const { id } = req.params;

        try {
            // Validate if the filter ID is provided
            if (!id) {
                return res.warn({}, req.__('Filter ID is required'));
            }

            // Check if the filter exists in the database
            const existingFilter = await BeamAngleAttribute.findById(id);
            if (!existingFilter) {
                return res.warn({}, req.__('Filter not found'));
            }

            // Mark the filter as deleted (soft delete)
            existingFilter.isDelete = true;

            // Save the updated filter
            await existingFilter.save();

            return res.success(existingFilter, req.__('Beam angle filter deleted successfully'));
        } catch (error) {
            console.error('Error deleting beam angle filter:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /*************************************** BEAM ANGLE ATTRIBUTE APIs END*************************************/

    /*************************************** BEND ATTRIBUTE APIs START *************************************/

    // List Bend Attributes
    async listBendAttribute(req, res) {
        const { id, isDelete, isActive } = req.query;

        try {
            let query = {};

            // If a specific attribute ID is provided, fetch that attribute
            if (id) {
                const attribute = await BendAttribute.findById(id);
                if (!attribute) {
                    return res.warn({}, req.__('Attribute not found'));
                }
                return res.success(attribute, req.__('Bend attribute fetched successfully'));
            }

            // Apply filters for isDelete and isActive if provided
            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            // Fetch all attributes based on the query
            const attributes = await BendAttribute.find(query);

            return res.success(attributes, req.__('Bend attributes fetched successfully'));
        } catch (error) {
            console.error('Error listing bend attributes:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add Bend Attribute
    async addBendAttribute(req, res) {
        const { name, modelSlug } = req.body;

        try {
            // Validate if the attribute name exists in the request
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Attribute name is required'));
            }

            // Check if the attribute already exists in the database
            const existingAttribute = await BendAttribute.findOne({ name, modelSlug });
            if (existingAttribute) {
                return res.warn({}, req.__('Attribute already exists'));
            }

            // Create a new bend attribute and save it to the database
            const newAttribute = new BendAttribute({
                name,
                modelSlug
            });

            await newAttribute.save();

            return res.success(newAttribute, req.__('Bend attribute added successfully'));
        } catch (error) {
            console.error('Error adding bend attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Bend Attribute
    async editBendAttribute(req, res) {
        const { id } = req.params;
        const { name, modelSlug, isActive } = req.body;

        try {
            // Validate if the attribute ID and name are provided
            if (!id || !name || name.trim() === '') {
                return res.warn({}, req.__('Attribute ID and name are required'));
            }

            // Check if the attribute exists in the database
            const existingAttribute = await BendAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            // Update the attribute details
            existingAttribute.name = name;
            existingAttribute.modelSlug = modelSlug;
            existingAttribute.isActive = isActive;

            // Save the updated attribute
            await existingAttribute.save();

            return res.success(existingAttribute, req.__('Bend attribute updated successfully'));
        } catch (error) {
            console.error('Error editing bend attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Bend Attribute (Soft delete)
    async deleteBendAttribute(req, res) {
        const { id } = req.params;

        try {
            // Validate if the attribute ID is provided
            if (!id) {
                return res.warn({}, req.__('Attribute ID is required'));
            }

            // Check if the attribute exists in the database
            const existingAttribute = await BendAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            // Mark the attribute as deleted (soft delete)
            existingAttribute.isDelete = true;

            // Save the updated attribute
            await existingAttribute.save();

            return res.success(existingAttribute, req.__('Bend attribute deleted successfully'));
        } catch (error) {
            console.error('Error deleting bend attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /******************************* BEND ATTRIBUTE APIs END *************************************/
    /******************************* CONNECTION CABLE ATTRIBUTE APIs START *************************************/


    // List Connection Cable Attributes
    async listConnectionCableAttribute(req, res) {
        const { id, isDelete, isActive } = req.query;

        try {
            let query = {};

            if (id) {
                const attribute = await ConnectionCableAttribute.findById(id);
                if (!attribute) {
                    return res.warn({}, req.__('Attribute not found'));
                }
                return res.success(attribute, req.__('Connection cable attribute fetched successfully'));
            }

            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            const attributes = await ConnectionCableAttribute.find(query);
            return res.success(attributes, req.__('Connection cable attributes fetched successfully'));
        } catch (error) {
            console.error('Error listing connection cable attributes:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add Connection Cable Attribute
    async addConnectionCableAttribute(req, res) {
        const { name, modelSlug } = req.body;

        try {
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Attribute name is required'));
            }

            const existingAttribute = await ConnectionCableAttribute.findOne({ name, modelSlug });
            if (existingAttribute) {
                return res.warn({}, req.__('Attribute already exists'));
            }

            const newAttribute = new ConnectionCableAttribute({
                name,
                modelSlug,
            });

            await newAttribute.save();
            return res.success(newAttribute, req.__('Connection cable attribute added successfully'));
        } catch (error) {
            console.error('Error adding connection cable attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Connection Cable Attribute
    async editConnectionCableAttribute(req, res) {
        const { id } = req.params;
        const { name, modelSlug, isActive } = req.body;

        try {
            if (!id || !name || name.trim() === '') {
                return res.warn({}, req.__('Attribute ID and name are required'));
            }

            const existingAttribute = await ConnectionCableAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            existingAttribute.name = name;
            existingAttribute.modelSlug = modelSlug;
            existingAttribute.isActive = isActive;

            await existingAttribute.save();
            return res.success(existingAttribute, req.__('Connection cable attribute updated successfully'));
        } catch (error) {
            console.error('Error editing connection cable attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Connection Cable Attribute (soft delete)
    async deleteConnectionCableAttribute(req, res) {
        const { id } = req.params;

        try {
            if (!id) {
                return res.warn({}, req.__('Attribute ID is required'));
            }

            const existingAttribute = await ConnectionCableAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            existingAttribute.isDelete = true;
            await existingAttribute.save();

            return res.success(existingAttribute, req.__('Connection cable attribute deleted successfully'));
        } catch (error) {
            console.error('Error deleting connection cable attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    /******************************* CONNECTION CABLE ATTRIBUTE APIs END *************************************/

    /*************************************** DRIVER ATTRIBUTE APIs START *************************************/

    // List Driver Attributes
    async listDriverAttribute(req, res) {
        const { id, isDelete, isActive } = req.query;

        try {
            let query = {};

            // If a specific attribute ID is provided, fetch that attribute
            if (id) {
                const attribute = await DriverAttribute.findById(id);
                if (!attribute) {
                    return res.warn({}, req.__('Attribute not found'));
                }
                return res.success(attribute, req.__('Driver attribute fetched successfully'));
            }

            // Apply filters for isDelete and isActive if provided
            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            // Fetch all attributes based on the query
            const attributes = await DriverAttribute.find(query);

            return res.success(attributes, req.__('Driver attributes fetched successfully'));
        } catch (error) {
            console.error('Error listing driver attributes:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add Driver Attribute
    async addDriverAttribute(req, res) {
        const { name, modelSlug } = req.body;

        try {
            // Validate if the attribute name exists in the request
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Attribute name is required'));
            }

            // Check if the attribute already exists in the database
            const existingAttribute = await DriverAttribute.findOne({ name, modelSlug });
            if (existingAttribute) {
                return res.warn({}, req.__('Attribute already exists'));
            }

            // Create a new driver attribute and save it to the database
            const newAttribute = new DriverAttribute({
                name,
                modelSlug
            });

            await newAttribute.save();

            return res.success(newAttribute, req.__('Driver attribute added successfully'));
        } catch (error) {
            console.error('Error adding driver attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Driver Attribute
    async editDriverAttribute(req, res) {
        const { id } = req.params;
        const { name, modelSlug, isActive } = req.body;

        try {
            // Validate if the attribute ID and name are provided
            if (!id || !name || name.trim() === '') {
                return res.warn({}, req.__('Attribute ID and name are required'));
            }

            // Check if the attribute exists in the database
            const existingAttribute = await DriverAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            // Update the attribute details
            existingAttribute.name = name;
            existingAttribute.modelSlug = modelSlug;
            existingAttribute.isActive = isActive;

            // Save the updated attribute
            await existingAttribute.save();

            return res.success(existingAttribute, req.__('Driver attribute updated successfully'));
        } catch (error) {
            console.error('Error editing driver attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Driver Attribute (Soft delete)
    async deleteDriverAttribute(req, res) {
        const { id } = req.params;

        try {
            // Validate if the attribute ID is provided
            if (!id) {
                return res.warn({}, req.__('Attribute ID is required'));
            }

            // Check if the attribute exists in the database
            const existingAttribute = await DriverAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            // Mark the attribute as deleted (soft delete)
            existingAttribute.isDelete = true;

            // Save the updated attribute
            await existingAttribute.save();

            return res.success(existingAttribute, req.__('Driver attribute deleted successfully'));
        } catch (error) {
            console.error('Error deleting driver attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /******************************* DRIVER ATTRIBUTE APIs END *************************************/

    /*********************************** FINISH ATTRIBUTE APIs START *************************************/

    // List Finish Attributes
    async listFinishAttribute(req, res) {
        const { id, isDelete, isActive } = req.query;

        try {
            let query = {};

            // If a specific attribute ID is provided, fetch that attribute
            if (id) {
                const attribute = await FinishAttribute.findById(id);
                if (!attribute) {
                    return res.warn({}, req.__('Attribute not found'));
                }
                return res.success(attribute, req.__('Finish attribute fetched successfully'));
            }

            // Apply filters for isDelete and isActive if provided
            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            // Fetch all attributes based on the query
            const attributes = await FinishAttribute.find(query);

            return res.success(attributes, req.__('Finish attributes fetched successfully'));
        } catch (error) {
            console.error('Error listing finish attributes:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add Finish Attribute
    async addFinishAttribute(req, res) {
        const { name, modelSlug } = req.body;

        try {
            // Validate if the attribute name exists in the request
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Attribute name is required'));
            }

            // Check if the attribute already exists in the database
            const existingAttribute = await FinishAttribute.findOne({ name, modelSlug });
            if (existingAttribute) {
                return res.warn({}, req.__('Attribute already exists'));
            }

            // Create a new finish attribute and save it to the database
            const newAttribute = new FinishAttribute({
                name,
                modelSlug
            });

            await newAttribute.save();

            return res.success(newAttribute, req.__('Finish attribute added successfully'));
        } catch (error) {
            console.error('Error adding finish attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Finish Attribute
    async editFinishAttribute(req, res) {
        const { id } = req.params;
        const { name, modelSlug, isActive } = req.body;

        try {
            // Validate if the attribute ID and name are provided
            if (!id || !name || name.trim() === '') {
                return res.warn({}, req.__('Attribute ID and name are required'));
            }

            // Check if the attribute exists in the database
            const existingAttribute = await FinishAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            // Update the attribute details
            existingAttribute.name = name;
            existingAttribute.modelSlug = modelSlug;
            existingAttribute.isActive = isActive;

            // Save the updated attribute
            await existingAttribute.save();

            return res.success(existingAttribute, req.__('Finish attribute updated successfully'));
        } catch (error) {
            console.error('Error editing finish attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Finish Attribute (Soft delete)
    async deleteFinishAttribute(req, res) {
        const { id } = req.params;

        try {
            // Validate if the attribute ID is provided
            if (!id) {
                return res.warn({}, req.__('Attribute ID is required'));
            }

            // Check if the attribute exists in the database
            const existingAttribute = await FinishAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            // Mark the attribute as deleted (soft delete)
            existingAttribute.isDelete = true;

            // Save the updated attribute
            await existingAttribute.save();

            return res.success(existingAttribute, req.__('Finish attribute deleted successfully'));
        } catch (error) {
            console.error('Error deleting finish attribute:', error);
            return res.serverError(error, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /******************************* FINISH ATTRIBUTE APIs END *************************************/


    /*************************************** LENS ATTRIBUTE APIs START *************************************/

    // List Lens Attributes
    async listLensAttribute(req, res) {
        const { id, isDelete, isActive } = req.query;

        try {
            let query = {};

            // If a specific attribute ID is provided, fetch that attribute
            if (id) {
                const attribute = await LensAttribute.findById(id);
                if (!attribute) {
                    return res.warn({}, req.__('Attribute not found'));
                }
                return res.success(attribute, req.__('Lens attribute fetched successfully'));
            }

            // Apply filters for isDelete and isActive if provided
            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            // Fetch all attributes based on the query
            const attributes = await LensAttribute.find(query);

            return res.success(attributes, req.__('Lens attributes fetched successfully'));
        } catch (error) {
            console.error('Error listing lens attributes:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add Lens Attribute
    async addLensAttribute(req, res) {
        const { name, modelSlug } = req.body;

        try {
            // Validate if the attribute name exists in the request
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Attribute name is required'));
            }

            // Check if the attribute already exists in the database
            const existingAttribute = await LensAttribute.findOne({ name, modelSlug });
            if (existingAttribute) {
                return res.warn({}, req.__('Attribute already exists'));
            }

            // Create a new lens attribute and save it to the database
            const newAttribute = new LensAttribute({
                name,
                modelSlug
            });

            await newAttribute.save();

            return res.success(newAttribute, req.__('Lens attribute added successfully'));
        } catch (error) {
            console.error('Error adding lens attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Lens Attribute
    async editLensAttribute(req, res) {
        const { id } = req.params;
        const { name, modelSlug, isActive } = req.body;

        try {
            // Validate if the attribute ID and name are provided
            if (!id || !name || name.trim() === '') {
                return res.warn({}, req.__('Attribute ID and name are required'));
            }

            // Check if the attribute exists in the database
            const existingAttribute = await LensAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            // Update the attribute details
            existingAttribute.name = name;
            existingAttribute.modelSlug = modelSlug;
            existingAttribute.isActive = isActive;

            // Save the updated attribute
            await existingAttribute.save();

            return res.success(existingAttribute, req.__('Lens attribute updated successfully'));
        } catch (error) {
            console.error('Error editing lens attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Lens Attribute (Soft delete)
    async deleteLensAttribute(req, res) {
        const { id } = req.params;

        try {
            // Validate if the attribute ID is provided
            if (!id) {
                return res.warn({}, req.__('Attribute ID is required'));
            }

            // Check if the attribute exists in the database
            const existingAttribute = await LensAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            // Mark the attribute as deleted (soft delete)
            existingAttribute.isDelete = true;

            // Save the updated attribute
            await existingAttribute.save();

            return res.success(existingAttribute, req.__('Lens attribute deleted successfully'));
        } catch (error) {
            console.error('Error deleting lens attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /******************************* LENS ATTRIBUTE APIs END *************************************/

    /*************************************** LIGHT COLOUR ATTRIBUTE APIs START *************************************/

    // List Light Colour Attributes
    async listLightColourAttribute(req, res) {
        const { id, isDelete, isActive } = req.query;

        try {
            let query = {};

            // If a specific attribute ID is provided, fetch that attribute
            if (id) {
                const attribute = await LightColourAttribute.findById(id);
                if (!attribute) {
                    return res.warn({}, req.__('Attribute not found'));
                }
                return res.success(attribute, req.__('Light colour attribute fetched successfully'));
            }

            // Apply filters for isDelete and isActive if provided
            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            // Fetch all attributes based on the query
            const attributes = await LightColourAttribute.find(query);

            return res.success(attributes, req.__('Light colour attributes fetched successfully'));
        } catch (error) {
            console.error('Error listing light colour attributes:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add Light Colour Attribute
    async addLightColourAttribute(req, res) {
        const { name, modelSlug } = req.body;

        try {
            // Validate if the attribute name exists in the request
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Attribute name is required'));
            }

            // Check if the attribute already exists in the database
            const existingAttribute = await LightColourAttribute.findOne({ name, modelSlug });
            if (existingAttribute) {
                return res.warn({}, req.__('Attribute already exists'));
            }

            // Create a new light colour attribute and save it to the database
            const newAttribute = new LightColourAttribute({
                name,
                modelSlug
            });

            await newAttribute.save();

            return res.success(newAttribute, req.__('Light colour attribute added successfully'));
        } catch (error) {
            console.error('Error adding light colour attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Light Colour Attribute
    async editLightColourAttribute(req, res) {
        const { id } = req.params;
        const { name, modelSlug, isActive } = req.body;

        try {
            // Validate if the attribute ID and name are provided
            if (!id || !name || name.trim() === '') {
                return res.warn({}, req.__('Attribute ID and name are required'));
            }

            // Check if the attribute exists in the database
            const existingAttribute = await LightColourAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            // Update the attribute details
            existingAttribute.name = name;
            existingAttribute.modelSlug = modelSlug;
            existingAttribute.isActive = isActive;

            // Save the updated attribute
            await existingAttribute.save();

            return res.success(existingAttribute, req.__('Light colour attribute updated successfully'));
        } catch (error) {
            console.error('Error editing light colour attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Light Colour Attribute (Soft delete)
    async deleteLightColourAttribute(req, res) {
        const { id } = req.params;

        try {
            // Validate if the attribute ID is provided
            if (!id) {
                return res.warn({}, req.__('Attribute ID is required'));
            }

            // Check if the attribute exists in the database
            const existingAttribute = await LightColourAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            // Mark the attribute as deleted (soft delete)
            existingAttribute.isDelete = true;

            // Save the updated attribute
            await existingAttribute.save();

            return res.success(existingAttribute, req.__('Light colour attribute deleted successfully'));
        } catch (error) {
            console.error('Error deleting light colour attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /******************************* LIGHT COLOUR ATTRIBUTE APIs END *************************************/

    /*************************************** SUSPENSION ATTRIBUTE APIs START *************************************/

    // List Suspension Attributes
    async listSuspensionAttribute(req, res) {
        const { id, isDelete, isActive } = req.query;

        try {
            let query = {};

            // If a specific attribute ID is provided, fetch that attribute
            if (id) {
                const attribute = await SuspensionAttribute.findById(id);
                if (!attribute) {
                    return res.warn({}, req.__('Attribute not found'));
                }
                return res.success(attribute, req.__('Suspension attribute fetched successfully'));
            }

            // Apply filters for isDelete and isActive if provided
            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            // Fetch all attributes based on the query
            const attributes = await SuspensionAttribute.find(query);

            return res.success(attributes, req.__('Suspension attributes fetched successfully'));
        } catch (error) {
            console.error('Error listing suspension attributes:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add Suspension Attribute
    async addSuspensionAttribute(req, res) {
        const { name, modelSlug } = req.body;

        try {
            // Validate if the attribute name exists in the request
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Attribute name is required'));
            }

            // Check if the attribute already exists in the database
            const existingAttribute = await SuspensionAttribute.findOne({ name, modelSlug });
            if (existingAttribute) {
                return res.warn({}, req.__('Attribute already exists'));
            }

            // Create a new suspension attribute and save it to the database
            const newAttribute = new SuspensionAttribute({
                name,
                modelSlug
            });

            await newAttribute.save();

            return res.success(newAttribute, req.__('Suspension attribute added successfully'));
        } catch (error) {
            console.error('Error adding suspension attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Suspension Attribute
    async editSuspensionAttribute(req, res) {
        const { id } = req.params;
        const { name, modelSlug, isActive } = req.body;

        try {
            // Validate if the attribute ID and name are provided
            if (!id || !name || name.trim() === '') {
                return res.warn({}, req.__('Attribute ID and name are required'));
            }

            // Check if the attribute exists in the database
            const existingAttribute = await SuspensionAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            // Update the attribute details
            existingAttribute.name = name;
            existingAttribute.modelSlug = modelSlug;
            existingAttribute.isActive = isActive;

            // Save the updated attribute
            await existingAttribute.save();

            return res.success(existingAttribute, req.__('Suspension attribute updated successfully'));
        } catch (error) {
            console.error('Error editing suspension attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Suspension Attribute (Soft delete)
    async deleteSuspensionAttribute(req, res) {
        const { id } = req.params;

        try {
            // Validate if the attribute ID is provided
            if (!id) {
                return res.warn({}, req.__('Attribute ID is required'));
            }

            // Check if the attribute exists in the database
            const existingAttribute = await SuspensionAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            // Mark the attribute as deleted (soft delete)
            existingAttribute.isDelete = true;

            // Save the updated attribute
            await existingAttribute.save();

            return res.success(existingAttribute, req.__('Suspension attribute deleted successfully'));
        } catch (error) {
            console.error('Error deleting suspension attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /******************************* SUSPENSION ATTRIBUTE APIs END *************************************/

    /*************************************** TRIM ATTRIBUTE APIs START *************************************/

    // List Trim Attributes
    async listTrimAttribute(req, res) {
        const { id, isDelete, isActive } = req.query;

        try {
            let query = {};

            // If a specific attribute ID is provided, fetch that attribute
            if (id) {
                const attribute = await TrimAttribute.findById(id);
                if (!attribute) {
                    return res.warn({}, req.__('Attribute not found'));
                }
                return res.success(attribute, req.__('Trim attribute fetched successfully'));
            }

            // Apply filters for isDelete and isActive if provided
            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            // Fetch all attributes based on the query
            const attributes = await TrimAttribute.find(query);

            return res.success(attributes, req.__('Trim attributes fetched successfully'));
        } catch (error) {
            console.error('Error listing trim attributes:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add Trim Attribute
    async addTrimAttribute(req, res) {
        const { name, modelSlug } = req.body;

        try {
            // Validate if the attribute name exists in the request
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Attribute name is required'));
            }

            // Check if the attribute already exists in the database
            const existingAttribute = await TrimAttribute.findOne({ name, modelSlug });
            if (existingAttribute) {
                return res.warn({}, req.__('Attribute already exists'));
            }

            // Create a new trim attribute and save it to the database
            const newAttribute = new TrimAttribute({
                name,
                modelSlug
            });

            await newAttribute.save();

            return res.success(newAttribute, req.__('Trim attribute added successfully'));
        } catch (error) {
            console.error('Error adding trim attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Trim Attribute
    async editTrimAttribute(req, res) {
        const { id } = req.params;
        const { name, modelSlug, isActive } = req.body;

        try {
            // Validate if the attribute ID and name are provided
            if (!id || !name || name.trim() === '') {
                return res.warn({}, req.__('Attribute ID and name are required'));
            }

            // Check if the attribute exists in the database
            const existingAttribute = await TrimAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            // Update the attribute details
            existingAttribute.name = name;
            existingAttribute.modelSlug = modelSlug;
            existingAttribute.isActive = isActive;

            // Save the updated attribute
            await existingAttribute.save();

            return res.success(existingAttribute, req.__('Trim attribute updated successfully'));
        } catch (error) {
            console.error('Error editing trim attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Trim Attribute (Soft delete)
    async deleteTrimAttribute(req, res) {
        const { id } = req.params;

        try {
            // Validate if the attribute ID is provided
            if (!id) {
                return res.warn({}, req.__('Attribute ID is required'));
            }

            // Check if the attribute exists in the database
            const existingAttribute = await TrimAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            // Mark the attribute as deleted (soft delete)
            existingAttribute.isDelete = true;

            // Save the updated attribute
            await existingAttribute.save();

            return res.success(existingAttribute, req.__('Trim attribute deleted successfully'));
        } catch (error) {
            console.error('Error deleting trim attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /******************************* TRIM ATTRIBUTE APIs END *************************************/

    /*************************************** WATTS ATTRIBUTE APIs START *************************************/

    // List Watts Attributes
    async listWattsAttribute(req, res) {
        const { id, isDelete, isActive } = req.query;

        try {
            let query = {};

            // If a specific attribute ID is provided, fetch that attribute
            if (id) {
                const attribute = await WattsAttribute.findById(id);
                if (!attribute) {
                    return res.warn({}, req.__('Attribute not found'));
                }
                return res.success(attribute, req.__('Watts attribute fetched successfully'));
            }

            // Apply filters for isDelete and isActive if provided
            if (isDelete !== undefined) {
                query.isDelete = isDelete === 'true';
            }

            if (isActive !== undefined) {
                query.isActive = isActive === 'true';
            }

            // Fetch all attributes based on the query
            const attributes = await WattsAttribute.find(query);

            return res.success(attributes, req.__('Watts attributes fetched successfully'));
        } catch (error) {
            console.error('Error listing watts attributes:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Add Watts Attribute
    async addWattsAttribute(req, res) {
        const { name, modelSlug } = req.body;

        try {
            // Validate if the attribute name exists in the request
            if (!name || name.trim() === '') {
                return res.warn({}, req.__('Attribute name is required'));
            }

            // Check if the attribute already exists in the database
            const existingAttribute = await WattsAttribute.findOne({ name, modelSlug });
            if (existingAttribute) {
                return res.warn({}, req.__('Attribute already exists'));
            }

            // Create a new watts attribute and save it to the database
            const newAttribute = new WattsAttribute({
                name,
                modelSlug
            });

            await newAttribute.save();

            return res.success(newAttribute, req.__('Watts attribute added successfully'));
        } catch (error) {
            console.error('Error adding watts attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Edit Watts Attribute
    async editWattsAttribute(req, res) {
        const { id } = req.params;
        const { name, modelSlug, isActive } = req.body;

        try {
            // Validate if the attribute ID and name are provided
            if (!id || !name || name.trim() === '') {
                return res.warn({}, req.__('Attribute ID and name are required'));
            }

            // Check if the attribute exists in the database
            const existingAttribute = await WattsAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            // Update the attribute details
            existingAttribute.name = name;
            existingAttribute.modelSlug = modelSlug;
            existingAttribute.isActive = isActive;

            // Save the updated attribute
            await existingAttribute.save();

            return res.success(existingAttribute, req.__('Watts attribute updated successfully'));
        } catch (error) {
            console.error('Error editing watts attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Delete Watts Attribute (Soft delete)
    async deleteWattsAttribute(req, res) {
        const { id } = req.params;

        try {
            // Validate if the attribute ID is provided
            if (!id) {
                return res.warn({}, req.__('Attribute ID is required'));
            }

            // Check if the attribute exists in the database
            const existingAttribute = await WattsAttribute.findById(id);
            if (!existingAttribute) {
                return res.warn({}, req.__('Attribute not found'));
            }

            // Mark the attribute as deleted (soft delete)
            existingAttribute.isDelete = true;

            // Save the updated attribute
            await existingAttribute.save();

            return res.success(existingAttribute, req.__('Watts attribute deleted successfully'));
        } catch (error) {
            console.error('Error deleting watts attribute:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /******************************* WATTS ATTRIBUTE APIs END *************************************/
}
module.exports = new AttributesController();
